/**
 * 根据类型获取日期范围
 * @param {string} type - 日期范围类型：'all'|'work'|'24h'|'7d'|'custom'
 * @returns {Array} 返回日期范围数组 [startDate, endDate]
 */
export function getDateRangeByType(type) {
  const now = new Date()
  const formatDate = (date) => {
    const pad = (n) => (n < 10 ? '0' + n : n)
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(
      date.getDate()
    )} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(
      date.getSeconds()
    )}`
  }

  switch (type) {
    case 'all':
    case 'custom':
      return []
    case 'work': {
      const start = new Date(now)
      start.setDate(now.getDate() - 1)
      start.setHours(18, 0, 0, 0)
      return [formatDate(start), formatDate(now)]
    }
    case '24h': {
      const start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      return [formatDate(start), formatDate(now)]
    }
    case '7d': {
      const start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      return [formatDate(start), formatDate(now)]
    }
    case 'today': {
      const start = new Date(now)
      start.setHours(0, 0, 0, 0)
      return [formatDate(start), formatDate(now)]
    }
    default:
      return []
  }
}


/**
 * [工具函数] 安全地高亮富文本中的多组关键词，支持不同样式。
 * @param {string} htmlString - 包含HTML的原始字符串。
 * @param {Array<object>} wordsToHighlight - 需要高亮的词对象数组，格式: [{word: '关键词', className: 'css-class-name'}]
 * @returns {string} - 处理过的高亮HTML字符串。
 */
export function highlightRichTextMultiple(htmlString, wordsToHighlight) {
  if (!htmlString || !wordsToHighlight || wordsToHighlight.length === 0) {
    return htmlString;
  }

  // 1. 过滤无效词，并按词长倒序排序，确保长词优先匹配
  const sortedWords = wordsToHighlight
    .filter(item => item && item.word)
    .sort((a, b) => b.word.length - a.word.length);

  if (sortedWords.length === 0) {
    return htmlString;
  }

  // 创建一个 词 -> 类名 的映射，方便后续查找
  const classMap = {};
  sortedWords.forEach(item => {
    classMap[item.word] = item.className;
  });

  // 2. 构建一个联合正则表达式
  const pattern = sortedWords.map(item => item.word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
  const masterRegex = new RegExp(`(${pattern})`, 'g');

  // 3. 创建临时DOM容器并执行DOM操作
  const container = document.createElement('div');
  container.innerHTML = htmlString;

  function walkAndHighlight(node) {
    const ignoreTags = ['SCRIPT', 'STYLE', 'TEXTAREA', 'INPUT', 'SPAN']; // 增加SPAN，防止在高亮标签内再次高亮
    if (node.nodeType === Node.ELEMENT_NODE && ignoreTags.includes(node.tagName)) {
        if (node.tagName !== 'SPAN' || !(node.classList.contains('hitword-highlight') || node.classList.contains('sensitive-highlight'))) {
          // 如果不是高亮span，则继续递归
           Array.from(node.childNodes).forEach(child => walkAndHighlight(child));
        }
        return; // 跳过脚本、样式和已高亮的span
    }

    // 只处理文本节点
    if (node.nodeType === Node.TEXT_NODE) {
      const textContent = node.textContent;
      // 如果文本内容中没有可能匹配的词，则跳过，轻微优化
      if (!masterRegex.test(textContent)) return;
      
      // 重置正则的lastIndex，以确保从头开始匹配
      masterRegex.lastIndex = 0;

      const fragment = document.createDocumentFragment();
      let lastIndex = 0;
      let match;

      while ((match = masterRegex.exec(textContent)) !== null) {
        const matchedWord = match[0]; // match[0] 是完整匹配的字符串
        const offset = match.index;

        // 添加匹配项之前的部分
        if (offset > lastIndex) {
          fragment.appendChild(document.createTextNode(textContent.substring(lastIndex, offset)));
        }

        // 创建并添加高亮span
        const highlightSpan = document.createElement('span');
        highlightSpan.className = classMap[matchedWord]; // 从map中获取正确的类名
        highlightSpan.textContent = matchedWord;
        fragment.appendChild(highlightSpan);

        lastIndex = offset + matchedWord.length;
      }
      
      // 如果替换发生过（fragment中有内容）
      if (lastIndex > 0) {
        // 添加最后一个匹配项之后的部分
        if (lastIndex < textContent.length) {
          fragment.appendChild(document.createTextNode(textContent.substring(lastIndex)));
        }
        // 用新创建的节点片段替换旧的文本节点
        node.parentNode.replaceChild(fragment, node);
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 对其他元素节点，递归其子节点
      // 使用 Array.from 复制一份，防止因DOM修改导致的NodeList实时变化问题
      Array.from(node.childNodes).forEach(child => walkAndHighlight(child));
    }
  }

  walkAndHighlight(container);

  return container.innerHTML;
}


/**
 * [Web/Browser环境工具函数] 从富文本字符串中安全地移除所有的<img>标签。
 * 此函数无需任何外部库，直接在浏览器中运行。
 *
 * @param {string} htmlString - 包含HTML的原始字符串。
 * @returns {string} - 移除了<img>标签后的HTML字符串。
 */
export function removeImgTagsFromRichText(htmlString) {
  // 基本的输入校验
  if (!htmlString || typeof htmlString !== 'string') {
    return ''; // 或根据需求返回 htmlString
  }

  // 1. 在内存中创建一个临时的DOM元素作为容器，它不会被渲染到页面上
  const container = document.createElement('div');

  // 2. 将HTML字符串赋值给容器的innerHTML，浏览器会立即将其解析成一个DOM子树
  container.innerHTML = htmlString;

  // 3. 在这个临时的DOM子树中，查找所有的<img>标签
  // querySelectorAll 返回一个静态的 NodeList
  const images = container.querySelectorAll('img');

  // 4. 遍历这个列表，并从DOM树中移除每一个<img>元素
  // .remove() 是标准的DOM API，用于移除节点自身
  images.forEach(img => {
    img.remove();
  });

  // 5. 返回容器处理后的innerHTML，这就是我们想要的、清除了图片的HTML字符串
  return container.innerHTML;
}