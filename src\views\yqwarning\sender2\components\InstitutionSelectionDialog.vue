<template>
  <el-dialog
    title="选择机构"
    :visible.sync="dialogVisible"
    width="50%"
    :show-close="false"
  >
    <div slot="title" class="dialog-header-actions">
      <span class="dialog-title">选择机构</span>
      <div class="dialog-buttons">
        <el-button size="small" @click="handleCancel">取 消</el-button>
        <el-button type="primary" size="small" @click="handleConfirm">确 定</el-button>
      </div>
    </div>
    <el-input
      v-model="searchText"
      placeholder="输入机构名称进行搜索"
      clearable
      style="margin-bottom: 15px"
    />
    <el-tree
      ref="institutionTree"
      :data="treeData"
      show-checkbox
      node-key="id"
      :check-strictly="true"
      :props="{ label: 'label', children: 'children' }"
      :default-checked-keys="initialSelectedIds"
      :filter-node-method="filterTreeNode"
      @check="handleTreeCheck"
    />
  </el-dialog>
</template>

<script>
export default {
  name: 'InstitutionSelectionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: () => []
    },
    initialSelectedIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchText: '',
      tempSelectedNodes: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    searchText(val) {
      if (this.$refs.institutionTree) {
        this.$refs.institutionTree.filter(val)
      }
    },
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时，重置临时选择状态
        this.tempSelectedNodes = []
        this.$nextTick(() => {
          if (this.$refs.institutionTree) {
            this.$refs.institutionTree.setCheckedKeys(this.initialSelectedIds)
          }
        })
      }
    }
  },
  methods: {
    filterTreeNode(value, data) {
      if (!value) return true
      return data.label.toLowerCase().includes(value.toLowerCase())
    },
    handleTreeCheck(clickedNode, checkedStatus) {
      this.tempSelectedNodes = checkedStatus.checkedNodes.map(
        (node) => ({
          id: node.id,
          label: node.label,
          originalData: node.originalData
        })
      )
    },
    handleConfirm() {
      this.$emit('confirm', this.tempSelectedNodes)
      this.dialogVisible = false
    },
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

::v-deep .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}
</style>
