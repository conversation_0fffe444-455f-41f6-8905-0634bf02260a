<template>
  <div class="report-detail-page">
    <!-- 标题 -->
    <div class="page-header">
      <div class="header-container">
        <h1 class="page-title">舆情早晚报</h1>
      </div>
    </div>
    
    <div class="container">
      <div class="content">
        <!-- 上：显示机构、时间 -->
        <div class="content-upper">
          <div v-if="institutions && institutions.length > 0" class="meta-line">
            机构：{{ institutions.join('、') }}
          </div>
          <div class="meta-line">
            <span>{{ sendTime }}</span>
          </div>
        </div>

        <!-- 下：具体的内容 -->
        <div class="content-lower">
          <div class="analysis-box">
            <!-- <p>{{ reportContent }}</p> -->
            <p v-html="reportContent"></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSmsMsgInfoByMsgId } from '@/api/h5'

export default {
  name: 'ReportIndex',
  data() {
    return {
      reportContent: '',
      sendTime: '',
      institutions: []
    }
  },
  created() {
    this.fetchReportDetail()
  },
  methods: {
    fetchReportDetail() {
      const msgId = this.$route.params.id
      if (!msgId) {
        this.$message.error('无效的消息ID')
        return
      }
      getSmsMsgInfoByMsgId(msgId).then(response => {
        if (response.code === 0 && response.data) {
          this.sendTime = response.data.sendTime || ''
          this.institutions = response.data.institutions || []

          const content = response.data.content || '无内容'
          const linkRegex = /\n\n?点击查看更多：.*/
          this.reportContent = this.wrapLlink(content.replace(linkRegex, '').trim())
        } else {
          this.$message.error(response.msg || '获取报告详情失败')
          this.reportContent = '请稍后重试。'
        }
      }).catch(err => {
        console.error('获取报告详情失败:', err)
        this.$message.error('请求失败，请检查网络连接')
        this.reportContent = '请稍后重试。'
      })
    },
      /**
     * 将文本中的url包裹成超链接
     * @param content 
     */
     wrapLlink(content) {
      if (!content) return ''
      let htmlContent = content;

      // 1. 转义 HTML 特殊字符，防止 XSS
      htmlContent = htmlContent
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

      // 2. 将换行符 \n 转换为 <br> 标签
      htmlContent = htmlContent.replace(/\n/g, '<br>');

      // 3. 使用正则表达式查找 URL 并替换为 <a> 标签
        const urlRegex = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi
      htmlContent = htmlContent.replace(urlRegex, function(url) {
        // url 是原始匹配，此时它不包含任何 '<' 或 '>' 字符，可以安全地放入 href
        return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`
      })

      return htmlContent
    }
  }
}
</script>

<style scoped lang="scss">
.report-detail-page {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #1e5799, #207cca);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  .header-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
    display: flex;
    justify-content: center;
    align-items: center;

    .page-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      text-align: center;
    }
  }
}

.container {
  flex-grow: 1;
  width: 95%;
  max-width: 900px;
  margin: 15px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 20px;
  overflow: hidden;
}

.content {
  margin-top: 10px;
}

.content-upper {
  padding-bottom: 15px;

  .meta-line {
    font-size: 13px;
    color: #909399;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.content-lower {
  border-top: 1px solid #ebeef5;

  .section-title {
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    margin: 10px 0 15px 0;
    padding-left: 12px;
    border-left: 4px solid #1e5799;
  }

  .analysis-box {
    background: #f0f7ff;
    border-radius: 12px;
    padding: 18px;
    border-left: 4px solid #1e5799;

    p {
      font-size: 15px;
      color: #333;
      line-height: 1.7;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

@media (max-width: 480px) {
  .container {
    width: 100%;
    margin: 0;
    padding: 15px;
    border-radius: 0;
    box-shadow: none;
  }

  .content-lower .section-title {
    font-size: 16px;
  }

  .content-lower .analysis-box p {
    font-size: 14px;
  }

  .page-header .header-container {
    padding: 0 10px;

    .page-title {
      font-size: 16px;
    }
  }
}

@media (max-width: 375px) {
  .page-header .header-container .page-title {
    font-size: 15px;
  }
}
</style>
