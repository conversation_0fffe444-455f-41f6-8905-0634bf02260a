<template>
  <div class="searchall-container">
    <!-- 顶部搜索操作栏 -->
    <div class="search-bar">
      <!-- 时间筛选框 -->
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="—"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
        format="yyyy-MM-dd HH:mm:ss"
        style="width: 230px;"
      />
      <!-- 搜索域+输入+icon整体包裹 -->
      <div class="search-fields-group">
        <el-select
          v-model="searchFields"
          multiple
          placeholder=""
          style="width: 120px;"
          @change="handleSearchFieldsChange"
        >
          <el-option
            v-for="item in searchFieldOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="searchText"
          placeholder="请输入搜索内容，多个词请用空格分隔"
          style="width: 280px;"
          @keyup.enter.native="handleSearch"
        />
        <i class="el-icon-search search-icon" @click="handleSearch" />
      </div>
    </div>

    <!-- 内容列表 -->
    <div v-loading="loading" class="maincontent">
      <ContentList
        :checked-visible="false"
        :list="contentList"
        :page-num="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :total="total"
        :checked-list="checkedList"
        :sentiment-options="sentimentOptions"
        :ai-sentiment-options="aiSentimentOptions"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @check-change="handleCheckChange"
        @delete="handleSingleDelete"
        @change-sentiment="handleChangeSingleSentiment"
        @change-ai-sentiment="handleChangeSingleAiSentimentType"
        @mark-read="handleSingleMarkRead"
      />
    </div>
  </div>
</template>

<script>
import { getPublicSentimentList, batchUpdatePublicSentiment, batchUpdateSentiment, batchUpdateAiSentimentType, batchDeletePublicSentiment } from '@/api/yqmonitor'
import ContentList from './components/MainContent/ContentList.vue'

export default {
  name: 'SearchAll',
  components: {
    ContentList
  },
  dicts: [
    'content_publish_type',
    'ai_public_opinion_type',
    'negative_public_opinion_tag',
    'filter_sentiment',
    'search_field'
  ],
  data() {
    return {
      dateRange: [], // 时间范围
      searchText: '', // 搜索文本
      searchFields: ['all'],
      contentList: [], // 内容列表
      total: 0, // 总数
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        searchText: '',
        searchFields: [],
        startTime: '',
        endTime: '',
        isAsc: 'desc',
        orderByColumn: ''
      },
      checkedList: [], // 选中的列表
      sentimentOptions: [], // 倾向性选项
      aiSentimentOptions: [] // AI舆情类型选项
    }
  },
  computed: {
    searchFieldOptions() {
      return this.dict.type.search_field || []
    }
  },
  watch: {
    dateRange(val) {
      if (Array.isArray(val) && val.length === 2) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    }
  },
  mounted() {
    this.sentimentOptions = this.dict.type.filter_sentiment || []
    this.aiSentimentOptions = this.dict.type.ai_public_opinion_type || []
    this.getContentList()
  },
  beforeDestroy() {
    // 清除 store 中的 searchText
    this.$store.commit('yqmonitorMenu/SET_SEARCH_TEXT', '')
  },
  methods: {
    // 获取内容列表
    async getContentList() {
      try {
        this.loading = true
        const { rows, total } = await getPublicSentimentList(this.queryParams)
        this.contentList = rows
        this.total = total
      } catch (error) {
        console.error('获取内容列表失败:', error)
        this.$message.error('获取内容列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      // 过滤掉 all，如果包含 all 或为空数组，则传空数组给后端
      const fields = this.searchFields.includes('all') || this.searchFields.length === 0 ? [] : this.searchFields
      this.queryParams.searchFields = fields
      this.queryParams.searchText = this.searchText
      // 更新 store 中的 searchText
      this.$store.commit('yqmonitorMenu/SET_SEARCH_TEXT', this.searchText)
      this.queryParams.pageNum = 1
      this.getContentList()
    },

    handleSearchFieldsChange(val) {
      const allValue = 'all'
      if (val.includes(allValue) && val.length > 1) {
        if (val[val.length - 1] === allValue) {
          this.searchFields = [allValue]
        } else {
          this.searchFields = val.filter(item => item !== allValue)
        }
      }
    },

    // 分页相关方法
    handlePageChange(pageNum) {
      this.queryParams.pageNum = pageNum
      this.getContentList()
    },
    handleSizeChange(pageSize) {
      this.queryParams.pageSize = pageSize
      this.getContentList()
    },

    // 选中相关方法
    handleCheckChange({ id, checked }) {
      if (checked) {
        if (!this.checkedList.includes(id)) {
          this.checkedList.push(id)
        }
      } else {
        this.checkedList = this.checkedList.filter(_id => _id !== id)
      }
    },

    // 单个操作相关方法
    async handleSingleDelete(item) {
      try {
        await batchDeletePublicSentiment({ uniqueIds: [item.uniqueId] })
        this.contentList = this.contentList.filter(i => i.uniqueId !== item.uniqueId)
        if (this.checkedList.includes(item.uniqueId)) {
          this.checkedList = this.checkedList.filter(id => id !== item.uniqueId)
        }
        this.$message.success('删除成功')
      } catch (e) {
        this.$message.error('删除失败')
      }
    },

    async handleChangeSingleSentiment({ id, value }) {
      try {
        await batchUpdateSentiment({
          uniqueIds: [id],
          sensitivity: value.sentiment
        })
        this.contentList = this.contentList.map(item => {
          if (item.uniqueId === id) {
            return {
              ...item,
              feature: {
                ...item.feature,
                sensitive: value.sentiment
              }
            }
          }
          return item
        })
        this.$message.success('修改倾向性成功')
      } catch (e) {
        this.$message.error('修改倾向性失败')
      }
    },

    async handleChangeSingleAiSentimentType({ id, value }) {
      try {
        await batchUpdateAiSentimentType({
          uniqueIds: [id],
          aiSentimentType: value.value
        })
        this.contentList = this.contentList.map(item => {
          if (item.uniqueId === id) {
            return {
              ...item,
              dmxTagInfo: {
                ...item.dmxTagInfo,
                label: value.value
              }
            }
          }
          return item
        })
        this.$message.success('修改AI舆情倾向性成功')
      } catch (e) {
        this.$message.error('修改AI舆情倾向性失败')
      }
    },

    async handleSingleMarkRead(uniqueId) {
      try {
        await batchUpdatePublicSentiment({
          uniqueIds: [uniqueId],
          readStatus: 1
        })
        this.contentList = this.contentList.map(item => {
          if (item.uniqueId === uniqueId) {
            return { ...item, readStatus: 1 }
          }
          return item
        })
      } catch (e) {
        this.$message.error('标记已读失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.searchall-container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;
        margin-bottom: 20px;
        background-color: #fff;

        .el-select,
        .el-input {
            height: 38px;
            line-height: 38px;
            border-radius: 0;
            box-shadow: none;
        }

        .el-select {
            width: 100px;
            ::v-deep .el-select__tags {
                display: none;
            }
            ::v-deep>.el-input {
                .el-input__inner {
                    outline: none;
                    border: none;
                }
                &::after {
                    content: "匹配(...)";
                    color: #333;
                    font-size: 14px;
                    font-weight: 500;
                    position: absolute;
                    right: 39px;
                }
            }
        }

        .search-fields-group {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #1976d2;
            margin-left: 10px;
        }

        .el-input {
            ::v-deep .el-input__inner {
                border: none;
                outline: none;
            }
        }

        .search-icon {
            font-size: 20px;
            margin-left: 8px;
            cursor: pointer;
            color: #1976d2;
            transition: color 0.2s;
        }
        .search-icon:hover {
            color: #1251a3;
        }
    }

    .maincontent {
        flex: 1;
        overflow: auto;

        ::v-deep .list-item {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff;
        }

        ::v-deep .content-list {
            padding-bottom: 0px;

            .list-pagination {
                width: 100%;
                left: 0;
                bottom: 0;
            }
        }
    }
}
</style>
