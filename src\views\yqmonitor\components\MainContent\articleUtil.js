import { removeImgTagsFromRichText } from "@/utils/yqmonitorTool";
import { createUniqueString } from "@/utils/index";

// 标题截断长度
const TITLE_PREVIEW_LENGTH = 40;
// 内容截断长度
const CONTENT_PREVIEW_LENGTH = 400;

// 命中词属性与中文名映射
// const hitWordsMap = {
//   wyylHitWords: '万有引力',
//   institutionHitWords: '机构',
//   institutionAliasHitWords: '本案机构别名',
//   starHitWords: '明星',
//   companyHitWords: '名企',
//   sensitiveHitWords: '敏感词',
//   sensitiveMediaHitWords: '敏感媒体',
//   sensitiveAccountHitWords: '敏感账号',
//   keyMediaHitWords: '重点媒体',
//   keyAccountHitWords: '重点账号',
//   industryHitWords: '行业词',
//   aiHitWords: 'AI'
// }

function handleTitle(item) {
  // 标题优先级：title > content前20字 > ocr前20字 > asr前20字 > 缺省值
  if (item.title) return item.title;
  if (item.content) return item.content;

  // 非原创内容从 origin 中获取
  if (item.isOriginal !== 1 && item.origin) {
    if (item.origin.ocr) return item.origin.ocr;
    if (item.origin.asr) return item.origin.asr;
  }

  // 原创内容从 feature 中获取
  if (item.feature?.ocr) return item.feature.ocr;
  if (item.feature?.asr) return item.feature.asr;

  return "缺省值";
}

function handleContent(item) {
  const { content = "", feature = {}, postCategory, isOriginal, origin } = item;

  if (isOriginal !== 1 && origin) {
    let originContent = "";
    if (origin.title) {
      originContent += origin.title + "\n";
    }
    originContent += origin.content || "";
    if (origin.ocr) {
      originContent += `\nocr:${origin.ocr}`;
    }
    if (postCategory === 2 && origin.asr) {
      originContent += `\nasr:${origin.asr}`;
    }
    return originContent;
  }

  let result = content;
  // 图文类型（postCategory === 1）只显示 ocr
  if (postCategory === 1 && feature.ocr) {
    result += `\nocr:${feature.ocr}`;
  }
  // 视频类型（postCategory === 2）同时显示 ocr 和 asr
  if (postCategory === 2) {
    if (feature.ocr) {
      result += `\nocr:${feature.ocr}`;
    }
    if (feature.asr) {
      result += `\nasr:${feature.asr}`;
    }
  }
  return result;
}

// 获取内容预览
function getContentPreview(content, length = TITLE_PREVIEW_LENGTH) {
  if (!content) return "";
  return content.length > length
    ? content.substring(0, length) + "..."
    : content;
}

function getTitleObj(item) {
  let title = "";
  let rawTitle = "";
  // 算出原创文章的标题
  const setOneTitle = () => {
    // 优先取 title
    if (item.title) {
      title = getContentPreview(item.title, TITLE_PREVIEW_LENGTH);
    }

    // 以下情况可以断言：不会出现省略号
    // 其次取 content 前20字
    else if (item.content) {
      title = item.content.slice(0, 20);
    }
    // 再看 ocr
    else if (item.feature?.ocr) {
      title = item.feature.ocr.slice(0, 20);
    }
    // 然后看 asr
    else if (item.feature?.asr) {
      title = item.feature.asr.slice(0, 20);
    }
    // 最后都没有则使用默认值
    else {
      title = "缺省值";
    }
  };
  // 算出原创文章的原始标题
  // 断言：出现省略号的情况下才需要rawTitle
  const setOneRawTitle = () => {
    // 如果 title 有省略号,rawTitle 取完整内容
    if (title.endsWith("...")) {
      if (item.title) {
        rawTitle = item.title;
      }
    }
  };

  // 算出非原创文章的标题
  const setOtherTitleAndRawTitle = () => {
    const parts = [];

    // 添加title
    if (item.title) {
      parts.push(item.title);
    }

    // 添加content，如果title存在则用冒号连接
    if (item.content) {
      if (item.title) {
        parts.push(`: ${item.content}`);
      } else {
        parts.push(item.content);
      }
    }

    // 添加ocr
    if (item.feature?.ocr) {
      parts.push(`ocr:${item.feature.ocr}`);
    }

    // 添加asr
    if (item.feature?.asr) {
      parts.push(`asr:${item.feature.asr}`);
    }

    // 拼接所有部分
    title = parts.join("");

    // 如果都没有值则使用默认值
    if (!title) {
      title = "缺省值";
    }
    if (title.length > TITLE_PREVIEW_LENGTH) {
      // 先保存原始标题
      rawTitle = title;
      // 再截断
      title = getContentPreview(title, TITLE_PREVIEW_LENGTH);
    }
  };

  // 原创文章
  if (item.isOriginal === 1) {
    setOneTitle();
    setOneRawTitle();
  } else {
    // 非原创文章
    setOtherTitleAndRawTitle();
  }

  return {
    rawTitle,
    title,
  };
}

function getContentObj(item) {
  let content = "";
  let rawContent = "";
  let rawContentHtml = "";
  let ocr = "";
  let asr = "";
  let rawOcr = "";
  let rawAsr = "";

  if (item.isOriginal === 1) {
    // 原创文章
    content = item.dmxTagInfo?.dmxAbstractInfo?.summary
      ? `<span class="custom-tag custom-tag-content zw">正文</span>${getContentPreview(
          item.dmxTagInfo.dmxAbstractInfo.summary,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    // 确定OCR预览内容的来源：优先使用 firstFrame，如果为空，则回退到 feature.ocr
    const ocrPreviewSource = item.dmxTagInfo?.dmxAbstractInfo?.firstFrame || item.feature?.ocr;
    ocr = ocrPreviewSource
      ? `<span class="custom-tag custom-tag-warning">ocr</span>${getContentPreview(
          ocrPreviewSource,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    asr = item.dmxTagInfo?.dmxAbstractInfo?.audio
      ? `<span class="custom-tag custom-tag-success">asr</span>${getContentPreview(
          item.dmxTagInfo.dmxAbstractInfo.audio,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    rawContent = item.content || "";
    rawContentHtml =
      removeImgTagsFromRichText(item.contentHtml) || item.content || "";
    rawOcr = item.feature?.ocr || "";
    rawAsr = item.feature?.asr || "";
  } else {
    // 非原创文章
    content = item.origin?.content
      ? `<span class="custom-tag custom-tag-content zw">正文</span><img src="/avatar.png" class="avatar-img" /><span class="origin-nickname">${
          item.origin.nickname
        }：</span>${getContentPreview(
          item.origin.content,
          CONTENT_PREVIEW_LENGTH
        )}`
      : item.origin?.title
      ? `<span class="custom-tag custom-tag-content zw">标题</span><img src="/avatar.png" class="avatar-img" /><span class="origin-nickname">${
          item.origin.nickname
        }：</span>${getContentPreview(
          item.origin.title,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    rawContent = item.origin?.content || item.origin?.title || "";
    ocr = item.origin?.ocr
      ? `<span class="custom-tag custom-tag-warning">ocr</span>${getContentPreview(
          item.origin.ocr,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    asr = item.origin?.asr
      ? `<span class="custom-tag custom-tag-success">asr</span>${getContentPreview(
          item.origin.asr,
          CONTENT_PREVIEW_LENGTH
        )}`
      : "";
    rawOcr = item.origin?.ocr || "";
    rawAsr = item.origin?.asr || "";
  }

  return {
    content,
    ocr,
    asr,
    rawContent,
    rawContentHtml,
    rawOcr,
    rawAsr,
  };
}

function mapItem(item) {
  // ====================== 开始：coverInfo.coverOcr处理 ======================
  // 1. 安全地获取封面OCR文本
  const coverOcr = item?.videoInfo?.coverInfo?.coverOcr;

  // 2. 确保 coverOcr 和 feature.ocr 都是字符串
  if (typeof coverOcr === "string" && typeof item?.feature?.ocr === "string") {
    // 3. 判断 feature.ocr 是否已经包含了封面OCR的内容
    if (!item.feature.ocr.includes(coverOcr)) {
      // 4. 如果不包含，则将封面OCR内容拼接到最前面（加个换行符以作区分）
      console.log("coverOcr", coverOcr);
      item.feature.ocr = coverOcr + "\n" + item.feature.ocr;
    }
  }
  // ====================== 结束：coverInfo.coverOcr处理 ======================
  // 互动数据
  const stats = item.postStats || {};
  // 作者信息
  const user = item.userInfo || {};
  // 地理位置
  const location =
    item.basedLocation && item.basedLocation.publicLocation
      ? item.basedLocation.publicLocation.province
      : "";
  // 标题处理
  const title = handleTitle(item);
  // 内容处理
  const content = handleContent(item);

  return {
    // 基础信息
    frontId: `${item.pushTime}-${item.uniqueId}-${createUniqueString()}`,    uniqueId: item.uniqueId,
    postId: item.postId,
    url: item.url,
    // 标题、内容、图片、视频
    title: getContentPreview(title, TITLE_PREVIEW_LENGTH),
    titleObj: getTitleObj(item),
    content, // content不需要截断 等splitContentBlocks处理
    contentObj: getContentObj(item),
    rawTitle: title || "",
    rawContent: content || "",
    rawOcr: item.feature?.ocr || "",
    rawAsr: item.feature?.asr || "",
    images: item.images || [],
    playUrl: item.playUrl || [],
    videoCover:
      item.videoInfo && item.videoInfo.coverInfo
        ? item.videoInfo.coverInfo.onlineUrl
        : "",
    videoDuration: item.videoInfo ? item.videoInfo.duration : 0,
    // 来源信息
    sourceType: item.sourceType,
    sourceLevel: item.sourceLevel,
    platform: item.platform,
    source: item.platformName || "",
    // 内容分类
    postCategory: item.postCategory,
    postType: item.postType,
    isOriginal: item.isOriginal,
    // 原始内容
    origin: item.origin,
    // 互动
    views: stats.viewCount || 0,
    comments: stats.commentCount || 0,
    likes: stats.likeCount || 0,
    reposts: stats.repostCount || 0,
    collects: stats.collectCount || 0,
    // 作者
    author: user.nickname || "",
    avatar: user.avatar || "",
    authorId: user.userId || "",
    authorUrl: user.url || "",
    fansCount: user.fansCount || 0,
    verifyInfo: user.verifyInfo || {},
    // 采集时间
    time: item.pushTime || "",
    // 初次采集时间
    firstCollectionTime: item.firstCollectionTime || "",
    // 发布时间
    publishTime: item.postPublishTime || "",
    // 敏感性
    sentiment: item.feature?.sensitive,
    // 地理位置
    location,
    // 特征信息
    isNoise: item.feature?.isNoise || false,
    asr: item.feature?.asr || "",
    dmxTagInfo: item.dmxTagInfo,
    // 命中词（逗号隔开字符串）
    hitWordStr: Array.isArray(item.planIds[0].hitWord)
      ? item.planIds[0].hitWord.join(",")
      : "",
    hitWordsInfo: item.hitWordsInfo, // 新的命中词取值对象(所有命中词都从这里取值)
    // 阅读状态
    readStatus: item.readStatus, // 即将舍弃
    moduleReadStatus: item.moduleReadStatus, // 模块阅读状态
    isFavorited: item.isFavorited, // 收藏状态
    // 折叠信息
    similarGroupId: item.similarGroupId,
    similarCount: item.similarCount,

    splited: item.dmxTagInfo?.splited,
  };
}

// 获取万有引力命中词
function getWyylHitWords(hitWordsInfo) {
  if (!hitWordsInfo || !Array.isArray(hitWordsInfo.wyylHitWords)) return [];
  return hitWordsInfo.wyylHitWords.filter(Boolean);
}

// 获取AI舆情特征详情（排除wyylHitWords、institutionAliasHitWords、aiHitWords，institutionHitWords优先）
// 目前只显示机构
function getAiFeatureHitWords(hitWordsInfo, needSensitiveHitWords = false) {
  if (!hitWordsInfo) return [];
  const result = [];
  const hitWordsMap = {
    institutionHitWords: "机构",
    industryHitWords: "行业词",
  };
  if (needSensitiveHitWords) {
    hitWordsMap.sensitiveHitWords = "敏感词";
  }
  // institutionHitWords优先
  if (
    Array.isArray(hitWordsInfo.institutionHitWords) &&
    hitWordsInfo.institutionHitWords.length
  ) {
    result.push({
      key: "institutionHitWords",
      label: "机构",
      value: hitWordsInfo.institutionHitWords,
    });
  }
  // 其他属性
  Object.keys(hitWordsMap).forEach((key) => {
    if (
      key !== "institutionHitWords" &&
      Array.isArray(hitWordsInfo[key]) &&
      hitWordsInfo[key].length > 0
    ) {
      result.push({
        key,
        label: hitWordsMap[key],
        value: hitWordsInfo[key].filter(Boolean),
      });
    }
  });
  return result;
}

// 获取机构别名命中词
function getInstitutionAliasHitWords(hitWordsInfo) {
  if (!hitWordsInfo || !Array.isArray(hitWordsInfo.institutionAliasHitWords)) {
    return [];
  }
  return hitWordsInfo.institutionAliasHitWords.filter(Boolean);
}

// 获取媒体相关命中词（敏感媒体、重点媒体）
function getMediaHitWords(hitWordsInfo) {
  if (!hitWordsInfo) return [];

  const mediaWords = [];

  // 敏感媒体
  if (Array.isArray(hitWordsInfo.sensitiveMediaHitWords)) {
    mediaWords.push(...hitWordsInfo.sensitiveMediaHitWords.filter(Boolean));
  }

  // 重点媒体
  if (Array.isArray(hitWordsInfo.keyMediaHitWords)) {
    mediaWords.push(...hitWordsInfo.keyMediaHitWords.filter(Boolean));
  }

  return [...new Set(mediaWords)]; // 去重
}

// 获取账号相关命中词（敏感账号、重点账号）
function getAccountHitWords(hitWordsInfo) {
  if (!hitWordsInfo) return [];

  const accountWords = [];

  // 敏感账号
  if (Array.isArray(hitWordsInfo.sensitiveAccountHitWords)) {
    accountWords.push(...hitWordsInfo.sensitiveAccountHitWords.filter(Boolean));
  }

  // 重点账号
  if (Array.isArray(hitWordsInfo.keyAccountHitWords)) {
    accountWords.push(...hitWordsInfo.keyAccountHitWords.filter(Boolean));
  }

  return [...new Set(accountWords)]; // 去重
}

// 获取需要从高亮中排除的词汇
function getExcludedFromHighlight(hitWordsInfo) {
  if (!hitWordsInfo) return [];

  const excludedWords = [];

  // 明星、名企、敏感媒体、敏感账号、重点媒体、重点账号
  [
    "starHitWords",
    "companyHitWords",
    "sensitiveMediaHitWords",
    "sensitiveAccountHitWords",
    "keyMediaHitWords",
    "keyAccountHitWords",
  ].forEach((key) => {
    if (Array.isArray(hitWordsInfo[key])) {
      excludedWords.push(...hitWordsInfo[key].filter(Boolean));
    }
  });

  return [...new Set(excludedWords)]; // 去重
}

// 获取敏感词 (sensitiveHitWords)
function getSensitiveHitWords(hitWordsInfo) {
  if (!hitWordsInfo || !Array.isArray(hitWordsInfo.sensitiveHitWords)) {
    return [];
  }
  return hitWordsInfo.sensitiveHitWords.filter(Boolean);
}

export {
  mapItem,
  getContentPreview,
  getWyylHitWords,
  getAiFeatureHitWords,
  getInstitutionAliasHitWords,
  getMediaHitWords,
  getAccountHitWords,
  getExcludedFromHighlight,
  getSensitiveHitWords,
};
