<template>
  <div class="public-opinion-detail">
    <!-- 顶部导航栏 -->
    <TopNavBar :active-tab="activeTab" @tab-click="handleTabClick" />
    <div class="container">
      <!-- 信息详情 -->
      <InfoDetail v-if="activeTab === 'info-detail'" :info-detail="infoDetail" :raw-content="rawContent" :sentiment-data="sentimentData" />

      <!-- 账号信息 (替换了原账号画像) -->
      <AccountInfo v-if="activeTab === 'account-info'" :account-info="accountInfo" />

      <!-- 相似信息 (新增) -->
      <SimilarInfo v-if="activeTab === 'similar-info'" :similar-info-list="similarInfoList" />

      <!-- 相关热搜 (新增) -->
      <!-- <HotSearches v-if="activeTab === 'hot-searches'" /> -->
    </div>
    <!-- 页脚 -->
    <Footer />
  </div>
</template>

<script>
// 引入组件
import TopNavBar from './components/TopNavBar.vue'
import InfoDetail from './components/InfoDetail.vue'
import AccountInfo from './components/AccountInfo.vue' // 引入新组件
import SimilarInfo from './components/SimilarInfo.vue' // 引入新组件
import HotSearches from './components/HotSearches.vue' // 引入新组件
import Footer from './components/Footer.vue'
import { getWarnMsgDetail, getTopUsersBySimilarId, getPublicSentimentById, getPublicSentimentList } from '@/api/h5'
import { mapItem } from '@/views/yqmonitor/components/MainContent/articleUtil'

export default {
  name: 'PablicOpinionDetail',
  components: {
    TopNavBar,
    InfoDetail,
    AccountInfo, // 替换 AccountProfile
    SimilarInfo,
    HotSearches,
    Footer
  },
  data() {
    return {
      activeTab: 'info-detail',
      // Data for "信息详情" tab
      infoDetail: {
        title: '',
        titleLink: '',
        riskStamp: '',
        agency: '',
        time: '',
        source: '',
        sourceLink: '',
        video: null,
        analysis: '',
        summary: ''
      },
      rawContent: '', // 新增：保存原始content内容
      sentimentData: null,
      similarId: '', // For similar articles reference
      // Data for "账号信息" tab (新的数据结构)
      accountInfo: {
        basicInfo: {
          avatar: '',
          nickname: '',
          id: '',
          homepageId: '',
          homepageUrl: '',
          followers: '',
          verificationType: '',
          verificationName: '',
          location: ''
        },
        aiSensitiveInfos: []
      },
      similarInfoList: []
      // spreadInfo 数据已移除
    }
  },
  created() {
    this.initData()
  },
  methods: {
    handleTabClick(tabName) {
      this.activeTab = tabName
    },
    fetchWarnMsgDetail(uniqueId) {
      getWarnMsgDetail({ postUniqueId: uniqueId })
        .then(response => {
          const data = response.data
          const content = data.content || ''
          console.log('我是content:', content)
          // Parse content fields
          const agencyMatch = content.match(/机构：([^\n]+)/)
          const titleMatch = content.match(/标题：([^\n]+)/)
          const riskLevelMatch = content.match(/风险等级：([^\n]+)/)
          const timeMatch = content.match(/时间：([^\n]+)/)
          const sourceMatch = content.match(/来源：([^\n]+)/)
          const summaryMatch = content.match(/摘要：([^\n]+)/)
          const analysisMatch = content.match(/研判分析：([^\n]+)/)

          // 逐项赋值，排除 titleLink、sourceLink、video
          this.infoDetail.title = titleMatch ? titleMatch[1] : data.title || ''
          this.infoDetail.riskStamp = riskLevelMatch ? riskLevelMatch[1] : ''
          this.infoDetail.agency = agencyMatch ? agencyMatch[1] : ''
          this.infoDetail.time = timeMatch ? timeMatch[1] : ''
          this.infoDetail.source = sourceMatch ? sourceMatch[1] : ''
          this.infoDetail.analysis = analysisMatch ? analysisMatch[1] : ''
          this.infoDetail.summary = summaryMatch ? summaryMatch[1] : ''
          this.rawContent = content // 保存原始content内容
        })
        .catch(error => {
          console.error('Error fetching warning message details:', error)
        })
    },
    fetchPublicSentimentById(uniqueId) {
      return getPublicSentimentById(uniqueId).then(response => {
        if (response.code === 200) {
          console.log('舆情详情:', response.data)
          const data = response.data || {}
          this.sentimentData = data
          this.similarId = data.feature && data.feature.similarId || ''
          // 补充 titleLink、sourceLink、video 的赋值
          this.infoDetail.titleLink = data.url || ''
          this.infoDetail.sourceLink = (data.userInfo && data.userInfo.url) || ''
          // video 是对象 {src, title, duration}，取视频相关数据
          if (Array.isArray(data.playUrl) && data.playUrl.length > 0) {
            this.infoDetail.video = {
              src: data.playUrl[0],
              title: (data.videoInfo && data.videoInfo.coverInfo && data.videoInfo.coverInfo.coverOcr) || '',
              cover: (data.videoInfo && data.videoInfo.coverInfo && data.videoInfo.coverInfo.onlineUrl) || '',
              duration: (data.videoInfo && data.videoInfo.duration) || 0
            }
          } else {
            this.infoDetail.video = null
          }
          // 账号信息 basicInfo 赋值
          const userInfo = data.userInfo || {}
          this.accountInfo.basicInfo.avatar = userInfo.avatar || ''
          this.accountInfo.basicInfo.nickname = userInfo.nickname || ''
          this.accountInfo.basicInfo.id = userInfo.userId || ''
          this.accountInfo.basicInfo.homepageUrl = userInfo.url || ''
          this.accountInfo.basicInfo.followers = userInfo.fansCount || ''
          // 下面字段接口无对应，保留原值或置空
          // homepageId 取自 userInfo.url
          this.accountInfo.basicInfo.homepageId = userInfo.url || ''
          // verificationType、verificationName 来自 verifyInfo
          if (userInfo.verifyInfo && typeof userInfo.verifyInfo === 'object') {
            this.accountInfo.basicInfo.verificationType = userInfo.verifyInfo.verifyType || ''
            this.accountInfo.basicInfo.verificationName = userInfo.verifyInfo.verifyName || ''
          } else {
            this.accountInfo.basicInfo.verificationType = ''
            this.accountInfo.basicInfo.verificationName = ''
          }
          // location 字段
          const locationInfo = data.basedLocation && data.basedLocation.publicLocation ? data.basedLocation.publicLocation : {}
          const province = locationInfo.province || ''
          const city = locationInfo.city || ''
          if (province && city && province !== city) {
            this.accountInfo.basicInfo.location = `${province}-${city}`
          } else if (province) {
            this.accountInfo.basicInfo.location = province
          } else {
            this.accountInfo.basicInfo.location = ''
          }
        } else {
          this.$message.error(response.msg || '获取舆情详情失败')
        }
      }).catch(error => {
        console.error('获取舆情详情失败:', error)
        this.$message.error('获取舆情详情失败')
      })
    },
    /**
     * 获取作者相关的舆情列表
     * 依赖于 fetchPublicSentimentById 执行后，accountInfo.basicInfo.nickname 已赋值
     */
    fetchAuthorPublicSentimentList() {
      // 构造参数，依赖 this.accountInfo.basicInfo.nickname
      const params = {
        pageNum: 1,
        pageSize: 10,
        searchFields: ['author'],
        searchText: this.accountInfo.basicInfo.nickname,
        isAsc: 'desc',
        aiSentimentTypes: ['1'],
        enableSimilarityDedup: 'fold'
      }
      getPublicSentimentList(params).then(res => {
        if (res.code === 200) {
          if (Array.isArray(res.rows)) {
            // 这里用 mapItem 处理
            this.accountInfo.aiSensitiveInfos = res.rows.map(item => mapItem(item))
          }
        } else {
          this.$message.error(res.msg || '获取作者相关舆情列表失败')
        }
      }).catch(error => {
        console.error('获取作者相关舆情列表失败:', error)
        this.$message.error('获取作者相关舆情列表失败')
      })
    },
    fetchTopUsersBySimilarId(uniqueId) {
      // 临时代码：用getPublicSentimentList代替getTopUsersBySimilarId，方便页面展示
      // const params = {
      //   pageNum: 1,
      //   pageSize: 5,
      //   aiSentimentTypes: ['1'],
      //   enableSimilarityDedup: 'fold',
      // };
      // getPublicSentimentList(params).then(res => {
      //   if (res.code === 200 && Array.isArray(res.rows)) {
      //     this.similarInfoList = res.rows.map(item => mapItem(item));
      //   } else {
      //     this.$message.error(res.msg || '获取相似信息列表失败');
      //   }
      // }).catch(error => {
      //   console.error('获取相似信息列表失败:', error);
      //   this.$message.error('获取相似信息列表失败');
      // });

      if (!this.similarId) {
        console.log('similarId is not available, skipping fetchTopUsersBySimilarId')
        return
      }
      const params = {
        uniqueId: uniqueId,
        similarId: this.similarId
      }
      getTopUsersBySimilarId(params)
        .then(response => {
          if (response.code === 200 && Array.isArray(response.rows)) {
            console.log('Top users by similarId:', response.rows)
            this.similarInfoList = response.rows.map(item => mapItem(item))
          } else {
            this.$message.error(response.msg || '获取相似信息列表失败')
          }
        })
        .catch(error => {
          console.error('Error fetching top users by similarId:', error)
        })
    },
    initData() {
      // Extract uniqueId from URL path /yqwarning/h5/{uniqueId}
      const pathParts = this.$route.path.split('/')
      const uniqueId = pathParts[pathParts.length - 1]
      this.fetchWarnMsgDetail(uniqueId)
      this.fetchPublicSentimentById(uniqueId).then(() => {
        this.fetchAuthorPublicSentimentList()
        this.fetchTopUsersBySimilarId(uniqueId)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.public-opinion-detail {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex-grow: 1;
  margin: 15px auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 20px;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
  width: 95%;
}

@media (max-width: 480px) {
  .container {
    margin: 10px;
    padding: 15px;
  }
}
</style>
