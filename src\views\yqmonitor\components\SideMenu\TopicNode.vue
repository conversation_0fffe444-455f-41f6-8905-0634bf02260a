<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleNodeClick" />
    <div class="node-content" :class="{ 'opacity-text': data.status === 0 }" @click="handleNodeClick">
      <svg-icon :icon-class="data.id === '1-1' ? 'all' : 'topic'" class-name="custom-class" />
      <span class="node-label" :title="node.label">{{ node.label }}</span>
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click.stop>
        <i class="el-icon-more" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="edit">编辑</el-dropdown-item>
        <el-dropdown-item command="toggleUpdate">{{ data.status === 1 ? '暂停更新' : '开启更新' }}</el-dropdown-item>
        <el-dropdown-item command="delete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import auth from '@/plugins/auth'
import { openPlan, suspendPlan, deletePlan } from '@/api/yqmonitor/topic'
export default {
  name: 'TopicNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleNodeClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', this.data)
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'edit' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有编辑权限')
        return
      }
      if (command === 'toggleUpdate' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有更新权限')
        return
      }
      if (command === 'delete' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有删除权限')
        return
      }
      if (command === 'edit') {
        this.$emit('command', { command: 'editTopic', data: this.data })
      } else if (command === 'toggleUpdate') {
        this.handleToggleUpdate()
      } else if (command === 'delete') {
        this.deleteTopic()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    },
    deleteTopic() {
      this.$confirm('确认删除该专题吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePlan({ menuId: this.data.id }).then(async response => {
          console.log('删除专题', response)
          if (response.code === 0) {
            await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
            const menuList = this.$store.state.yqmonitorMenu.menuList
            if (menuList && menuList.length > 0) {
              this.$store.dispatch('yqmonitorMenu/setCurrentNode', menuList[0])
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      }).catch(() => { })
    },
    async handleToggleUpdate() {
      try {
        const menuId = this.data.id
        if (this.data.status === 1) {
          const res = await suspendPlan({ menuId })
          if (res.code === 0) {
            this.data.status = 0
            this.$message.success('暂停更新成功')
          } else {
            this.$message.error('暂停更新失败')
          }
        } else {
          const res = await openPlan({ menuId })
          if (res.code === 0) {
            this.data.status = 1
            this.$message.success('开启更新成功')
          } else {
            this.$message.error('开启更新失败')
          }
        }
      } catch (error) {
        console.error('切换更新状态失败:', error)
        this.$message.error('切换更新状态失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;

    .node-content {
        display: flex;
        align-items: center;
    }

    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }

    .node-input {
        margin-left: 8px;
        width: 130px;
        background: transparent;
        border: none;
        border-bottom: 1px solid #409EFF;
        color: #fff;
        outline: none;
        padding: 2px 4px;
        font-size: 16px;
    }
}

.custom-class {
    font-size: 15px;
}

.el-dropdown-link {
    cursor: pointer;
    color: #fff;

    &:hover {
        color: #409EFF;
    }
}

.opacity-text {
    opacity: 0.3;
}

.click-area {
    position: absolute;
    left: -35px;
    top: -9px;
    width: 170px;
    height: 50px;
    cursor: pointer;
}
</style>
