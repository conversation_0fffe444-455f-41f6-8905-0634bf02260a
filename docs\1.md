# dynamicRoutes的作用

## `dynamicRoutes` 的作用是什么？

`dynamicRoutes`（定义在 `src/router/index.js`）是一组**预定义在前端的、需要权限才能访问的路由**。

通过分析代码，我们可以总结出它的几个关键特征：

1.  **前端预定义**：与从服务器获取的路由不同，这些路由的路径、组件、元信息（meta）都硬编码在前端 `router/index.js` 文件中。
2.  **与特定功能绑定**：这些路由通常对应一些非菜单功能页面。例如：
    *   `path: 'role/:userId(\\d+)'`：为用户分配角色的页面。
    *   `path: 'index/:dictId(\\d+)'`：查看某个字典数据的页面。
    *   `path: 'index/:tableId(\\d+)'`：修改代码生成配置的页面。
    这些页面通常不是通过点击侧边栏菜单进入的，而是通过在某个列表页（如用户列表、字典列表）点击“编辑”或“分配角色”等按钮进行跳转。
3.  **权限控制**：每条路由都配置了 `permissions` 属性，例如 `permissions: ['system:user:edit']`。这意味着只有当用户拥有 `system:user:edit` 这个权限时，这条路由才会被激活和访问。
4.  **非菜单项**：这些路由都设置了 `hidden: true`，意味着它们不会显示在侧边栏导航菜单中。

**总结：`dynamicRoutes` 的核心作用是定义那些不属于主导航菜单、但又需要根据用户权限动态加载的“功能性”页面路由。**

---

## 为什么有了服务端返回的动态路由，还需要 `dynamicRoutes`？

这是一个非常好的问题，它涉及到前端路由设计的架构思想。简单来说，这是**为了实现“菜单配置”与“功能权限”的解耦**。

我们来分别看两者各自的职责：

### 1. 服务端返回的路由 (`getRouters()` 的结果)

*   **职责**：定义应用的**主导航结构（通常是侧边栏菜单）**。
*   **目的**：提供灵活性。管理员可以在后台配置菜单的显示、隐藏、排序、层级关系、图标和名称，而**不需要修改和重新部署前端代码**。例如，管理员可以在后台把“系统监控”菜单挪到“系统管理”菜单下面，前端下次登录时就会自动应用新的菜单结构。
*   **处理流程** (`permission.js` 中的 `filterAsyncRouter` 函数)：前端获取到这份JSON数据后，会将其解析，把字符串路径（如 `'@/views/system/user/index'`）动态地转换成真正的Vue组件 (`loadView` 函数），从而生成用户能看到的菜单和对应的页面路由。

### 2. 前端定义的 `dynamicRoutes`

*   **职责**：定义应用内部的**功能性、非菜单页面路由**。
*   **目的**：将与具体业务操作紧密耦合的页面路由固定在前端。这些页面（如编辑页、详情页、分配权限页）的URL结构通常是固定的，并且由前端的某个按钮或链接触发。将它们写在前端代码里有以下好处：
    *   **关注点分离**：后端只关心“用户是否拥有`system:user:edit`这个权限标识”，而不关心这个权限具体对应前端的哪个URL路径。前端则负责将这个权限标识与具体的页面路由 `/system/user-auth/role/:userId` 关联起来。这是一种解耦，让前后端职责更清晰。
    *   **开发便利性**：前端开发者在开发“用户管理”功能时，可以直接在代码中定义和引用“分配角色”的路由，而不需要去后台系统中进行配置，逻辑更内聚。
    *   **稳定性**：这些功能性路由是应用逻辑的一部分，不应该由非开发人员（如系统管理员）在后台随意修改，否则可能导致前端功能异常（例如，按钮点击后找不到对应的路由）。

### 流程整合 (`GenerateRoutes` action)

在 `permission.js` 的 `GenerateRoutes` action 中，这两部分被整合在一起：

1.  `getRouters().then(...)`: 从后端获取菜单数据。
2.  `filterAsyncRouter(sdata)`: 将菜单数据转换成侧边栏要显示的路由（`sidebarRoutes`）。
3.  `filterDynamicRoutes(dynamicRoutes)`: **用从后端获取的用户权限（存储在 `store.getters.permissions`）去过滤前端预定义的 `dynamicRoutes`**。
4.  `router.addRoutes(asyncRoutes)`: 将过滤后、用户有权访问的 `dynamicRoutes` 添加到Vue Router实例中，使其可以被访问。
5.  `commit("SET_ROUTES", ...)`: 将服务端返回的路由和固定路由组合起来，形成完整的路由表，用于导航等。

## 结论

| 路由类型 | 来源 | 作用 | 为什么需要 |
| :--- | :--- | :--- | :--- |
| **服务端返回的路由** | 后端 API (`getRouters`) | 构建**可配置的导航菜单** | 提供灵活性，允许管理员在后台调整菜单结构，无需改动前端代码。 |
| **`dynamicRoutes`** | 前端代码 (`src/router/index.js`) | 定义**非菜单的功能性页面路由**，并与权限绑定 | 解耦菜单与功能，将固定的业务逻辑路由保留在前端，后端只负责授权，增强了系统的稳定性和开发便利性。 |

所以，**服务端返回的路由决定了“你能看到什么菜单”，而前端的 `dynamicRoutes` 决定了“你有什么权限去做什么操作（访问哪些功能页面）”**。两者各司其职，共同构成了这套灵活且职责分明的动态路由权限方案。
