<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="localVisible"
    width="70%"
    :before-close="handleClose"
    append-to-body
    custom-class="similar-articles-dialog"
  >
    <div v-loading="loading" class="dialog-content-wrapper">
      <div v-if="!loading && (!articles || articles.length === 0)" class="empty-state">
        <el-empty description="暂无相似数据" />
      </div>
      <div v-else class="articles-list-container">
        <SimilarArticleItem v-for="item in mappedArticles" :key="item.uniqueId" :item="item" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- 分页组件 -->
      <div v-if="!loading && articles && articles.length > 0" class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </span>
  </el-dialog>
</template>

<script>
import SimilarArticleItem from './SimilarArticleItem.vue'
import { mapItem } from './articleUtil' // 用于映射原始数据

export default {
  name: 'SimilarArticlesDialog',
  components: { SimilarArticleItem },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    articles: {
      // 原始数据列表
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    representativeArticleTitle: {
      // 可选：主文章标题
      type: String,
      default: '相似文章列表'
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      localVisible: this.visible,
      // 分页相关数据
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    mappedArticles() {
      if (!this.articles) return []
      return this.articles.map((article) => mapItem(article)) // 使用现有的 mapItem 方法
    },
    dialogTitle() {
      if (
        this.representativeArticleTitle &&
        this.representativeArticleTitle !== '相似文章列表'
      ) {
        return `“${this.representativeArticleTitle}” 的相似文章`
      }
      return '相似文章列表'
    }
  },
  watch: {
    visible(val) {
      this.localVisible = val
      // 打开对话框时重置分页
      if (val) {
        this.currentPage = 1
        this.pageSize = 10
      }
    },
    localVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.$emit('page-change', {
        pageNum: this.currentPage,
        pageSize: this.pageSize
      })
    }
  }
}
</script>

<style lang="scss">
.similar-articles-dialog {
  .el-dialog__body {
    padding: 10px 20px;
    max-height: 70vh; // 根据需要调整
    overflow-y: auto;
  }

  .dialog-content-wrapper {
    min-height: 200px; // 正确显示加载指示器
  }

  .articles-list-container {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
    padding: 15px 0;
    border-top: 1px solid #ebeef5;
  }
}
</style>
