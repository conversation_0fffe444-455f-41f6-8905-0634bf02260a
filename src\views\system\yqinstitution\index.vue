<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" @submit.native.prevent="handleQuery">
      <el-form-item label="机构名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入机构名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
          >展开/折叠</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleBatchDelete">批量删除</el-button>
      </el-col>
    </el-row>

    <el-table
      v-if="refreshTable"
      ref="table"
      v-loading="loading"
      :data="institutionList"
      row-key="code"
      lazy
      :load="loadNode"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      style="width: 100%"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="机构名称" width="200" />
      <el-table-column prop="shortName" label="简称" width="150">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.shortName" placement="top">
            <span>{{ scope.row.shortName && scope.row.shortName.length > 8 ? scope.row.shortName.slice(0, 8) + '...' : scope.row.shortName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="aliasName" label="别名" width="180">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.aliasName" placement="top">
            <span>{{ scope.row.aliasName && scope.row.aliasName.length > 10 ? scope.row.aliasName.slice(0, 10) + '...' :
              scope.row.aliasName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)">新增</el-button>
          <el-button size="mini" type="text" icon="el-icon-collection" @click="handleInstitutionGroup(scope.row)">机构分组</el-button>
          <el-button
            v-if="scope.row.parentCode !== undefined"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑机构弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="父节点">
          <el-input v-model="form.parentName" disabled />
        </el-form-item>
        <el-form-item v-if="title === '编辑机构'" label="机构编码" prop="code">
          <el-input v-model="form.code" disabled />
        </el-form-item>
        <el-form-item label="机构名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="简称" prop="shortName">
          <el-input v-model="form.shortName" placeholder="请输入机构简称" />
        </el-form-item>
        <el-form-item label="别名" prop="aliasName">
          <el-input v-model="form.aliasName" type="textarea" :rows="20" placeholder="请输入别名，多个别名用中文逗号（，）分隔" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 机构分组弹窗 -->
    <el-dialog :title="groupDialogTitle" :visible.sync="groupDialogOpen" width="600px" append-to-body>
      <div style="margin-bottom: 15px;">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddGroup">新增分组</el-button>
      </div>
      <el-table v-loading="groupLoading" :data="groupList" style="width: 100%">
        <el-table-column prop="groupName" label="分组名称" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEditGroup(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteGroup(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 新增/编辑机构分组弹窗 -->
    <el-dialog :title="groupFormTitle" :visible.sync="groupFormOpen" width="400px" append-to-body>
      <el-form ref="groupForm" :model="groupForm" :rules="groupRules" label-width="80px">
        <el-form-item label="机构码">
          <el-input v-model="groupForm.institutionCode" disabled />
        </el-form-item>
        <el-form-item label="分组名" prop="groupName">
          <el-input v-model="groupForm.groupName" placeholder="请输入分组名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGroupForm">确 定</el-button>
        <el-button @click="groupFormOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInstitution,
  addInstitution,
  editInstitution,
  deleteInstitution,
  getInstitutionInfo,
  // 导入机构分组相关接口
  listInstitutionGroup,
  addInstitutionGroup,
  editInstitutionGroup,
  removeInstitutionGroup
} from '@/api/yqmonitor/institution'
import { getToken } from '@/utils/auth'

export default {
  name: 'InstitutionIndex',
  data() {
    return {
      loading: false,
      showSearch: true,
      institutionList: [], // 用于渲染的列表，初始只包含顶层节点
      fullInstitutionTree: [], // 存储完整的树形数据
      refreshTable: true,
      queryParams: {
        name: undefined,
        pageNum: 1, // 懒加载模式下，一次性获取所有数据，分页参数可能不再需要
        pageSize: 10000
      },
      open: false,
      title: '',
      form: {
        name: '',
        shortName: '', // 新增简称字段
        aliasName: '',
        parentCode: undefined,
        parentName: '根节点'
      },
      rules: {
        name: [
          { required: true, message: '机构名称不能为空', trigger: 'blur' }
        ],
        shortName: [
          { max: 50, message: '简称长度不能超过50个字符', trigger: 'blur' }
        ]
      },
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/business/institution/import'
      },
      // 机构分组相关数据
      groupDialogOpen: false,
      groupDialogTitle: '',
      currentInstitutionCode: '',
      currentInstitutionName: '',
      groupList: [],
      groupLoading: false,
      groupFormOpen: false,
      groupFormTitle: '',
      groupForm: {
        id: '',
        institutionCode: '',
        groupName: ''
      },
      groupRules: {
        groupName: [
          { required: true, message: '分组名称不能为空', trigger: 'blur' },
          { max: 100, message: '分组名称长度不能超过100个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 将一维数组转换为树形结构
    convertToTree(list) {
      const map = {}
      const result = []
      // 收集所有有效的 code 值
      const validCodes = new Set(list.map(item => item.code))
      // 先将所有节点放入map中，并处理脏数据
      list.forEach((item) => {
        // 检查 parentCode 是否为脏数据
        if (item.parentCode && !validCodes.has(item.parentCode)) {
          item.parentCode = ''
        }
        map[item.code] = { ...item, children: [] }
      })
      // 构建树形结构
      list.forEach((item) => {
        const node = map[item.code]
        if (item.parentCode && item.parentCode !== '') {
          // 如果有父节点，将当前节点添加到父节点的children中
          if (map[item.parentCode]) {
            map[item.parentCode].children.push(node)
          }
        } else {
          // 如果没有父节点，则为根节点
          result.push(node)
        }
      })
      return result
    },
    async getList() {
      this.loading = true
      try {
        const res = await listInstitution(this.queryParams)
        if (res.code === 200) {
          this.fullInstitutionTree = this.convertToTree(res.rows || [])
          // 初始化 institutionList 为顶层节点
          this.institutionList = this.fullInstitutionTree.map(node => ({
            ...node,
            children: null, // 明确告知el-table子节点需要懒加载
            hasChildren: node.children && node.children.length > 0
          }))
        }
      } catch (error) {
        console.error('Failed to fetch institution list:', error)
      } finally {
        this.loading = false
      }
    },
    loadNode(tree, treeNode, resolve) {
      // tree: 当前行的数据
      // treeNode: 当前行的状态信息
      // resolve: 回调函数，用于返回子节点数据
      const parentCode = tree.code
      // 从 fullInstitutionTree 中找到父节点
      const parentNode = this.findNodeInTree(this.fullInstitutionTree, parentCode)
      if (parentNode && parentNode.children && parentNode.children.length > 0) {
        const childrenNodes = parentNode.children.map(child => ({
          ...child,
          children: null, // 明确告知el-table子节点需要懒加载
          hasChildren: child.children && child.children.length > 0
        }))
        resolve(childrenNodes)
      } else {
        resolve([]) // 没有子节点
      }
    },
    // 辅助函数：在完整树中查找节点
    findNodeInTree(tree, code) {
      for (const node of tree) {
        if (node.code === code) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeInTree(node.children, code)
          if (found) {
            return found
          }
        }
      }
      return null
    },
    async handleQuery() {
      // 搜索时，重新加载整个列表，并重置为只显示顶层
      await this.getList()
      // 强制刷新
      this.refreshTable = false
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    resetQuery() {
      this.queryParams = { ...this.queryParams, name: undefined }
      this.getList()
    },
    // 辅助方法：根据code获取节点信息
    getNodeByCode(code) {
      const findNode = (nodes) => {
        for (const node of nodes) {
          if (node.code === code) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children)
            if (found) return found
          }
        }
        return null
      }
      return findNode(this.fullInstitutionTree)
    },
    // 辅助方法：根据parentCode获取父节点名称
    getParentName(parentCode) {
      if (!parentCode) return '根节点'
      const parentNode = this.getNodeByCode(parentCode)
      return parentNode ? parentNode.name : '根节点'
    },
    handleAdd(row) {
      this.resetForm()
      this.open = true
      this.title = row.code ? '新增子机构' : '新增机构'
      if (row.code) {
        this.form.parentCode = row.code
        this.form.parentName = row.name
      } else {
        this.form.parentCode = ''
        this.form.parentName = '根节点'
      }
    },
    handleEdit(row) {
      this.resetForm()
      this.open = true
      this.title = '编辑机构'
      // 从fullInstitutionTree中获取节点信息
      const nodeInfo = this.getNodeByCode(row.code)
      if (nodeInfo) {
        this.form = {
          name: nodeInfo.name,
          shortName: nodeInfo.shortName || '', // 添加简称字段
          aliasName: nodeInfo.aliasName,
          parentCode: nodeInfo.parentCode,
          code: nodeInfo.code,
          parentName: this.getParentName(nodeInfo.parentCode)
        }
      }
    },
    resetForm() {
      this.form = {
        name: '',
        shortName: '', // 重置简称字段
        aliasName: '',
        parentCode: undefined,
        parentName: '根节点'
      }
    },
    async submitForm() {
      this.$refs['form'].validate(async(valid) => {
        if (!valid) return
        const submitData = { ...this.form }
        const isAdding = this.title.indexOf('新增') !== -1
        delete submitData.parentName
        try {
          if (isAdding) {
            delete submitData.code
            await addInstitution(submitData)
          } else { // 编辑
            await editInstitution(submitData)
            this.$message.success('修改成功')
          }
          this.open = false
          await this.getList() // 1. 重新获取完整数据
          this.$message.success(isAdding ? '新增成功' : '修改成功')
          // 强制刷新
          this.refreshTable = false
          this.$nextTick(() => {
            this.refreshTable = true
          })
        } catch (error) {
          console.error('Submit form or refresh logic failed:', error)
          this.$message.error(isAdding ? '新增失败' : '修改失败')
        }
      })
    },
    async handleDelete(row) {
      this.$confirm(`确认删除机构 "${row.name}" 及其所有子机构吗？`, '提示', { // 提示更明确
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await deleteInstitution(row.code)
          await this.getList()
          this.$message.success('删除成功')
          // 强制刷新
          this.refreshTable = false
          this.$nextTick(() => {
            this.refreshTable = true
          })
        } catch (error) {
          console.error('Delete operation or refresh failed:', error)
        }
      }).catch(() => {
        // 用户取消删除
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleImport() {
      this.upload.title = '机构导入'
      this.upload.open = true
    },
    handleExport() {
      this.download(
        '/business/institution/export',
        { ...this.queryParams },
        `institution_${new Date().getTime()}.xlsx`
      )
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      )
      this.getList()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    importTemplate() {
      this.download(
        '/business/institution/importTemplate',
        {},
        `institution_template_${new Date().getTime()}.xlsx`
      )
    },
    // 递归获取所有节点 - 注意：此方法现在应该作用于 fullInstitutionTree
    // 如果仅希望获取已加载并显示的节点，则作用于 institutionList
    // 为保持原意"所有节点"，这里改为遍历 fullInstitutionTree
    getAllRows(tree = this.fullInstitutionTree) {
      const res = []
      const traverse = (nodes) => {
        for (const node of nodes) {
          res.push(node)
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        }
      }
      traverse(tree)
      return res
    },
    // 选中/取消选中某一行时 (懒加载模式下，el-table 会自动处理父子联动，但仅限于已加载的节点)
    handleSelect(selection, row) {
      // 在懒加载模式下，el-table 的选择行为可能已经处理了子节点。
      // 如果需要手动级联选择未加载的节点，会非常复杂。
      // Element UI 的懒加载表格通常不提供开箱即用的对未加载子节点的级联选择。
      // 此处保持简单，依赖 el-table 自身行为。
    },
    // 全选/取消全选 (懒加载下，全选通常只作用于已加载的节点)
    handleSelectAll(selection) {
      // 懒加载模式下的全选行为通常只影响当前已加载的数据。
      // 如果要实现对整个数据集（包括未加载部分）的全选，需要额外逻辑来维护一个独立的选择状态树。
      // 目前保持 el-table 的默认行为。
    },
    handleBatchDelete() {
      const selection = this.$refs.table.selection || []
      if (selection.length === 0) {
        this.$message.warning('请先选择要删除的机构')
        return
      }
      // selection 只包含当前已加载并选中的项
      this.$confirm(`确认删除选中的${selection.length}个机构及其所有子机构吗？`, '提示', { type: 'warning' })
        .then(() => {
          // 如果需要删除所有子孙，需要从 fullInstitutionTree 中找到这些节点及其所有后代
          // 目前仅删除选中的节点
          const codes = selection.map(item => item.code).join(',')
          deleteInstitution(codes).then(() => {
            this.$message.success('删除成功')
            this.getList() // 重新加载数据
          })
        })
    },

    // ========== 机构分组相关方法 ==========

    // 打开机构分组管理弹窗
    async handleInstitutionGroup(row) {
      this.currentInstitutionCode = row.code
      this.currentInstitutionName = row.name
      this.groupDialogTitle = `机构分组管理 - ${row.name}`
      this.groupDialogOpen = true
      await this.getGroupList()
    },

    // 获取机构分组列表
    async getGroupList() {
      this.groupLoading = true
      try {
        const res = await listInstitutionGroup(this.currentInstitutionCode)
        if (res.code === 200) {
          this.groupList = res.rows || []
        }
      } catch (error) {
        console.error('Failed to fetch institution group list:', error)
        this.$message.error('获取分组列表失败')
      } finally {
        this.groupLoading = false
      }
    },

    // 新增分组
    handleAddGroup() {
      this.resetGroupForm()
      this.groupFormTitle = '新增机构分组'
      this.groupForm.institutionCode = this.currentInstitutionCode
      this.groupFormOpen = true
    },

    // 编辑分组
    handleEditGroup(row) {
      this.resetGroupForm()
      this.groupFormTitle = '编辑机构分组'
      this.groupForm = {
        id: row.id,
        institutionCode: this.currentInstitutionCode,
        groupName: row.groupName
      }
      this.groupFormOpen = true
    },

    // 删除分组
    async handleDeleteGroup(row) {
      this.$confirm(`删除分组，会删除该分组下人员分组的关键关系！确认删除分组 "${row.groupName}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await removeInstitutionGroup(row.id)
          this.$message.success('删除成功')
          await this.getGroupList()
        } catch (error) {
          console.error('Delete group failed:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 重置分组表单
    resetGroupForm() {
      this.groupForm = {
        id: '',
        institutionCode: '',
        groupName: ''
      }
      if (this.$refs.groupForm) {
        this.$refs.groupForm.resetFields()
      }
    },

    // 提交分组表单
    async submitGroupForm() {
      this.$refs['groupForm'].validate(async(valid) => {
        if (!valid) return

        const isAdding = !this.groupForm.id
        const submitData = { ...this.groupForm }

        try {
          if (isAdding) {
            delete submitData.id
            await addInstitutionGroup(submitData)
            this.$message.success('新增成功')
          } else {
            await editInstitutionGroup(submitData)
            this.$message.success('修改成功')
          }

          this.groupFormOpen = false
          await this.getGroupList()
        } catch (error) {
          console.error('Submit group form failed:', error)
          this.$message.error(isAdding ? '新增失败' : '修改失败')
        }
      })
    }
  }
}
</script>
