import { directList } from '@/api/yqsignal'
import { getInstitutionTree } from '@/api/yqmonitor/institution'
import { getRegion } from '@/api/yqmonitor/topic'

const state = {
  sourceGroups: [],
  topicDrawerVisible: false,
  institutionList: [],
  regionList: []
}

const mutations = {
  SET_SOURCE_GROUPS: (state, groups) => {
    state.sourceGroups = groups
  },
  SET_TOPIC_DRAWER_VISIBLE: (state, visible) => {
    state.topicDrawerVisible = visible
  },
  SET_INSTITUTION_LIST: (state, list) => {
    state.institutionList = list
  },
  SET_REGION_LIST: (state, list) => {
    state.regionList = list
  }
}

const actions = {
  // 获取信源组列表
  async fetchSourceGroups({ commit, state }) {
    // 如果已经有数据，直接返回
    if (state.sourceGroups.length > 0) {
      return state.sourceGroups
    }

    try {
      const res = await directList({
        page: 1,
        page_size: 100 // 获取所有信源组
      })
      if (res.code === 0) {
        const groups = res.data.list.map(item => ({
          value: item.direct_id,
          label: item.direct_name
        }))
        commit('SET_SOURCE_GROUPS', groups)
        return groups
      }
      return []
    } catch (error) {
      console.error('获取信源组列表失败:', error)
      return []
    }
  },

  // 获取机构树
  async fetchInstitutionTree({ commit, state }) {
    // 如果已经有数据，直接返回
    if (state.institutionList.length > 0) {
      return state.institutionList
    }

    try {
      const res = await getInstitutionTree()
      if (res.code === 200) {
        commit('SET_INSTITUTION_LIST', res.data)
        return res.data
      }
      return []
    } catch (error) {
      console.error('获取机构树失败:', error)
      return []
    }
  },

  // 获取地区列表
  async fetchRegionList({ commit, state }) {
    // 如果已经有数据，直接返回
    if (state.regionList.length > 0) {
      return state.regionList
    }

    try {
      const res = await getRegion()
      if (res.code === 0) {
        commit('SET_REGION_LIST', res.data)
        return res.data
      }
      return []
    } catch (error) {
      console.error('获取地区列表失败:', error)
      return []
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
