<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="this.$store.getters.device === 'mobile'" class="default-theme">
        <!--机构数据-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-input
                v-model="institutionName"
                placeholder="请输入机构名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
            </div>
            <div class="head-container">
              <el-tree
                ref="tree"
                :data="institutionTreeData"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                node-key="code"
                highlight-current
                lazy
                :load="loadInstitutionNode"
                @node-click="handleNodeClick"
              />
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="100">
          <el-col>
            <el-form
              v-show="showSearch"
              ref="queryForm"
              :model="queryParams"
              size="small"
              :inline="true"
              label-width="68px"
            >
              <el-form-item label="用户名称" prop="userName">
                <el-input
                  v-model="queryParams.userName"
                  placeholder="请输入用户名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input
                  v-model="queryParams.phonenumber"
                  placeholder="请输入手机号码"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:user:add']"
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                >新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:user:edit']"
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                >修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:user:remove']"
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                >删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:user:import']"
                  type="info"
                  plain
                  icon="el-icon-upload2"
                  size="mini"
                  @click="handleImport"
                >导入</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:user:export']"
                  type="warning"
                  plain
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                >导出</el-button>
              </el-col>
              <right-toolbar :show-search.sync="showSearch" :columns="columns" @queryTable="getList" />
            </el-row>

            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column v-if="columns[0].visible" key="userId" label="用户编号" align="center" prop="userId" />
              <el-table-column
                v-if="columns[1].visible"
                key="userName"
                label="用户名称"
                align="center"
                prop="userName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[2].visible"
                key="nickName"
                label="用户昵称"
                align="center"
                prop="nickName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[3].visible"
                key="institutionNames"
                label="机构"
                align="center"
                prop="institutionNames"
                :show-overflow-tooltip="true"
              />
              <el-table-column v-if="columns[7].visible" key="warnType" label="通道" align="center">
                <template slot-scope="scope">
                  <span>{{ (dict.type.tyfz_warn_type.find(item => item.value === scope.row.warnType) || {}).label || ''
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="columns[4].visible"
                key="phonenumber"
                label="手机号码"
                align="center"
                prop="phonenumber"
                width="120"
              >
                <template slot-scope="scope">
                  <span>{{ maskPhone(scope.row.phonenumber) }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="columns[5].visible" key="status" label="状态" align="center">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.status"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column v-if="columns[6].visible" label="创建时间" align="center" prop="createTime" width="160">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
                <template v-if="scope.row.userId !== 1" slot-scope="scope">
                  <el-button
                    v-hasPermi="['system:user:edit']"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                  >修改</el-button>
                  <el-button
                    v-hasPermi="['system:user:remove']"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                  >删除</el-button>
                  <el-dropdown
                    v-hasPermi="['system:user:resetPwd', 'system:user:edit']"
                    size="mini"
                    @command="(command) => handleCommand(command, scope.row)"
                  >
                    <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-hasPermi="['system:user:resetPwd']"
                        command="handleResetPwd"
                        icon="el-icon-key"
                      >重置密码</el-dropdown-item>
                      <el-dropdown-item
                        v-hasPermi="['system:user:edit']"
                        command="handleAuthRole"
                        icon="el-icon-circle-check"
                      >分配角色</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属机构" prop="institutionIds">
              <treeselect
                v-model="form.institutionIds"
                :options="institutionOptionsForSelect"
                :show-count="true"
                :multiple="true"
                :searchable="true"
                placeholder="请选择归属机构"
                :normalizer="node => ({ id: node.id, label: node.label, children: node.children || undefined })"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="showInstitutionGroups" :span="12">
            <el-form-item label="机构分组" prop="institutionGroupIds">
              <el-select
                v-model="form.institutionGroupIds"
                multiple
                placeholder="请选择机构分组"
                :loading="loadingGroups"
                clearable
                style="width: 100%"
              >
                <el-option v-for="group in institutionGroups" :key="group.id" :label="group.name" :value="group.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通道" prop="warnType">
              <el-select v-model="form.warnType" placeholder="请选择通道" clearable>
                <el-option
                  v-for="dict in dict.type.tyfz_warn_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-select v-model="form.postIds" multiple placeholder="请选择岗位">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip text-center">
          <div slot="tip" class="el-upload__tip">
            <el-checkbox v-model="upload.updateSupport" :true-label="1" :false-label="0" />是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
          >下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus } from '@/api/system/user'
import { listInstitution, getAllInstitutionGroups } from '@/api/yqmonitor/institution' // 添加 getAllInstitutionGroups 导入
import { getToken } from '@/utils/auth'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

export default {
  name: 'User',
  dicts: ['sys_normal_disable', 'sys_user_sex', 'tyfz_warn_type'],
  components: { Treeselect, Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // --- 机构树相关 ---
      institutionTreeData: [],
      fullInstitutionTree: [],
      institutionOptionsForSelect: undefined,
      institutionName: undefined,
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      // --- 机构分组相关 ---
      institutionGroups: [], // 机构分组列表
      showInstitutionGroups: false, // 是否显示分组下拉
      loadingGroups: false, // 分组加载状态
      // --- END ---
      // 是否显示弹出层
      open: false,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      // 用户导入参数
      upload: {
        open: false,
        title: '',
        isUploading: false,
        updateSupport: 1,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/system/user/importData'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        institutionCode: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `机构`, visible: true, propName: 'institutionNames' },
        { key: 4, label: `通道`, visible: true },
        { key: 5, label: `手机号码`, visible: true },
        { key: 6, label: `状态`, visible: true },
        { key: 7, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '用户名称不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '用户昵称不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: 'blur' }
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
          }
        ],
        phonenumber: [
          { required: true, message: '手机号码不能为空', trigger: 'blur' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        warnType: [
          { required: true, message: '通道不能为空', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    // 根据名称筛选机构树
    institutionName(val) {
      this.$refs.tree.filter(val)
    },
    // 监听机构选择变化，获取分组数据
    'form.institutionIds': {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0) {
          this.loadInstitutionGroups(newVal)
        } else {
          this.resetInstitutionGroups()
        }
      },
      deep: true
    }
  },
  created() {
    this.getInstitutionList()
    this.getList()
    this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.msg
    })
  },
  mounted() {
    console.log('tyfz_warn_type:', this.dict.type.tyfz_warn_type)
  },
  methods: {
    /** 手机号脱敏方法 */
    maskPhone(phone) {
      if (!phone || phone.length !== 11) return phone
      return phone.substr(0, 3) + '****' + phone.substr(7, 4)
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows.map(user => ({
          ...user,
          institutionName: user.dept ? user.dept.deptName : '无'
        }))
        this.total = response.total
        this.loading = false
      }
      )
    },

    /** 将一维数组转换为树形结构 */
    convertToTree(list, idKey = 'code', parentIdKey = 'parentCode', childrenKey = 'children') {
      const map = {}
      const result = []
      const validCodes = new Set(list.map(item => item[idKey]))

      list.forEach((item) => {
        if (item[parentIdKey] && !validCodes.has(item[parentIdKey])) {
          item[parentIdKey] = ''
        }
        map[item[idKey]] = { ...item, [childrenKey]: [] }
      })

      list.forEach((item) => {
        const node = map[item[idKey]]
        if (item[parentIdKey] && map[item[parentIdKey]]) {
          map[item[parentIdKey]][childrenKey].push(node)
        } else {
          result.push(node)
        }
      })
      return result
    },

    /** 查询机构树结构 */
    async getInstitutionList() {
      try {
        const response = await listInstitution({ pageNum: 1, pageSize: 10000 })
        if (response.code === 200) {
          const institutions = response.rows || []
          this.fullInstitutionTree = this.convertToTree(institutions, 'code', 'parentCode', 'children')

          this.institutionTreeData = this.fullInstitutionTree.map(node => ({
            ...node,
            isLeaf: !(node.children && node.children.length > 0)
          }))

          this.institutionOptionsForSelect = this.transformDataForTreeselect(this.fullInstitutionTree)
        } else {
          this.$modal.msgError('获取机构列表失败: ' + response.msg)
          this.fullInstitutionTree = []
          this.institutionTreeData = []
          this.institutionOptionsForSelect = []
        }
      } catch (error) {
        console.error('获取机构列表失败:', error)
        this.$modal.msgError('获取机构列表异常')
        this.fullInstitutionTree = []
        this.institutionTreeData = []
        this.institutionOptionsForSelect = []
      }
    },

    /** el-tree 懒加载节点方法 */
    loadInstitutionNode(node, resolve) {
      if (node.level === 0) {
        return resolve(this.fullInstitutionTree.map(n => ({
          ...n,
          isLeaf: !(n.children && n.children.length > 0)
        })))
      }

      if (node.data && node.data.code) {
        const parentCode = node.data.code
        const parentNode = this.findNodeInTree(this.fullInstitutionTree, parentCode, 'code', 'children')

        if (parentNode && parentNode.children && parentNode.children.length > 0) {
          resolve(parentNode.children.map(child => ({
            ...child,
            isLeaf: !(child.children && child.children.length > 0)
          })))
        } else {
          resolve([])
        }
      } else {
        resolve([])
      }
    },

    /** 辅助函数：在完整树中查找节点 */
    findNodeInTree(tree, value, key = 'code', childrenKey = 'children') {
      for (const node of tree) {
        if (node[key] === value) {
          return node
        }
        if (node[childrenKey] && node[childrenKey].length > 0) {
          const found = this.findNodeInTree(node[childrenKey], value, key, childrenKey)
          if (found) {
            return found
          }
        }
      }
      return null
    },

    /** 转换机构数据以适配 Treeselect */
    transformDataForTreeselect(nodes) {
      if (!nodes || nodes.length === 0) return []
      return nodes.map(node => {
        const transformedNode = {
          id: node.code,
          label: node.name
        }
        if (node.children && node.children.length > 0) {
          transformedNode.children = this.transformDataForTreeselect(node.children)
        }
        return transformedNode
      })
    },

    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 节点单击事件
    handleNodeClick(data) {
      if (data.code) {
        this.queryParams.institutionCode = data.code
      } else {
        this.queryParams.institutionCode = undefined
      }

      this.handleQuery()
    },

    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function() {
        return changeUserStatus(row.userId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        row.status = row.status === '0' ? '1' : '0'
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    /** 加载机构分组数据 */
    async loadInstitutionGroups(institutionIds) {
      if (!institutionIds || institutionIds.length === 0) {
        this.resetInstitutionGroups()
        return
      }

      this.loadingGroups = true
      try {
        // 调用接口获取机构分组
        const response = await getAllInstitutionGroups(institutionIds)
        if (response.code === 0 && response.data && response.data.length > 0) {
          this.institutionGroups = response.data.map(group => ({
            id: group.id,
            name: group.groupName,
            institutionCode: group.institutionCode
          }))
          this.showInstitutionGroups = true
        } else {
          this.resetInstitutionGroups()
        }
      } catch (error) {
        console.error('获取机构分组失败:', error)
        this.resetInstitutionGroups()
      } finally {
        this.loadingGroups = false
      }
    },

    /** 重置机构分组相关数据 */
    resetInstitutionGroups() {
      this.institutionGroups = []
      this.showInstitutionGroups = false
      this.form.institutionGroupIds = []
    },

    /** 表单重置 */
    reset() {
      this.form = {
        userId: undefined,
        institutionIds: [],
        institutionGroupIds: [], // 添加分组字段
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: '0',
        remark: undefined,
        postIds: [],
        roleIds: [],
        warnType: undefined
      }
      this.resetInstitutionGroups() // 重置分组相关数据
      this.resetForm('form')
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.institutionCode = undefined
      this.$refs.tree.setCurrentKey(null)
      this.institutionName = undefined
      this.handleQuery()
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },

    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleAuthRole':
          this.handleAuthRole(row)
          break
        default:
          break
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      getUser().then(response => {
        this.postOptions = response.posts
        this.roleOptions = response.roles
        this.open = true
        this.title = '添加用户'
        this.form.password = this.initPassword
      })
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const userId = row.userId || this.ids
      getUser(userId).then(response => {
        this.form = response.data
        this.postOptions = response.posts
        this.roleOptions = response.roles
        this.$set(this.form, 'postIds', response.postIds || [])
        this.$set(this.form, 'roleIds', response.roleIds || [])
        this.$set(this.form, 'institutionIds', response.institutionIds || [])
        this.$set(this.form, 'institutionGroupIds', response.institutionGroupIds || []) // 设置分组数据
        this.$set(this.form, 'warnType', response.data.warnType || undefined)
        this.open = true
        this.title = '修改用户'
        this.form.password = ''
      })
    },

    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
        inputValidator: (value) => {
          if (/<|>|"|'|\||\\/.test(value)) {
            return "不能包含非法字符：< > \" ' \\\ |"
          }
        }
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess('修改成功，新密码是：' + value)
        })
      }).catch(() => { })
    },

    /** 分配角色操作 */
    handleAuthRole: function(row) {
      const userId = row.userId
      this.$router.push('/system/user-auth/role/' + userId)
    },

    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 拷贝表单数据
          const submitData = { ...this.form }
          // institutionGroups 字段赋值
          submitData.institutionGroups = this.form.institutionGroupIds || []
          // 删除 institutionGroupIds 字段
          delete submitData.institutionGroupIds

          if (this.form.userId != undefined) {
            updateUser(submitData).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addUser(submitData).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids
      this.$modal.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？').then(function() {
        return delUser(userIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },

    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped>
.app-container {
  background-color: #fff;
}
</style>
