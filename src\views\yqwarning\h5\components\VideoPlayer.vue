<template>
  <div>
    <div
      v-show="visible"
      class="video-drawer-overlay"
      @click="handleClose"
    />
    <div
      class="video-drawer"
      :class="{ 'video-drawer-open': visible }"
    >
      <div class="video-drawer-header">
        <div class="drawer-title">视频播放</div>
        <button class="close-btn" @click="handleClose">
          <i class="el-icon-close" />
        </button>
      </div>
      <div class="video-drawer-content" @click.stop>
        <video
          v-if="videoSrc"
          ref="drawerVideo"
          :src="videoSrc"
          controls
          autoplay
          class="drawer-video"
          @loadedmetadata="onVideoLoaded"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoPlayer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    videoSrc: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      videoCache: null
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        document.body.style.overflow = 'hidden'
        if (this.videoCache) {
          this.$nextTick(() => {
            if (this.$refs.drawerVideo) {
              this.$refs.drawerVideo.play()
            }
          })
        }
      } else {
        document.body.style.overflow = 'auto'
        if (this.$refs.drawerVideo) {
          this.$refs.drawerVideo.pause()
          this.$refs.drawerVideo.currentTime = 0
        }
      }
    }
  },
  beforeDestroy() {
    document.body.style.overflow = 'auto'
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    onVideoLoaded() {
      this.videoCache = this.$refs.drawerVideo

      this.$nextTick(() => {
        if (this.$refs.drawerVideo) {
          const video = this.$refs.drawerVideo
          const videoAspectRatio = video.videoWidth / video.videoHeight

          const screenWidth = window.innerWidth
          const maxVideoHeight = window.innerHeight * 0.6
          const calculatedHeight = screenWidth / videoAspectRatio

          const finalHeight = Math.min(calculatedHeight, maxVideoHeight)
          video.style.height = finalHeight + 'px'
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.video-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-drawer-overlay.show {
    opacity: 1;
}

.video-drawer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-height: 80vh;
    overflow: hidden;

    &.video-drawer-open {
        transform: translateY(0);
    }
}

.video-drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;

    .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f0f0f0;
        }
    }
}

.video-drawer-content {
    padding: 0;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.drawer-video {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
    background: #000;
    display: block;
}

@media (max-width: 768px) {
    .video-drawer {
        max-height: 70vh;
    }

    .video-drawer-header {
        padding: 12px 16px;

        .drawer-title {
            font-size: 14px;
        }
    }
}
</style>
