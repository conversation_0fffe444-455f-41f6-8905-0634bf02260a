<template>
  <div class="result-item-wrapper">
    <div class="content-area">
      <div v-if="item.readStatus === 1" class="read-stamp">
        <span>已读</span>
      </div>
      <header class="item-header">
        <el-avatar :size="40" :src="item.avatar" class="author-avatar"></el-avatar>
        <div class="author-details" @click="goToDetail" style="cursor: pointer;">
          <div class="author-line">
            <span class="item-index">{{ item.id }}、</span>
            <span class="author-name">{{ item.author }}</span>
          </div>
          <div class="tags-line">
            <el-tag v-for="tag in item.tags" :key="tag" type="info" size="mini">{{ tag }}</el-tag>
            <span v-if="item.location" class="location-info">
              <i class="el-icon-location-outline"></i> {{ item.location }}
            </span>
          </div>
        </div>
        <div class="meta-details">
          <div class="meta-top-row">
            <el-tag :type="getSentimentTagType(item.sentiment)" size="mini" class="sensitivity-tag">
              {{ item.sentiment }}
            </el-tag>
            <div class="source-info">
              <span v-if="item.source === '头条' || item.source === '今日头条'" class="source-icon toutiao">头</span>
              <span v-else-if="item.source === '微博'" class="source-icon weibo">微</span>
              <span class="source-name">{{ item.source }}</span>
            </div>
          </div>
          <div class="timestamp">{{ item.timestamp }}</div>
        </div>
      </header>
      <main class="item-body">
        <div v-if="item.summary" class="summary-content" v-html="highlightKeywords(item.summary, highlightWords)"></div>
        <div v-if="item.mainContent" class="main-content" v-html="highlightKeywords(item.mainContent, highlightWords)"></div>
        <div v-if="item.quote" class="quote-block">
          <div class="quote-author-line">
            <span class="quote-author-avatar"><i class="el-icon-user-solid"></i></span> {{ item.quote.author }} / {{ item.quote.timestamp }}
          </div>
          <div class="quote-content" v-html="highlightKeywords(item.quote.content, highlightWords)"></div>
        </div>
      </main>
      <footer class="item-footer">
        <div class="footer-left">
          <el-tag v-if="item.original" size="mini" class="original-tag">原创</el-tag>
          <span class="action-item">阅读 {{ item.view_count || 0 }}</span>
          <span class="action-item"><i class="el-icon-thumb"></i> {{ item.like_count || 0 }}</span>
          <span class="action-item"><i class="el-icon-chat-dot-round"></i> {{ item.comment_count || 0 }}</span>
          <span class="action-item"><i class="el-icon-share"></i> {{ item.share_count || 0 }}</span>
        </div>

      </footer>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ResultItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    highlightWords: {
      type: Array,
      default: () => []
    }
  },

  methods: {
    highlightKeywords(text, keywords) {
      if (!text || !keywords || keywords.length === 0) return text;
      const escapedKeywords = keywords.map(kw => kw.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));
      const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'g');
      return text.replace(regex, `<span class="highlight">$1</span>`);
    },
    getSentimentTagType(sentiment) {
      switch (sentiment) {
        case '敏感':
          return 'danger';
        case '非敏感':
          return 'success';
        case '中性':
          return 'info';
        default:
          return 'info';
      }
    },
    goToDetail() {
      if (this.item.unity_id) {
        window.open(`/comprehensivesearch/detail/${this.item.unity_id}`, '_blank');
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.highlight {
  color: #dd4b39;
}
.result-item-wrapper {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  font-size: 14px;
  .content-area {
    position: relative;
  }
  .read-stamp {
    position: absolute;
    top: 40px;
    right: 260px;
    width: 70px;
    height: 70px;
    border: 2px solid #67C23A;
    color: #67C23A;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(-20deg);
    opacity: 0.6;
    font-size: 24px;
    font-weight: bold;
    pointer-events: none;
    z-index: 1;
  }

  .item-header {
    display: flex;
    align-items: flex-start;
    .author-avatar {
      margin-top: 5px;
    }
    .author-details {
      flex: 1;
      margin-left: 10px;
      transition: all 0.2s ease;
      &:hover {
        .author-line .author-name {
          color: #409EFF;
        }
      }
      .author-line {
        font-size: 16px;
        margin-bottom: 5px;
        .author-name {
          font-weight: 500;
          transition: color 0.2s ease;
        }
      }
      .tags-line {
        display: flex;
        align-items: center;
        gap: 5px;
        .location-info {
          color: #909399;
          font-size: 12px;
          margin-left: 5px;
        }
      }
    }
    .meta-details {
      text-align: right;
      color: #909399;
      font-size: 12px;
      .meta-top-row {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 15px;
        margin-bottom: 5px;
      }
      .sensitivity-tag {
        margin-right: 0;
      }
      .source-info {
        display: flex;
        align-items: center;
        gap: 4px;
        .source-icon {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          color: white;
          font-size: 12px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          &.toutiao { background-color: #f85959; }
          &.weibo { background-color: #e6162d; }
        }
      }
      .timestamp {
        margin-bottom: 8px;
      }
    }
  }
  .item-body {
    margin: 15px 0;
    line-height: 1.6;
    .summary-content, .main-content {
      color: #606266;
    }
    .quote-block {
      background-color: #f7f7f7;
      padding: 10px 15px;
      border-radius: 4px;
      margin-top: 10px;
      .quote-author-line {
        color: #909399;
        margin-bottom: 8px;
        .quote-author-avatar {
          background-color: #e4e7ed;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          margin-right: 5px;
          i { color: #909399; }
        }
      }
      .quote-content {
        color: #606266;
      }
    }
  }
  .item-footer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #909399;
    font-size: 13px;
    .footer-left {
      display: flex;
      align-items: center;
      gap: 15px;
      .original-tag {
        background-color: #fff;
        border-color: #dcdfe6;
        color: #909399;
      }
      .action-item {
        cursor: pointer;
        &:hover { color: #409EFF; }
        i { margin-right: 4px; }
      }
    }
  }
}
</style>