<template>
  <div class="contact-selector">
    <div class="selected-contacts">
      <div style="font-weight: 500; margin-bottom: 8px; color: var(--secondary-color);">
        已选联系人 <span style="color: var(--info-color); font-size: 13px">({{ selectedContacts.length }})</span>
      </div>

      <!-- 分组标签逻辑 -->
      <div
        v-if="selectionGroupTags.length > 0"
        class="institution-group-tags"
        style="margin-bottom: 10px; display:flex; flex-wrap:wrap; gap:8px;"
      >
        <el-tag
          v-for="tagInfo in selectionGroupTags"
          :key="tagInfo.id"
          closable
          type="info"
          style="cursor: pointer;"
          effect="light"
          size="small"
          @close="handleRemoveGroup(tagInfo.id)"
        >
          <!-- 显示格式：机构名 - 分组名 -->
          {{ tagInfo.institutionName }} - {{ tagInfo.name }}
        </el-tag>
      </div>

      <!-- 已选联系人列表 -->
      <template v-if="selectedContacts.length > 0">
        <el-tag
          v-for="contact in selectedContacts"
          :key="contact.contactId"
          closable
          class="contact-tag"
          @close="handleRemoveContact(contact)"
        >
          {{ contact.name }} ({{ contact.institutionName }})
        </el-tag>
      </template>
      <div v-else style="color: #c0c4cc; text-align: center; padding: 10px 0">
        <span v-if="selectedInstitutions.length > 0">当前机构(含上级)无可选联系人或已被移除。</span>
        <span v-else>暂无联系人</span>
      </div>
    </div>
    <div
      v-if="shouldShowInstitutionTip"
      style="
        color: #e6a23c;
        font-size: 13px;
        margin-top: 10px;
        padding: 8px;
        background: #fdf6ec;
        border-radius: 4px;
      "
    >
      <i class="el-icon-info" />
      请先选择机构，系统将自动预填相关联系人
    </div>
    <el-button
      type="primary"
      plain
      icon="el-icon-plus"
      style="width: 100%; margin-top: 15px"
      @click="handleAdd"
    >
      {{ buttonText }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'ContactSelector',
  props: {
    selectedContacts: {
      type: Array,
      default: () => []
    },
    selectedInstitutions: {
      type: Array,
      default: () => []
    },
    pageMode: {
      type: String,
      default: 'normal'
    },
    institutionMode: {
      type: String,
      default: 'institution'
    }
  },
  computed: {
    /**
     * 根据已选联系人，生成顶部的分组标签
     */
    selectionGroupTags() {
      if (this.selectedContacts.length === 0) return []

      const groups = new Map()
      this.selectedContacts.forEach(contact => {
        // 确保联系人对象上有 groupId 属性
        if (contact.groupId) {
          if (!groups.has(contact.groupId)) {
            groups.set(contact.groupId, {
              id: contact.groupId, // 分组的唯一ID
              name: contact.groupName, // 分组的名称
              institutionName: contact.institutionName // 分组所属的机构名
            })
          }
        }
      })

      return Array.from(groups.values())
    },
    shouldShowInstitutionTip() {
      // 根据不同页面模式和机构模式决定是否显示提示
      if (this.pageMode === 'report') {
        return false // report 模式下不显示机构提示
      } else {
        return (this.pageMode === 'normal' || this.pageMode === 'summary') &&
               !this.selectedInstitutions.length &&
               this.institutionMode === 'institution'
      }
    },
    buttonText() {
      if (this.pageMode === 'report') {
        return '选择联系人' // report 模式下固定显示"选择联系人"
      } else {
        return (this.pageMode === 'normal' || this.pageMode === 'summary') && this.selectedInstitutions.length ? '补充选择联系人' : '选择联系人'
      }
    }
  },
  methods: {
    handleRemoveContact(contact) {
      this.$emit('remove-contact', contact)
    },
    handleRemoveGroup(groupId) {
      this.$emit('remove-group', groupId)
    },
    handleAdd() {
      this.$emit('add')
    }
  }
}
</script>

<style scoped lang="scss">
.contact-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background: #fff;
}

.selected-contacts {
  min-height: 150px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background: #fafafa;
}

.contact-tag {
  margin: 0 8px 8px 0;
  background: #ecf5ff;
  border-color: #d9ecff;
  color: var(--primary-color);
  display: inline-flex;
  align-items: center;

  ::v-deep .el-icon-close {
    color: var(--primary-color);

    &:hover {
      background-color: var(--primary-color);
      color: white;
    }
  }
}

/* CSS变量定义 */
:root {
  --primary-color: #001ff8;
  --secondary-color: #2c3e50;
  --info-color: #909399;
  --border-color: #ebeef5;
}
</style>
