import request from '@/utils/request'

/**
 * 查询过滤词列表
 * @param {Object} query
 * @param {number} query.userId - 用户ID
 * @param {number} query.menuId - 菜单ID
 * @returns {Promise} 返回过滤词列表
 */
export function getFilterWordList(query) {
  return request({
    url: '/system/param/list',
    method: 'get',
    params: query
  })
}

/**
 * 新增过滤词
 * @param {Object} data
 * @param {number} data.userId - 用户ID
 * @param {string} data.name - 名称
 * @param {number} data.menuId - 菜单ID
 * @param {string} data.paramJson - 筛选词json
 * @returns {Promise} 返回操作结果
 */
export function addFilterWord(data) {
  return request({
    url: '/system/param/add',
    method: 'post',
    data: data
  })
}

/**
 * 修改过滤词
 * @param {Object} data
 * @param {number} data.userId - 用户ID
 * @param {string} data.name - 名称
 * @param {number} data.menuId - 菜单ID
 * @param {string} data.paramJson - 筛选词json
 * @returns {Promise} 返回操作结果
 */
export function updateFilterWord(data) {
  return request({
    url: '/system/param/edit',
    method: 'post',
    data: data
  })
}
