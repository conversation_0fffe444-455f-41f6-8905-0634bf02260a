<template>
  <el-aside width="200px">
    <el-scrollbar style="height: 95%;">
      <div class="menu-wrapper">
        <el-tree
          ref="tree"
          :data="currentDisplayMenu"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="true"
          :current-node-key="currentKey"
          highlight-current
          :default-expanded-keys="expandedKeys"
          draggable
          :allow-drop="allowDrop"
          @node-drag-start="handleNodeDragStart"
          @node-drop="handleNodeDrop"
          @node-drag-end="handleNodeDragEnd"
        >
          <template slot-scope="{ node, data }">
            <component
              :is="getNodeComponent(data.type)"
              ref="treeNode"
              :node="node"
              :data="data"
              :is-tree-currently-dragging="isTreeDragging"
              @command="handleCommand"
              @module-click="handleModuleNodeClick"
              @category-click="handleCategoryNodeClick"
            />
          </template>
        </el-tree>
      </div>
    </el-scrollbar>
  </el-aside>
</template>

<script>
import ModuleNode from './ModuleNode.vue'
import CategoryNode from './CategoryNode.vue'
import TopicNode from './TopicNode.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'SideMenu',
  components: {
    ModuleNode,
    CategoryNode,
    TopicNode
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['menuList'])
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentKey: null,
      expandedKeys: [],
      currentDisplayMenu: [],
      isTreeDragging: false,
      localStorageKey: 'sideMenuOrder'
    }
  },
  watch: {
    editNodeId(newVal) {
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    menuList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          let orderedList = JSON.parse(JSON.stringify(newVal))
          const savedOrder = this.loadOrderFromLocalStorage()
          if (savedOrder) {
            orderedList = this.applyStoredOrderToData(orderedList, savedOrder)
          }
          this.currentDisplayMenu = orderedList
          // 默认展开第一个节点
          this.expandedKeys = [newVal[0].id]
        } else {
          this.currentDisplayMenu = []
        }
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
    if (this.currentDisplayMenu && this.currentDisplayMenu.length > 0) {
      if (!this.$store.state.yqmonitorMenu.currentNode || !this.findNodeById(this.currentDisplayMenu, this.$store.state.yqmonitorMenu.currentNode.id)) {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', this.currentDisplayMenu[0])
        this.currentKey = this.currentDisplayMenu[0].id
      } else {
        this.currentKey = this.$store.state.yqmonitorMenu.currentNode.id
      }
    }
  },
  methods: {
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node
        if (node.children) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    getNodeComponent(type) {
      const componentMap = {
        module: 'ModuleNode',
        category: 'CategoryNode',
        topic: 'TopicNode'
      }
      return componentMap[type]
    },
    handleCommand({ command, data, value }) {
      if (this.isTreeDragging) return
      switch (command) {
        case 'addTopic':
          this.addTopic(data)
          break
        case 'editTopic':
          this.editTopic(data)
          break
      }
    },
    addTopic(node) {
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
        ...node,
        isAddingTopic: true
      })
      this.$store.commit('yqmonitorTopic/SET_TOPIC_DRAWER_VISIBLE', true)
    },
    editTopic(node) {
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', node)
      this.$store.commit('yqmonitorTopic/SET_TOPIC_DRAWER_VISIBLE', true)
    },
    handleModuleNodeClick(node) {
      if (this.isTreeDragging) return
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', node)
      this.currentKey = node.id
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(node.id)
      })
    },
    handleCategoryNodeClick(node) {
      if (this.isTreeDragging) return
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
        ...node,
        isEditing: false
      })
      this.currentKey = node.id
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(node.id)
      })
    },
    handleNodeDragStart(node, event) {
      this.isTreeDragging = true
    },
    handleNodeDrop(draggingNode, dropNode, dropType, ev) {
      this.$nextTick(() => {
        this.saveOrderToLocalStorage(this.currentDisplayMenu)
      })
    },
    handleNodeDragEnd(draggingNode, dropNode, dropType, ev) {
      this.isTreeDragging = false
    },
    allowDrop(draggingNode, dropNode, type) {
      const draggingData = draggingNode.data
      const dropData = dropNode.data

      // 1. 基本的防御性检查 (确保 parent 和 id 存在)
      if (!dropNode || !draggingData || !dropData ||
                !draggingNode.parent || !dropNode.parent ||
                !draggingNode.parent.data || !dropNode.parent.data ||
                typeof draggingNode.parent.data.id === 'undefined' ||
                typeof dropNode.parent.data.id === 'undefined' ||
                typeof draggingData.id === 'undefined' ||
                typeof dropData.id === 'undefined') {
        console.log('allowDrop: Missing critical data (node, data, parent, id).', { draggingNode, dropNode, type })
        return false
      }

      // console.log(`ALLOW_DROP: Dragging ${draggingData.label}(${draggingData.type}), Drop on ${dropData.label}(${dropData.type}), Type: ${type}, DragParentID: ${draggingNode.parent.data.id}, DropParentID: ${dropNode.parent.data.id}`);

      // 2. 'module' 类型的节点不能被拖动。
      if (draggingData.type === 'module') {
        console.log(false)
        return false
      }

      // 3. 对于“仅兄弟节点换位”的需求，不允许任何 'inner' 类型的放置。
      //    这样，当鼠标悬停在目标节点中间时，不会有任何指示器，这是符合预期的。
      if (type === 'inner') {
        return false
      }

      // 4. 现在 type 只能是 'prev' 或 'next'。
      //    检查拖拽节点和目标节点是否为同类型的兄弟节点。
      //    并且，不能拖放到自身。
      if (draggingData.id === dropData.id) {
        console.log(false)
        return false // 不能放到自己身上
      }

      // 检查是否是相同类型的节点并且拥有相同的父节点
      const isSameType = draggingData.type === dropData.type
      const isSameParent = draggingNode.parent.data.id === dropNode.parent.data.id

      if (isSameType && isSameParent) {
        // 对于 'category' 和 'topic'，只要是同类型、同父级，就允许 'prev' 或 'next'
        if (draggingData.type === 'category' || draggingData.type === 'topic') {
          console.log(true)
          return true
        }
      }
      console.log(false)
      // 5. 其他所有情况均不允许。
      return false
    },
    generateOrderMap(nodes) {
      const orderMap = {}
      orderMap['root_modules'] = nodes.filter(n => n.type === 'module').map(n => n.id)
      const traverse = (currentNodes) => {
        currentNodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            orderMap[node.id] = node.children.map(child => child.id)
            traverse(node.children)
          }
        })
      }
      traverse(nodes)
      return orderMap
    },
    saveOrderToLocalStorage(menuData) {
      if (!menuData) return
      const orderMap = this.generateOrderMap(menuData)
      localStorage.setItem(this.localStorageKey, JSON.stringify(orderMap))
    },
    loadOrderFromLocalStorage() {
      const storedOrder = localStorage.getItem(this.localStorageKey)
      return storedOrder ? JSON.parse(storedOrder) : null
    },
    applyStoredOrderToData(data, storedOrderMap) {
      if (!storedOrderMap) return data
      if (storedOrderMap['root_modules']) {
        data.sort((a, b) => {
          const aIndex = storedOrderMap['root_modules'].indexOf(a.id)
          const bIndex = storedOrderMap['root_modules'].indexOf(b.id)
          if (aIndex === -1 && bIndex === -1) return 0
          if (aIndex === -1) return 1
          if (bIndex === -1) return -1
          return aIndex - bIndex
        })
      }
      const sortChildren = (nodesToSort) => {
        if (!nodesToSort) return
        nodesToSort.forEach(node => {
          if (node.children && node.children.length > 0 && storedOrderMap[node.id]) {
            node.children.sort((a, b) => {
              const aIndex = storedOrderMap[node.id].indexOf(a.id)
              const bIndex = storedOrderMap[node.id].indexOf(b.id)
              if (aIndex === -1 && bIndex === -1) return 0
              if (aIndex === -1) return 1
              if (bIndex === -1) return -1
              return aIndex - bIndex
            })
            sortChildren(node.children)
          }
        })
      }
      sortChildren(data)
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.el-aside {
    background-color: #2A3143;
    border-right: 1px solid #e6e6e6;
    margin-bottom: 0;
    padding-left: 0;
    padding-right: 0;

    .menu-wrapper {
        padding: 0;
        color: #fff;
    }

    ::v-deep .el-tree {
        background-color: transparent;
        color: inherit;

        .el-tree-node {
            &:focus>.el-tree-node__content {
                background-color: #F2F3F7;
                color: #5090f1;

                .custom-tree-node {
                    .el-dropdown-link {
                        color: #5090f1;
                    }

                    .node-input {
                        color: #5090f1;
                    }
                }

            }

            &__content {
                height: 48px;

                &:hover {
                    background-color: #F2F3F7;
                    color: #5090f1;
                }
            }

            &.is-current>.el-tree-node__content {
                background-color: #F2F3F7;
                color: #5090f1;

                .custom-tree-node .el-dropdown-link {
                    color: #5090f1;
                }

                .custom-tree-node>.node-content>.node-input {
                    color: #5090f1;
                }
            }

            .custom-tree-node .node-label {
                max-width: 118px;
            }
        }

        .el-tree-node__content:hover .custom-tree-node .el-dropdown-link {
            color: #5090f1;
        }

        .el-tree-node__content:hover>.custom-tree-node>.node-content>.node-input {
            color: #5090f1;
        }

        &>.el-tree-node>.el-tree-node__content {
            background-color: #222e44;
            border: 1px solid #2f4b77;
            color: #ddd;
            height: 48px;
            line-height: 48px;
            margin-top: 10px;
        }
    }

    ::v-deep .el-tree-node__expand-icon {
        display: none !important;
    }
}

.el-scrollbar {
    ::v-deep {
        .el-scrollbar__wrap {
            overflow-x: hidden;
        }
    }
}
</style>
