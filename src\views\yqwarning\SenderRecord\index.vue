<template>
  <div class="sender-record-container">
    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="80px">
        <!-- 常规模糊搜索行 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="机构">
              <el-input
                v-model="queryParams.institutionQuery"
                placeholder="机构名称模糊查询"
                clearable
                style="width: 100%"
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="接收人">
              <el-input
                v-model="queryParams.receiverQuery"
                placeholder="接收人姓名模糊查询"
                clearable
                style="width: 100%"
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发送人">
              <el-input
                v-model="queryParams.senderQuery"
                placeholder="发送人姓名模糊查询"
                clearable
                style="width: 100%"
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 其他筛选条件行 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发送时间">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                size="mini"
                :default-time="['00:00:00', '23:59:59']"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预警内容">
              <el-input
                v-model="queryParams.contentQuery"
                placeholder="预警内容关键词"
                clearable
                style="width: 100%"
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox
                v-model="queryParams.hasSmsFailure"
                true-label="1"
                false-label=""
                size="mini"
                style="margin-right: 15px;"
                @change="handleQuery"
              >仅显示发送失败</el-checkbox>
              <el-checkbox
                v-model="queryParams.onlyMyInstitution"
                true-label="1"
                false-label="0"
                size="mini"
                style="margin-right: 15px;"
                @change="handleQuery"
              >仅看本级</el-checkbox>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"> 查询 </el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"> 重置 </el-button>
              <el-button type="text" size="mini" @click="openAdvancedSearchDialog">高级筛选</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 高级筛选条件展示区 -->
        <div v-if="hasAdvancedFilters" class="advanced-filter-display">
          <span>高级筛选已激活:</span>
          <el-tag
            v-for="item in selectedInstitutionLabels"
            :key="`inst-${item.value}`"
            closable
            @close="removeAdvancedFilter('institutionCodes', item.value)"
          >
            机构: {{ item.label }}
          </el-tag>
          <el-tag
            v-for="item in selectedReceiverLabels"
            :key="`recv-${item.value}`"
            closable
            @close="removeAdvancedFilter('receiverIds', item.value)"
          >
            接收人: {{ item.label }}
          </el-tag>
          <el-tag
            v-for="item in selectedSenderLabels"
            :key="`send-${item.value}`"
            closable
            @close="removeAdvancedFilter('senderIds', item.value)"
          >
            发送人: {{ item.label }}
          </el-tag>
          <el-button type="text" icon="el-icon-delete" @click="clearAdvancedFilters">清除高级筛选</el-button>
        </div>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['business:smsMsg:remove']"
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="selectedRows.length === 0"
              @click="handleDelete"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 表格数据区域 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="序号" width="40" align="center" />
        <el-table-column label="预警内容" align="left" header-align="center">
          <template slot-scope="scope">
            <div class="content-column">
              <div class="content-text" :title="scope.row.content">
                {{ scope.row.content }}
              </div>
              <el-button
                v-if="scope.row.content && scope.row.content.length > 50"
                type="text"
                size="mini"
                @click="showFullContent(scope.row.content)"
              >
                更多
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="institutions" label="机构" width="100" align="center" show-overflow-tooltip />
        <el-table-column prop="suspectedInstitution" label="疑似机构" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="风险等级" width="50" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.riskLevel" :type="getRiskLevelType(scope.row.riskLevel)" size="mini">
              {{ scope.row.riskLevel }}
            </el-tag>
            <span v-else>——</span>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="200" align="left" show-overflow-tooltip header-align="center" />
        <el-table-column prop="comeFrom" label="来源" width="120" align="center" />
        <el-table-column label="接收人" align="left" header-align="center">
          <template slot-scope="scope">
            <div class="receiver-column">
              <div class="receiver-text" :title="scope.row.receivers">
                {{ scope.row.receivers }}
              </div>
              <el-button v-if="scope.row.receivers" type="text" size="mini" @click="showReceiverDetails(scope.row)">
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="原文时间" width="100" align="center" />
        <el-table-column prop="sendTime" label="发送时间" width="100" align="center" />
        <el-table-column prop="sender" label="发送人" width="60" align="center" />
        <el-table-column label="内容属性" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini">{{ scope.row.contentType }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 内容详情弹窗 -->
    <el-dialog title="预警内容详情" :visible.sync="contentDetailVisible" width="60%">
      <div class="content-detail">{{ selectedContent }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="copyContent">复 制</el-button>
        <el-button @click="contentDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 接收人详情弹窗 -->
    <el-dialog title="接收人详情" :visible.sync="receiverDetailVisible" width="40%" append-to-body>
      <el-form
        ref="receiverQueryForm"
        :model="receiverQueryParams"
        :inline="true"
        label-width="80px"
        size="mini"
        @submit.native.prevent
      >
        <el-form-item label="发送状态">
          <el-select
            v-model="receiverQueryParams.smsStatus"
            placeholder="请选择状态"
            clearable
            @change="handleReceiverFilter"
          >
            <el-option label="全部" value="" />
            <el-option label="发送成功" value="1" />
            <el-option label="发送失败" value="2" />
            <el-option label="未知" value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="receiverDetailLoading"
        :data="receiverDetailData"
        border
        style="width: 100%"
        :row-class-name="receiverTableRowClassName"
      >
        <el-table-column prop="userName" label="姓名" align="center" />
        <el-table-column prop="smsType" label="接收途径" align="center" />
        <el-table-column prop="smsStatus" label="发送状态" align="center" />
      </el-table>
      <div v-if="receiverTotal > 0" class="pagination-container">
        <el-pagination
          :current-page="receiverQueryParams.pageNum"
          :page-sizes="[10, 20, 50]"
          :page-size="receiverQueryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="receiverTotal"
          @size-change="handleReceiverSizeChange"
          @current-change="handleReceiverCurrentChange"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="receiverDetailVisible = false">关闭</el-button>
        <el-button type="primary" @click="refreshSmsStatus">刷新状态</el-button>
      </div>
    </el-dialog>
    <!-- 高级筛选对话框 -->
    <el-dialog title="高级筛选" :visible.sync="advancedSearchVisible" width="60%">
      <el-form :model="advancedQueryParams" label-width="100px">
        <el-form-item label="精确选择机构">
          <el-select
            v-model="advancedQueryParams.institutionCodes"
            multiple
            filterable
            placeholder="请选择机构"
            style="width: 100%"
            size="mini"
          >
            <el-option v-for="item in allInstitutions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="精确选择接收人">
          <el-select
            v-model="advancedQueryParams.receiverIds"
            multiple
            filterable
            placeholder="请选择接收人"
            style="width: 100%"
            size="mini"
          >
            <el-option v-for="item in allUsers" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="精确选择发送人">
          <el-select
            v-model="advancedQueryParams.senderIds"
            multiple
            filterable
            placeholder="请选择发送人"
            style="width: 100%"
            size="mini"
          >
            <el-option v-for="item in allUsers" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级">
          <el-select
            v-model="advancedQueryParams.riskLevels"
            multiple
            placeholder="请选择风险等级"
            style="width: 100%"
            size="mini"
          >
            <el-option label="高" value="1" />
            <el-option label="中" value="2" />
            <el-option label="低" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容属性">
          <el-select
            v-model="advancedQueryParams.contentTypes"
            multiple
            placeholder="请选择内容属性"
            style="width: 100%"
            size="mini"
          >
            <el-option label="预警" value="1" />
            <el-option label="汇总" value="2" />
            <el-option label="舆情早晚报" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="接收途径">
          <el-select
            v-model="advancedQueryParams.warnType"
            placeholder="请选择接收途径"
            style="width: 100%"
            clearable
            size="mini"
          >
            <el-option label="全部" value="1" />
            <el-option label="短信" value="2" />
            <el-option label="企业微信" value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="advancedSearchVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirmAdvancedSearch">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate"
          >下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getWarnMsgList, importSmsMsg, getSmsUserInfos, deleteWarnMsg } from '@/api/yqwarning/index'
import { getInstitutionUserTree } from '@/api/yqmonitor/institution'
import { flattenTreeData } from '../sender/helper'
import { getToken } from '@/utils/auth'

export default {
  name: 'SenderRecord',
  dicts: ['tyfz_warn_type'],
  data() {
    return {
      // 主查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        timeRange: [],
        institutionQuery: '',
        receiverQuery: '',
        senderQuery: '',
        contentQuery: '',
        hasSmsFailure: '',
        institutionCodes: [],
        riskLevels: [],
        warnType: '',
        receiverIds: [],
        senderIds: [],
        contentTypes: [],
        onlyMyInstitution: ''
      },
      // 高级筛选弹窗的临时参数
      advancedQueryParams: {
        institutionCodes: [],
        receiverIds: [],
        senderIds: [],
        riskLevels: [],
        contentTypes: [],
        warnType: ''
      },
      tableData: [],
      total: 0,
      loading: false,
      selectedRows: [],
      // 高级筛选所需元数据
      allInstitutions: [],
      allUsers: [],
      // 弹窗控制
      contentDetailVisible: false,
      receiverDetailVisible: false,
      advancedSearchVisible: false,
      selectedContent: '',
      // 接收人详情
      receiverDetailData: [],
      receiverDetailLoading: false,
      receiverTotal: 0,
      receiverQueryParams: {
        pageNum: 1,
        pageSize: 10,
        warnMsgId: null,
        smsStatus: ''
      },
      // 导入参数
      upload: {
        open: false,
        title: '',
        isUploading: false,
        url: process.env.VUE_APP_BASE_API + '/business/smsMsg/import',
        headers: { Authorization: 'Bearer ' + getToken() }
      }
    }
  },
  computed: {
    // 计算是否有高级筛选条件，用于显示提示区
    hasAdvancedFilters() {
      return this.queryParams.institutionCodes.length > 0 ||
        this.queryParams.receiverIds.length > 0 ||
        this.queryParams.senderIds.length > 0
    },
    // 计算已选机构的标签
    selectedInstitutionLabels() {
      return this.queryParams.institutionCodes.map(code => {
        const found = this.allInstitutions.find(i => i.value === code)
        return found ? { value: code, label: found.label } : { value: code, label: `Code:${code}` }
      })
    },
    // 计算已选接收人的标签
    selectedReceiverLabels() {
      return this.queryParams.receiverIds.map(id => {
        const found = this.allUsers.find(u => u.value === id)
        return found ? { value: id, label: found.name } : { value: id, label: `ID:${id}` }
      })
    },
    // 计算已选发送人的标签
    selectedSenderLabels() {
      return this.queryParams.senderIds.map(id => {
        const found = this.allUsers.find(u => u.value === id)
        return found ? { value: id, label: found.name } : { value: id, label: `ID:${id}` }
      })
    }
  },
  created() {
    this.loadFilterOptions() // 加载高级筛选所需数据
    this.getList()
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.hasSmsFailure == 1) { // 使用 '==' 弱等于来同时处理数字1和字符串'1'
        return 'failure-row'
      }
      return ''
    },
    // 获取列表数据
    getList() {
      this.loading = true
      const params = { ...this.queryParams }
      if (params.timeRange && params.timeRange.length === 2) {
        params.startTime = params.timeRange[0]
        params.endTime = params.timeRange[1]
      }
      delete params.timeRange
      Object.keys(params).forEach(key => {
        const value = params[key]
        if (value === null || value === '' || (Array.isArray(value) && value.length === 0)) {
          delete params[key]
        }
      })
      getWarnMsgList(params).then(response => {
        this.tableData = response.rows || []
        this.total = response.total || 0
      }).finally(() => {
        this.loading = false
      })
    },
    // 加载筛选器所需的元数据 (用于高级筛选)
    async loadFilterOptions() {
      try {
        const response = await getInstitutionUserTree()
        if (response.code === 200 && response.data) {
          const flattenedData = flattenTreeData(response.data)
          this.allInstitutions = flattenedData.map(inst => ({ label: inst.label, value: inst.id }))
          const userMap = new Map()
          flattenedData.forEach(inst => {
            if (inst.contacts && inst.contacts.length > 0) {
              inst.contacts.forEach(contact => {
                if (!userMap.has(contact.userId)) {
                  userMap.set(contact.userId, {
                    label: `${contact.userName} (${inst.name})`,
                    value: contact.userId,
                    name: contact.userName
                  })
                }
              })
            }
          })
          this.allUsers = Array.from(userMap.values())
        }
      } catch (error) {
        console.error('加载筛选器选项失败:', error)
      }
    },
    // 打开高级筛选对话框
    openAdvancedSearchDialog() {
      // 将主查询参数同步到弹窗的临时参数中
      this.advancedQueryParams.institutionCodes = [...this.queryParams.institutionCodes]
      this.advancedQueryParams.receiverIds = [...this.queryParams.receiverIds]
      this.advancedQueryParams.senderIds = [...this.queryParams.senderIds]
      this.advancedQueryParams.riskLevels = [...this.queryParams.riskLevels]
      this.advancedQueryParams.contentTypes = [...this.queryParams.contentTypes]
      this.advancedQueryParams.warnType = this.queryParams.warnType
      this.advancedSearchVisible = true
    },
    // 确认高级筛选
    handleConfirmAdvancedSearch() {
      // 将弹窗中的选择应用到主查询参数
      this.queryParams.institutionCodes = this.advancedQueryParams.institutionCodes
      this.queryParams.receiverIds = this.advancedQueryParams.receiverIds
      this.queryParams.senderIds = this.advancedQueryParams.senderIds
      this.queryParams.riskLevels = this.advancedQueryParams.riskLevels
      this.queryParams.contentTypes = this.advancedQueryParams.contentTypes
      this.queryParams.warnType = this.advancedQueryParams.warnType
      this.advancedSearchVisible = false
      this.handleQuery() // 应用后立即执行查询
    },
    // 移除单个高级筛选标签
    removeAdvancedFilter(key, value) {
      const index = this.queryParams[key].indexOf(value)
      if (index > -1) {
        this.queryParams[key].splice(index, 1)
        this.handleQuery() // 移除后立即查询
      }
    },
    // 清除所有高级筛选条件
    clearAdvancedFilters() {
      this.queryParams.institutionCodes = []
      this.queryParams.receiverIds = []
      this.queryParams.senderIds = []
      this.handleQuery()
    },
    // 查询操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: this.queryParams.pageSize, // 保留分页大小
        timeRange: [],
        institutionQuery: '',
        receiverQuery: '',
        senderQuery: '',
        contentQuery: '',
        hasSmsFailure: '',
        institutionCodes: [],
        riskLevels: [],
        warnType: '',
        receiverIds: [],
        senderIds: [],
        contentTypes: [],
        onlyMyInstitution: ''
      }
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    handleSelectionChange(selection) { this.selectedRows = selection },
    handleSizeChange(val) { this.queryParams.pageSize = val; this.getList() },
    handleCurrentChange(val) { this.queryParams.pageNum = val; this.getList() },
    getRiskLevelType(levelText) { return { '高': 'danger', '中': 'warning', '低': 'success' }[levelText] || 'info' },
    showFullContent(content) { this.selectedContent = content; this.contentDetailVisible = true },

    // 复制内容
    copyContent() {
      if (!this.selectedContent) {
        this.$modal.msgError('没有内容可复制')
        return
      }
      navigator.clipboard.writeText(this.selectedContent).then(() => {
        this.$modal.msgSuccess('复制成功')
      }).catch(err => {
        this.$modal.msgError('复制失败，请手动复制')
        console.error('无法复制文本: ', err)
      })
    },

    // 删除操作
    handleDelete() {
      const msgIds = this.selectedRows.map(item => item.smsId)
      if (msgIds.length === 0) {
        this.$modal.msgWarning('请选择要删除的数据')
        return
      }
      this.$modal.confirm('是否确认删除选中的' + msgIds.length + '条数据项？').then(() => {
        return deleteWarnMsg({ msgIds: msgIds })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },

    // ================== 接收人详情相关方法 ==================
    showReceiverDetails(row) {
      if (!row.smsId) {
        this.$modal.msgError('无法获取预警信息ID')
        return
      }
      this.receiverQueryParams.warnMsgId = row.smsId
      this.receiverQueryParams.pageNum = 1
      this.receiverQueryParams.smsStatus = '' // 打开时重置筛选
      this.receiverDetailVisible = true
      this.getReceiverList()
    },
    getReceiverList() {
      this.receiverDetailLoading = true
      getSmsUserInfos(this.receiverQueryParams).then(response => {
        // 定义映射
        const smsTypeMap = this.dict.type.tyfz_warn_type.reduce((acc, item) => {
          acc[item.value] = item.label
          return acc
        }, {})
        const smsStatusMap = { '1': '发送成功', '2': '发送失败', '3': '未知' }
        // 只保留后端有的字段，并做映射
        this.receiverDetailData = (response.rows || []).map(item => ({
          userName: item.userName,
          smsType: smsTypeMap[item.smsType] || item.smsType,
          smsStatus: smsStatusMap[item.smsStatus] || item.smsStatus,
          rawSmsStatus: item.smsStatus // 保留原始状态用于高亮判断
        }))
        this.receiverTotal = response.total || 0
        this.receiverDetailLoading = false
      }).catch(() => {
        this.receiverDetailLoading = false
      })
    },
    handleReceiverSizeChange(val) {
      this.receiverQueryParams.pageSize = val
      this.getReceiverList()
    },
    handleReceiverCurrentChange(val) {
      this.receiverQueryParams.pageNum = val
      this.getReceiverList()
    },
    handleReceiverFilter() {
      this.receiverQueryParams.pageNum = 1
      this.getReceiverList()
    },
    receiverTableRowClassName({ row }) {
      // '2' 表示发送失败
      if (row.rawSmsStatus == '2') {
        return 'failure-row'
      }
      return ''
    },
    refreshSmsStatus() {
      this.$message.info('正在刷新状态...')
      this.getReceiverList()
    },

    // ================== 导出/导入相关方法 ==================
    handleExport() {
      // 1. 创建一个新的参数对象，避免修改原始queryParams
      const exportParams = { ...this.queryParams }

      // 2. 处理时间范围参数，与getList()方法保持一致
      if (exportParams.timeRange && exportParams.timeRange.length === 2) {
        exportParams.startTime = exportParams.timeRange[0]
        exportParams.endTime = exportParams.timeRange[1]
      }
      delete exportParams.timeRange // 删除多余的timeRange属性

      // 3. 清理空值参数，与getList()方法保持一致
      Object.keys(exportParams).forEach(key => {
        const value = exportParams[key]
        if (value === null || value === '' || (Array.isArray(value) && value.length === 0)) {
          delete exportParams[key]
        }
      })

      this.$modal.confirm('是否确认导出所有符合条件的短信发送记录数据项？').then(() => {
        this.download('/business/smsMsg/export',
          exportParams, // 使用处理过后的干净参数
          `短信发送记录_${new Date().getTime()}.xlsx`
        )
      }).catch(() => { })
    },
    handleImport() {
      this.upload.title = '预警信息导入'
      this.upload.open = true
    },
    importTemplate() {
      window.location.href = 'https://jc.tyfzyuqing.com/download/importYjxx.xlsx'
    },
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>
<style scoped>
.sender-record-container {
  padding: 20px;
}

.filter-section {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
}

.content-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-text {
  flex: 1;
  margin-right: 10px;
  /* --- 以下是实现多行截断的核心代码 --- */
  display: -webkit-box;
  /* 启用 WebKit 的弹性盒子布局 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-line-clamp: 5;
  /* 限制在一个块元素显示的文本的行数，这里设置为5行 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分用省略号显示 */
  word-break: break-all;
  /* 允许在单词内换行，防止长英文或数字不换行 */
  white-space: pre-wrap;
}

.receiver-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.receiver-text {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
  margin-right: 10px;
}

.content-detail {
  padding: 20px;
  line-height: 1.8;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.advanced-filter-display {
  margin-top: 10px;
  padding: 10px;
  background-color: #f4f4f5;
  border-radius: 4px;
}

.advanced-filter-display .el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
  /* 适应多行情况 */
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-table .failure-row {
  background: #fef0f0 !important;
}

::v-deep .el-table .failure-row:hover>td {
  background-color: #fce1e1 !important;
}
</style>
