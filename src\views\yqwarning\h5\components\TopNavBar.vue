<template>
  <div class="top-nav">
    <div class="nav-container">
      <div class="nav-tabs">
        <!-- 调整后的导航项 -->
        <div class="nav-tab" :class="{ active: activeTab === 'info-detail' }" @click="handleTabClick('info-detail')">
          信息详情
        </div>
        <div class="nav-tab" :class="{ active: activeTab === 'account-info' }" @click="handleTabClick('account-info')">
          账号画像
        </div>
        <div
          class="nav-tab"
          :class="{ active: activeTab === 'similar-info' }"
          @click="handleTabClick('similar-info')"
        >
          相似信息
        </div>
        <!-- <div class="nav-tab" :class="{ active: activeTab === 'hot-searches' }"
          @click="handleTabClick('hot-searches')">
          相关热搜
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['activeTab'],
  methods: {
    handleTabClick(tab) {
      this.$emit('tab-click', tab)
    }
  }
}
</script>

<style scoped lang="scss">
.top-nav {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #1e5799, #207cca);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  .nav-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    .nav-tabs {
      display: flex;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 30px;
      padding: 4px;
      width: 100%;
      .nav-tab {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 30px;
        cursor: pointer;
        transition: all 0.3s;
        flex-grow: 1;
        text-align: center;
        &.active {
          background: white;
          color: #1e5799;
          font-weight: bold;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 媒体查询保持不变
@media (max-width: 480px) {
    .top-nav .nav-container {
        padding: 0 10px;
        .nav-tabs .nav-tab {
            padding: 6px 12px;
            font-size: 13px;
        }
    }
}
@media (max-width: 375px) {
    .top-nav .nav-container .nav-tabs .nav-tab {
        padding: 5px 8px; // 针对4个tab，微调padding
        font-size: 12px;
    }
}
</style>
