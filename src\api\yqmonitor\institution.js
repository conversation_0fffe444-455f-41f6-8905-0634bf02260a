import request from '@/utils/request'

// 新增机构
export function addInstitution(data) {
  return request({
    url: '/business/institution/addInstitution',
    method: 'post',
    data: data
  })
}

// 批量删除机构
export function deleteInstitution(codes) {
  return request({
    url: '/business/institution/delete/' + codes,
    method: 'get'
  })
}

// 修改机构信息
export function editInstitution(data) {
  return request({
    url: '/business/institution/editInstitution',
    method: 'post',
    data: data
  })
}

// 获取机构详细信息
export function getInstitutionInfo(code) {
  return request({
    url: '/business/institution/getInstitutionInfo/' + code,
    method: 'get'
  })
}

// 机构列表查询
export function listInstitution(query) {
  return request({
    url: '/business/institution/list',
    method: 'get',
    params: query
  })
}

// 获取机构树形结构
export function getInstitutionTree() {
  return request({
    url: '/business/institution/tree',
    method: 'get'
  })
}

// 获取机构用户关联树
export function getInstitutionUserTree() {
  return request({
    url: '/business/institution/treeWithUser',
    method: 'get'
  })
}

// 发送预警消息
export function sendWarnMsg(data) {
  return request({
    url: '/business/warnSms/sendWarnMsg',
    method: 'post',
    data: data
  })
}

// ==================== 机构分组相关接口 ====================

// 机构分组列表查询
export function listInstitutionGroup(institutionCode) {
  return request({
    url: '/business/institutionGroup/list',
    method: 'get',
    params: { institutionCode }
  })
}

// 新增机构分组
export function addInstitutionGroup(data) {
  return request({
    url: '/business/institutionGroup/addInstitutionGroup',
    method: 'post',
    data: data
  })
}

// 修改机构分组
export function editInstitutionGroup(data) {
  return request({
    url: '/business/institutionGroup/editInstitutionGroup',
    method: 'post',
    data: data
  })
}

// 删除机构分组
export function removeInstitutionGroup(id) {
  return request({
    url: '/business/institutionGroup/remove/' + id,
    method: 'get'
  })
}

// 根据机构code集合获取所有机构分组
export function getAllInstitutionGroups(institutionCodes) {
  return request({
    url: '/business/institutionGroup/getAllInstitutionGroups',
    method: 'post',
    data: { institutionCodes },
    headers: {
      repeatSubmit: false
    }
  })
}

// 调用后端AI服务进行智能分析
export function getAIAnalysisFromBackend(data) {
  return request({
    url: '/coze/analyze', // 根据文档图片，使用此接口地址
    method: 'post',
    data: data
  })
}
