<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: this.fileList.length >= this.limit }"
    >
      <i class="el-icon-plus" />
    </el-upload>
    <div v-if="showTip" slot="tip" class="el-upload__tip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>
    <el-dialog :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto">
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import { isExternal } from '@/utils/validate'
export default {
  props: {
    // 新增 action 属性，用于自定义上传地址
    action: {
      type: String,
      default: '/common/upload'
    },
    limit: {
      type: Number,
      default: 5
    },
    fileSize: {
      type: Number,
      default: 5
    },
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    isShowTip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      // 使用 prop 初始化上传URL
      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action,
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      realFileList: []
    }
  },
  computed: {
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  methods: {
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }
      if (!isImg) {
        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join('/')}图片格式文件!`)
        return false
      }
      if (file.name.includes(',')) {
        this.$modal.msgError('文件名不正确，不能包含英文逗号!')
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$modal.loading('正在上传图片，请稍候...')
      this.number++
    },
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 更新：兼容新旧两种API返回格式
    handleUploadSuccess(res, file) {
      if (res.code === 0) {
        let uploadItem
        const data = res.data || res
        // 兼容新接口格式 { code: 200, data: { path, fileName, size } } 或 { code: 200, path, ... }
        if (data.path && data.fileName) {
          const originalPath = data.path
          // 直接替换，无需判断，必然有
          const displayUrl = originalPath.replace(/\/home\/<USER>\//, '/yqmsImg/')
          uploadItem = {
            name: data.fileName,
            url: displayUrl, // <-- 使用转换后的 URL 用于显示
            originalPath: originalPath, // <-- 存储原始路径用于提交
            size: data.size !== undefined ? data.size : file.size
          }
        }
        // 兼容旧接口格式 { code: 200, fileName }
        else if (res.fileName) {
          uploadItem = {
            name: res.fileName,
            url: res.fileName,
            size: file.size
          }
        } else {
          this.number--
          this.$modal.closeLoading()
          this.$modal.msgError('上传成功但返回数据格式不正确')
          this.$refs.imageUpload.handleRemove(file)
          return
        }
        this.uploadList.push(uploadItem)
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.imageUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },
    // 更新：增加 on-change 事件抛出
    handleDelete(file) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name)
      if (findex > -1) {
        // 从"数据源"中删除
        this.realFileList.splice(findex, 1)
        // el-upload 已经从界面上移除了，我们这里可以再同步一下 fileList 保证一致性
        this.fileList = [...this.realFileList]
        this.$emit('on-change', this.fileList)
      }
    },
    handleUploadError() {
      this.$modal.msgError('上传图片失败，请重试')
      this.$modal.closeLoading()
    },
    // 更新：增加 on-change 事件抛出
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        // 1. 将新上传的、格式正确的图片列表，追加到我们的"数据源"中
        this.realFileList = this.realFileList.concat(this.uploadList)
        // 2. 基于更新后的"数据源"，创建一个全新的副本，强制更新 el-upload 的显示
        //    el-upload 会再次修改这个新副本，但没关系，我们的 realFileList 是安全的。
        this.fileList = [...this.realFileList]
        this.uploadList = []
        this.number = 0
        this.$emit('on-change', this.fileList)
        this.$modal.closeLoading()
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep.hide .el-upload--picture-card {
  display: none;
}

::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
</style>
