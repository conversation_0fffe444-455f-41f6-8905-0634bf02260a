<template>
  <el-drawer
    title="添加信源"
    :visible.sync="visible"
    direction="rtl"
    size="1200px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form :model="form" label-width="100px" class="search-form">
        <div class="form-row">
          <el-form-item label="平台">
            <el-select v-model="form.platform" placeholder="请选择平台" size="small" @change="handleSearch">
              <el-option
                v-for="item in platforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="form.keyword"
              placeholder="请输入关键词"
              size="small"
            >
            <el-button slot="append" icon="el-icon-search" size="small" @click="handleSearch" />
            </el-input>
          </el-form-item>
          <el-form-item label="用户ID">
            <el-input
              v-model="form.user_id"
              placeholder="请输入用户ID"
              size="small"
            >
              <el-button slot="append" icon="el-icon-search" size="small" @click="handleSearch" />
            </el-input>
          </el-form-item>
          <el-form-item label="主域名">
            <el-input
              v-model="form.main_domain"
              placeholder="请输入主域名"
              size="small"
            >
              <el-button slot="append" icon="el-icon-search" size="small" @click="handleSearch" />
            </el-input>
          </el-form-item>
        </div>
      </el-form>

      <div class="batch-add">
        <el-button
          type="primary"
          size="small"
          :disabled="selectedRows.length === 0"
          @click="handleBatchAdd"
        >批量添加</el-button>
      </div>

      <div class="table-container">
        <el-table
          v-loading="loading"
          element-loading-text="加载中..."
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="nickname" label="账号昵称" />
          <el-table-column prop="user_id" label="用户ID" />
          <el-table-column prop="main_domain" label="主域名" />
          <el-table-column prop="platform" label="平台" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="handleAdd(scope.row)">添加</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { searchUser } from '@/api/yqsignal'

export default {
  name: 'SignalForm',
  props: {
    platforms: {
      type: Array,
      required: true
    },
    directId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        platform: '',
        keyword: '',
        user_id: '',
        main_domain: ''
      },
      tableData: [],
      selectedRows: [],
      page: 1,
      pageSize: 20,
      total: 0
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.handleSearch()
      }
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    handleClose() {
      this.resetForm()
      this.visible = false
    },
    resetForm() {
      this.form = {
        platform: '',
        keyword: '',
        user_id: '',
        main_domain: ''
      }
      this.page = 1
      this.pageSize = 20
      this.tableData = []
      this.selectedRows = []
    },
    async handleSearch() {
      this.loading = true
      try {
        const params = {
          ...this.form,
          direct_id: this.directId,
          page: 1, // 查询的时候 当前页要重置为1
          page_size: this.pageSize
        }
        const res = await searchUser(params)
        if (res.code === 0) {
          this.tableData = res.data.list || []
          this.total = res.data.total || 0
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败')
      } finally {
        this.loading = false
      }
    },
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    handleAdd(row) {
      this.$emit('add-signal', row)
      this.visible = false
      this.$message.success('添加成功')
    },
    async handleBatchAdd() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要添加的信源')
        return
      }
      // 一次性发送所有选中的信源
      this.$emit('add-signal', this.selectedRows)
      this.visible = false
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.page = val
      this.handleSearch()
    }
  }
}
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}
.table-container {
  margin-top: 20px;
}
.search-form {
 border-bottom: 1px solid #E4E7ED;
 padding-bottom: 20px;
}
.form-row {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
}
.form-row .el-form-item {
  margin-bottom: 0;
  flex: 1;
}
.form-row .el-select {
  width: 100%;
}
.drawer-content {
  padding: 0;
  padding-right: 20px;
}
.batch-add {
  padding: 15px 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
