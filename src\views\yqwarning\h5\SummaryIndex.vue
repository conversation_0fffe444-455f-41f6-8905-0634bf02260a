<template>
  <div class="summary-detail-page">
    <!-- Tabs -->
    <div class="top-nav">
      <div class="nav-container">
        <div class="nav-tabs">
          <div class="nav-tab" :class="{ active: activeTab === 'detail' }" @click="activeTab = 'detail'">
            汇总详情
          </div>
          <div class="nav-tab" :class="{ active: activeTab === 'trend' }" @click="activeTab = 'trend'">
            热度走势
          </div>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="tab-content">
        <div v-show="activeTab === 'detail'">
          <!-- 上：显示机构、时间 -->
          <div class="content-upper">
            <div v-if="institutions && institutions.length > 0" class="meta-line">
              机构：{{ institutions.join('、') }}
            </div>
            <div class="meta-line">
              <span>{{ sendTime }}</span>
            </div>
          </div>

          <!-- 下：舆情汇总(标题) 具体的内容 -->
          <div class="content-lower">
            <div class="section-title">舆情汇总</div>
            <div class="analysis-box">
              <!-- <p>{{ summaryContent }}</p> -->
              <p v-html="summaryContent"></p>
            </div>
          </div>
        </div>

        <div v-show="activeTab === 'trend'">
          <div v-if="imageList && imageList.length > 0" class="image-list">
            <!-- 使用 Element UI 的 Image 组件进行预览 -->
            <el-image v-for="(item, index) in imageList" :key="index" :src="getImageUrl(item.filePath)"
              :preview-src-list="previewImageList" fit="cover" class="summary-image">
              <!-- 加载时的占位内容 -->
              <div slot="placeholder" class="image-slot">
                <i class="el-icon-loading" />
              </div>
              <!-- 加载失败时的占位内容 -->
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
                <span>图片加载失败</span>
              </div>
            </el-image>
          </div>
          <div v-else class="no-images">无相关图片</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSmsMsgInfoByMsgId } from '@/api/h5'

export default {
  name: 'SummaryIndex',
  data() {
    return {
      activeTab: 'detail',
      summaryContent: '',
      sendTime: '',
      institutions: [],
      imageList: []
    }
  },
  computed: {
    previewImageList() {
      return this.imageList.map(item => this.getImageUrl(item.filePath))
    }
  },
  created() {
    this.fetchSummaryDetail()
  },
  methods: {
    fetchSummaryDetail() {
      const msgId = this.$route.params.id
      if (!msgId) {
        this.$message.error('无效的消息ID')
        return
      }
      getSmsMsgInfoByMsgId(msgId).then(response => {
        if (response.code === 0 && response.data) {
          this.sendTime = response.data.sendTime || ''
          this.institutions = response.data.institutions || []
          this.imageList = response.data.imgList || []

          const content = response.data.content || '无内容'
          const linkRegex = /\n\n?点击查看更多：.*/
          this.summaryContent = this.wrapLlink(content.replace(linkRegex, '').trim())
        } else {
          this.$message.error(response.msg || '获取汇总详情失败')
          this.summaryContent = '请稍后重试。'
        }
      }).catch(err => {
        console.error('获取汇总详情失败:', err)
        this.$message.error('请求失败，请检查网络连接')
        this.summaryContent = '请稍后重试。'
      })
    },
    getImageUrl(path) {
      if (!path) return ''
      if (path.startsWith('http')) {
        return path
      }
      const cleanPath = path.startsWith('/') ? path : '/' + path
      const url = window.location.origin + cleanPath
      return url
    },
    /**
     * 将文本中的url包裹成超链接
     * @param content 
     */
    wrapLlink(content) {
      if (!content) return ''
      let htmlContent = content;

      // 1. 转义 HTML 特殊字符，防止 XSS
      htmlContent = htmlContent
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

      // 2. 将换行符 \n 转换为 <br> 标签
      htmlContent = htmlContent.replace(/\n/g, '<br>');

      // 3. 使用正则表达式查找 URL 并替换为 <a> 标签
        const urlRegex = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi
      htmlContent = htmlContent.replace(urlRegex, function(url) {
        // url 是原始匹配，此时它不包含任何 '<' 或 '>' 字符，可以安全地放入 href
        return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`
      })

      return htmlContent
    }
  }
}
</script>

<style scoped lang="scss">
.summary-detail-page {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.container {
  flex-grow: 1;
  width: 95%;
  max-width: 900px;
  margin: 15px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 20px;
  overflow: hidden;
}

.top-nav {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #1e5799, #207cca);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  .nav-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
    display: flex;
    justify-content: center;
    align-items: center;

    .nav-tabs {
      display: flex;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 30px;
      padding: 4px;
      width: 100%;

      .nav-tab {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 30px;
        cursor: pointer;
        transition: all 0.3s;
        flex-grow: 1;
        text-align: center;

        &.active {
          background: white;
          color: #1e5799;
          font-weight: bold;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

.tab-content {
  margin-top: 10px;
}

.content-upper {
  padding-bottom: 15px;

  .summary-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
  }

  .meta-line {
    font-size: 13px;
    color: #909399;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.content-lower {
  border-top: 1px solid #ebeef5;

  .section-title {
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    margin: 10px 0 15px 0;
    padding-left: 12px;
    border-left: 4px solid #1e5799;
  }

  .analysis-box {
    background: #f0f7ff;
    border-radius: 12px;
    padding: 18px;
    border-left: 4px solid #1e5799;

    p {
      font-size: 15px;
      color: #333;
      line-height: 1.7;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-image {
  width: 100%;
  height: auto;
  min-height: 180px;
  max-width: 100%;
  border-radius: 8px;
  cursor: pointer;
  background-color: #f0f2f5;
  object-fit: cover;
  border: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-slot {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 14px;

  i {
    font-size: 30px;
    margin-bottom: 8px;
  }

  span {
    font-size: 12px;
  }
}

.el-icon-loading {
  animation: loading-rotate 2s linear infinite;
}

.no-images {
  color: #909399;
  font-size: 14px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 480px) {
  .container {
    width: 100%;
    margin: 0;
    padding: 15px;
    border-radius: 0;
    box-shadow: none;
  }

  .content-upper .summary-title {
    font-size: 18px;
  }

  .content-lower .section-title {
    font-size: 16px;
  }

  .content-lower .analysis-box p {
    font-size: 14px;
  }

  .image-list {
    gap: 10px;
  }

  .summary-image {
    min-height: 120px;
  }

  .no-images {
    font-size: 13px;
    padding: 15px;
  }

  .top-nav .nav-container {
    padding: 0 10px;

    .nav-tabs .nav-tab {
      padding: 6px 12px;
      font-size: 13px;
    }
  }
}

@media (max-width: 375px) {
  .top-nav .nav-container .nav-tabs .nav-tab {
    padding: 5px 8px;
    font-size: 12px;
  }
}
</style>
