import request from '@/utils/request'

// 获取预警信息详情(H5)
export function getWarnMsgDetail(params) {
  return request({
    url: '/h5/getSmsMsgInfoByPostUniqueId',
    method: 'post',
    data: params
  })
}
// 根据 warnMsgId 获取普通预警信息
export function getSmsMsgInfoByMsgId(warnMsgId) {
  return request({
    url: '/h5/getSmsMsgInfoByMsgId',
    method: 'post',
    data: { warnMsgId }
  })
}
// 获取舆情列表(H5)（原getFoldedPublicSentimentList，方法名改为getPublicSentimentList）
export function getPublicSentimentList(query) {
  // 安全校验：必须有enableSimilarityDedup === 'fold'
  if (!query || query.enableSimilarityDedup !== 'fold') {
    throw new Error('参数缺少 enableSimilarityDedup === "fold"，不允许调用getFoldedPublicSentimentList')
  }
  const url = '/h5/getFoldedPublicSentimentList'
  const paramsForApi = { ...query }
  delete paramsForApi.enableSimilarityDedup
  return request({
    url,
    method: 'post',
    data: paramsForApi
  })
}

// 获取舆情详情(H5)
export function getPublicSentimentById(id) {
  return request({
    url: `/h5/getPublicSentimentById/${id}`,
    method: 'get'
  })
}

// 查询不同账号，similarId相同且为相似主数据的数据，再按热度排序(H5)
export function getTopUsersBySimilarId(data) {
  return request({
    url: '/h5/getTopUsersBySimilarId',
    method: 'post',
    data: data
  })
}
