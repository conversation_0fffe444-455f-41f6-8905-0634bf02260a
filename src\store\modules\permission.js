import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout-new/index";
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
    firstRoute: "",
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_FIRST_ROUTE: (state, route) => {
      state.firstRoute = route;
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes);
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      state.topbarRouters = routes;
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes;
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes({ commit }) {
      return new Promise((resolve) => {
        // 向后端请求路由数据
        getRouters().then((res) => {
          const sdata = JSON.parse(JSON.stringify(res.data));
          const rdata = JSON.parse(JSON.stringify(res.data));
          const sidebarRoutes = filterAsyncRouter(sdata);
          const rewriteRoutes = filterAsyncRouter(rdata, false, true);
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
          rewriteRoutes.push({ path: "*", redirect: "/404", hidden: true });
          router.addRoutes(asyncRoutes);
          commit("SET_ROUTES", rewriteRoutes);
          commit("SET_SIDEBAR_ROUTERS", constantRoutes.concat(sidebarRoutes));
          commit("SET_DEFAULT_ROUTES", sidebarRoutes);
          commit("SET_TOPBAR_ROUTES", sidebarRoutes);
          const firstRoute = findFirstValidRoute(res.data);
          if (firstRoute) {
            commit("SET_FIRST_ROUTE", firstRoute);
          }

          resolve(rewriteRoutes);
        });
      });
    },
  },
};

/**
 * 解析路径
 * @param {string} basePath 基础路径
 * @param {string} routePath 路由路径
 * @returns {string} 解析后的完整路径
 */
function resolvePath(basePath, routePath) {
  // 如果 routePath 已经是绝对路径，直接返回
  if (/^(httpsμί:)\/\//.test(routePath) || routePath.startsWith("/")) {
    return routePath;
  }
  // 拼接路径，并处理多余的斜杠
  return `${basePath}/${routePath}`.replace(/\/+/g, "/");
}

/**
 * 递归查找第一个有效的、可访问的路由
 * @param {Array} routes 路由列表
 * @returns {string | null} 第一个有效路由的完整路径，或者 null
 */
function findFirstValidRoute(routes) {
  // 需要排除的业务菜单路由路径（注意：这里应该是路由对象中的 path 属性值）
  const excludedPaths = [
    "yqmonitor-business",
    "followed-events-business",
    "target-monitor-business",
  ];

  // 定义一个递归的深度优先搜索函数
  function find(currentRoutes, basePath) {
    for (const route of currentRoutes) {
      // 1. 如果路由本身在排除列表里，则跳过它和它的所有子路由
      if (excludedPaths.includes(route.path)) {
        continue;
      }

      // 2. 如果路由被设置为隐藏，也跳过
      if (route.hidden) {
        continue;
      }

      // 构建当前检查的完整路径
      const currentPath = resolvePath(basePath, route.path);

      // 3. 如果它有子路由，优先在子路由中寻找
      if (route.children && route.children.length > 0) {
        const firstChildPath = find(route.children, currentPath);
        // 如果在子路由中找到了，立即返回结果
        if (firstChildPath) {
          return firstChildPath;
        }
      }

      // 4. 如果没有子路由或子路由里没找到有效项，
      //    检查当前路由自身是否是一个可访问的“叶子”页面
      //    (通常，Layout 和 ParentView 是容器，而不是最终页面)
      if (
        route.component &&
        route.component !== "Layout" &&
        route.component !== "ParentView"
      ) {
        return currentPath;
      }
    }

    // 在当前层级没有找到任何有效路由
    return null;
  }

  // 从顶层开始搜索，基础路径为空字符串
  return find(routes, "");
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else if (route.component === "InnerLink") {
        route.component = InnerLink;
      } else {
        route.component = loadView(route.component);
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = [];
  childrenMap.forEach((el) => {
    el.path = lastRouter ? lastRouter.path + "/" + el.path : el.path;
    if (el.children && el.children.length && el.component === "ParentView") {
      children = children.concat(filterChildren(el.children, el));
    } else {
      children.push(el);
    }
  });
  return children;
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = [];
  routes.forEach((route) => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route);
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route);
      }
    }
  });
  return res;
}

export const loadView = (view) => {
  if (process.env.NODE_ENV === "development") {
    return (resolve) => require([`@/views/${view}`], resolve);
  } else {
    // 使用 import 实现生产环境的路由懒加载
    return () => import(`@/views/${view}`);
  }
};

export default permission;
