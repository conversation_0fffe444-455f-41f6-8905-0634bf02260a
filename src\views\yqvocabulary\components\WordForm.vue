<template>
  <el-dialog :title="title" :visible.sync="visible" width="600px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="词汇" prop="wordText">
        <el-input v-model="form.wordText" type="textarea" :placeholder="`请输入词汇，多个词汇请用空格分隔`" />
      </el-form-item>
      <!-- 仅在排除词库时显示分组选择 -->
      <el-form-item v-if="isExcludeWord" label="所属分组" prop="groupIds">
        <el-select v-model="form.groupIds" multiple placeholder="请选择分组" style="width: 100%;">
          <el-option
            v-for="group in groupList"
            :key="group.groupId"
            :label="group.groupName"
            :value="group.groupId"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { batchAddDictionaryWord, updateDictionaryWord, getDictionaryWordDetail, getDictionaryGroupsByCategory } from '@/api/yqvocabulary';

export default {
  name: "WordForm",
  props: {
    categoryInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 弹窗可见性
      visible: false,
      // 弹窗标题
      title: '',
      // 表单数据
      form: {},
      // 分组列表 (用于排除词)
      groupList: [],
      // 表单校验规则
      rules: {
        wordText: [
          { required: true, message: "词汇不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    isExcludeWord() {
      return this.categoryInfo.categoryCode === 'exclude_word';
    }
  },
  methods: {
    // 打开弹窗
    open(row) {
      this.reset();
      this.visible = true;
      if (row && row.wordId) {
        this.title = '修改词汇';
        // 获取词汇详情
        getDictionaryWordDetail(row.wordId).then(response => {
          this.form = response.data;
          // 如果是排除词，需要处理groupIds
           if (this.isExcludeWord && response.data.groups) {
             this.form.groupIds = response.data.groups.map(g => g.groupId);
           }
        });
      } else {
        this.title = '新增词汇';
      }
      // 如果是排除词库，加载分组列表
      if (this.isExcludeWord) {
        this.fetchGroupList();
      }
    },
    // 获取排除词库下的分组
    fetchGroupList() {
      getDictionaryGroupsByCategory(this.categoryInfo.categoryId).then(response => {
        this.groupList = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.visible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        wordId: undefined,
        wordText: undefined,
        categoryId: this.categoryInfo.categoryId,
        groupIds: []
      };
      this.groupList = [];
      this.resetForm("form");
    },
    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.wordId != null) {
            // 修改
            updateDictionaryWord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.visible = false;
              this.$emit('submit-success');
            });
          } else {
            // 新增
            batchAddDictionaryWord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.visible = false;
              this.$emit('submit-success');
            });
          }
        }
      });
    }
  }
};
</script>