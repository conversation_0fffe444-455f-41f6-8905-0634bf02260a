<template>
  <div class="simple-info-detail">
    <div class="header">
      <div class="title-section">
        <div class="title">
          <a v-if="infoDetail.link" :href="infoDetail.link" target="_blank">{{ infoDetail.title }}</a>
          <span v-else>{{ infoDetail.title }}</span>
          <span
            v-if="infoDetail.link"
            class="copy-link-icon"
            title="复制原文链接"
            @click="copyLink(infoDetail.link)"
          >
            <i class="el-icon-link" />
          </span>
        </div>
        <div v-if="infoDetail.riskStamp" class="stamp" :class="riskClass">{{ riskLevelText }}</div>
      </div>
      <div v-if="infoDetail.agency" class="agency">机构: {{ infoDetail.agency }}</div>
      <div class="meta-info">
        <div v-if="infoDetail.time" class="time">
          <i class="el-icon-time" /> {{ infoDetail.time }}
        </div>
        <div v-if="infoDetail.source" class="source">
          来源：{{ infoDetail.source }}
        </div>
      </div>
    </div>

    <div v-if="mediaInfo" class="video-container" @click="openMediaPlayer">
      <div class="video-placeholder">
        <img v-if="mediaInfo.cover" :src="mediaInfo.cover" alt="媒体封面" class="cover-image">
      </div>
      <div class="play-button">
        <svg-icon icon-class="play_icon" class-name="play-icon" />
      </div>
    </div>

    <div v-if="infoDetail.analysis">
      <div class="section-title">
        <i class="el-icon-data-analysis" /> 研判分析
      </div>
      <div class="analysis-box">
        <p>{{ infoDetail.analysis }}</p>
      </div>
    </div>

    <div v-if="infoDetail.summary">
      <div class="section-title">
        <i class="el-icon-document" /> 摘要
      </div>
      <div class="summary-box">
        <p>{{ infoDetail.summary }}</p>
      </div>
    </div>

    <div class="copy-btn" @click="copyContent">
      <svg-icon icon-class="copy" class-name="copy-icon" />
    </div>
    <video-player
      :visible.sync="mediaPlayerVisible"
      :video-src="mediaInfo ? mediaInfo.videoSrc : ''"
    />
  </div>
</template>

<script>
import VideoPlayer from './VideoPlayer.vue'

export default {
  name: 'SimpleInfoDetail',
  components: {
    VideoPlayer
  },
  props: {
    infoDetail: {
      type: Object,
      default: () => ({
        title: '',
        link: '',
        riskStamp: '',
        agency: '',
        time: '',
        source: '',
        analysis: '',
        summary: ''
      })
    },
    rawContent: {
      type: String,
      default: ''
    },
    sentimentData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      mediaPlayerVisible: false
    }
  },
  computed: {
    mediaInfo() {
      if (!this.sentimentData || this.sentimentData.platform !== 'douyin') {
        return null
      }
      const data = this.sentimentData
      const playUrl = data.playUrl && data.playUrl.length > 0 ? data.playUrl[0] : null
      const coverInfo = data.videoInfo ? data.videoInfo.coverInfo : null
      const coverUrl = coverInfo ? coverInfo.onlineUrl : null

      if (data.postCategory === 2) {
        if (!playUrl || (data.videoInfo && data.videoInfo.duration === 0)) {
          return null
        }
        return {
          type: 'video',
          cover: coverUrl,
          videoSrc: playUrl
        }
      }
      return null
    },
    riskClass() {
      if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('低')) {
        return 'risk-low'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('中')) {
        return 'risk-medium'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('高')) {
        return 'risk-high'
      } else {
        return ''
      }
    },
    riskLevelText() {
      if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('低')) {
        return '低风险'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('中')) {
        return '中风险'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('高')) {
        return '高风险'
      } else {
        return ''
      }
    }
  },
  methods: {
    openMediaPlayer() {
      if (this.mediaInfo && this.mediaInfo.videoSrc) {
        this.mediaPlayerVisible = true
      }
    },
    copyLink(text) {
      if (!text) {
        this.$message.error('链接不存在，无法复制')
        return
      }
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('原文链接已复制')
      }).catch(err => {
        this.$message.error('复制失败')
      })
    },
    copyContent() {
      if (!this.rawContent) {
        this.$message.error('内容不存在，无法复制')
        return
      }
      navigator.clipboard.writeText(this.rawContent).then(() => {
        this.$message.success('内容已复制到剪贴板！')
      }).catch(err => {
        this.$message.error('复制失败，请手动复制。')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.simple-info-detail {
    padding: 10px;
}

.header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.title-section {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 10px;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    flex-grow: 1;
    margin-right: 15px;

    a {
        color: #1e5799;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

.copy-link-icon {
    margin-left: 10px;
    cursor: pointer;
    color: #909399;
    font-size: 18px;

    &:hover {
        color: #1e5799;
    }
}

.stamp {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: bold;
    transform: rotate(-5deg);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    letter-spacing: 1px;
    flex-shrink: 0;
}

.risk-low {
    border: 2px dashed #FFD700;
    background: linear-gradient(145deg, #FFFACD, #FFF8DC);
    color: #FFD700;
}

.risk-medium {
    border: 2px dashed orange;
    background: linear-gradient(145deg, #fff5e6, #ffe6cc);
    color: orange;
}

.risk-high {
    border: 2px dashed red;
    background: linear-gradient(145deg, #fff0f0, #ffdede);
    color: red;
}

.agency {
    font-size: 13px;
    color: #909399;
    margin-bottom: 10px;
}

.meta-info {
    display: flex;
    flex-wrap: wrap;
    font-size: 13px;
    color: #909399;
    gap: 20px;
}

.video-container {
    position: relative;
    cursor: pointer;
    margin-bottom: 20px;
    .video-placeholder {
        width: 100%;
        position: relative;
        background-color: #e9ecef;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 56.25%; /* 16:9 Aspect Ratio */
    }
    .cover-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    .play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s;
        z-index: 2;
        .play-icon {
            font-size: 30px;
            color: #1e5799;
        }
    }
}

.section-title {
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    margin: 25px 0 15px;
    padding-left: 10px;
    border-left: 4px solid #1e5799;
    display: flex;
    align-items: center;

    i {
        margin-right: 8px;
    }
}

.analysis-box,
.summary-box {
    background: #f8f8f9;
    border-radius: 8px;
    padding: 18px;
    font-size: 15px;
    color: #555;
    line-height: 1.7;
    white-space: pre-wrap;
    word-break: break-word;

    p {
        margin: 0;
    }
}

.analysis-box {
    background: #f0f7ff;
    border-left: 4px solid #1e5799;
}

.copy-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #1e5799;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(30, 87, 153, 0.4);
    z-index: 1000;

    .copy-icon {
        color: #fff;
        font-size: 32px;
    }
}
</style>
