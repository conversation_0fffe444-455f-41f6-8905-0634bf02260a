import request from '@/utils/request'

/**
 * 创建专题
 * @param {Object} data
 * @param {number[]} data.directIds - 信源组ID列表
 * @param {number[]} data.excludeDirectIds - 排除的信源组ID列表
 * @param {number[]} data.filterOriginals - 过滤原创性
 * @param {number[]} data.filterSentiments - 过滤情感
 * @param {string[]} data.filterSourceLevels - 过滤来源级别
 * @param {string[]} data.filterVerifies - 过滤认证状态
 * @param {number} data.parentMenuId - 父菜单ID
 * @param {string} data.planName - 专题计划名称
 * @param {string} data.wordCombination - 关键词组合
 * @param {string[]} data.wordExcludeList - 排除词列表
 * @returns {Promise}
 */
export function createPlan(data) {
  return request({
    url: '/universalGravitation/plan/createPlan',
    method: 'post',
    data
  })
}

/**
 * 删除专题
 * @param {Object} data
 * @param {string|number} data.menuId - 专题计划ID
 * @returns {Promise<{
 *   code: number,
 *   data: {
 *     menuId: string
 *   },
 *   msg: string,
 *   success: boolean,
 *   timestamp: number
 * }>}
 */
export function deletePlan(data) {
  return request({
    url: '/universalGravitation/plan/deletePlan',
    method: 'post',
    data
  })
}

/**
 * 打开专题
 * @param {Object} data
 * @param {string|number} data.plan_id - 专题计划ID
 * @returns {Promise<{
 *   code: number,
 *   data: {
 *     menuId: string
 *   },
 *   msg: string,
 *   success: boolean,
 *   timestamp: number
 * }>}
 */
export function openPlan(data) {
  return request({
    url: '/universalGravitation/plan/openPlan',
    method: 'post',
    data
  })
}

/**
 * 获取专题计划详情
 * @param {Object} data
 * e
 *   success: boolean,
 *   timestamp: number
 * }>}
 */
export function getPlanDetail(data) {
  return request({
    url: '/universalGravitation/plan/planDetail',
    method: 'post',
    data
  })
}

/**
 * 暂停专题
 * @param {Object} data
 * @param {string|number} data.menuId - 专题计划ID
 * @returns {Promise<{
 *   code: number,
 *   data: {
 *     plan_id: string
 *   },
 *   msg: string,
 *   success: boolean,
 *   timestamp: number
 * }>}
 */
export function suspendPlan(data) {
  return request({
    url: '/universalGravitation/plan/suspendPlan',
    method: 'post',
    data
  })
}

/**
 * 修改专题
 * @param {Object} data
 * @param {string} data.busiType - 业务类型
 * @param {number[]} data.directIds - 直接ID列表
 * @param {number[]} data.excludeDirectIds - 排除的直接ID列表
 * @param {number[]} data.filterOriginals - 过滤原创性
 * @param {number[]} data.filterSentiments - 过滤情感
 * @param {string[]} data.filterSourceLevels - 过滤来源级别
 * @param {string[]} data.filterVerifies - 过滤认证状态
 * @param {number} data.menuId - 菜单ID
 * @param {string} data.planName - 专题计划名称
 * @param {string|number} data.menuId - 专题计划ID
 * @param {string} data.wordCombination - 关键词组合
 * @param {string[]} data.wordExcludeList - 排除词列表
 * @returns {Promise<{
 *   code: number,
 *   data: {
 *     plan_id: string
 *   },
 *   msg: string,
 *   success: boolean,
 *   timestamp: number
 * }>}
 */
export function updatePlan(data) {
  return request({
    url: '/universalGravitation/plan/updatePlan',
    method: 'post',
    data
  })
}

/**
 * 获取地区列表
 */
export function getRegion() {
  return request({
    url: '/universalGravitation/common/getRegion',
    method: 'post',
    data: {}
  })
}
