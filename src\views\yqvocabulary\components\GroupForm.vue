<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入分组名称" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addDictionaryGroup, updateDictionaryGroup } from '@/api/yqvocabulary';

export default {
  name: "GroupForm",
  props: {
    categoryInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      rules: {
        groupName: [
          { required: true, message: "分组名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    open(row) {
      this.reset();
      this.visible = true;
      if (row && row.groupId) {
        this.title = '修改分组';
        this.form = { ...row };
      } else {
        this.title = '新增分组';
      }
    },
    cancel() {
      this.visible = false;
      this.reset();
    },
    reset() {
      this.form = {
        groupId: undefined,
        groupName: undefined,
        categoryId: this.categoryInfo.categoryId
      };
      this.resetForm("form");
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.groupId != null) {
            updateDictionaryGroup(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.visible = false;
              this.$emit('submit-success');
            });
          } else {
            addDictionaryGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.visible = false;
              this.$emit('submit-success');
            });
          }
        }
      });
    }
  }
};
</script>