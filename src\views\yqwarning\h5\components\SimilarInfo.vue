<template>
  <div class="tab-content">
    <!-- 相似信息模块 -->
    <div class="section-title">
      <i class="fas fa-copy" /> 相似信息
    </div>
    <div v-if="similarInfoList && similarInfoList.length > 0" class="similar-info-list">
      <div v-for="(item, idx) in similarInfoList" :key="idx" class="similar-info-item">
        <div class="info-title-row">
          <span class="info-title">{{ item.title }}</span>
          <span class="info-date">发布时间：{{ item.publishTime }}</span>
        </div>
        <div class="info-content">{{ getFormattedContent(item.content) }}</div>
        <div class="info-meta">
          <span class="info-platform">平台：{{ item.source }}</span>
          <a :href="item.url" class="info-link" target="_blank">原文</a>
        </div>
      </div>
    </div>
    <div v-else class="placeholder-wrapper">
      暂无相似敏感信息
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimilarInfo',
  props: {
    similarInfoList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getFormattedContent(content) {
      if (!content) return ''
      return content.length > 200 ? content.slice(0, 200) + '...' : content
    }
  }
}
</script>

<style scoped lang="scss">
.section-title {
  margin-bottom: 20px;
  font-size: 17px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  padding-left: 10px;
  border-left: 4px solid #1e5799;

  i {
    margin-right: 10px;
    color: #1e5799;
  }
}

.similar-info-list {
  display: flex;
  flex-direction: column;
  gap: 18px;

  .similar-info-item {
    background: #fff7f0;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(255, 167, 38, 0.08);
    padding: 16px 14px 12px 14px;
    border-left: 4px solid #e6a23c;

    .info-title-row {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 8px;
      gap: 10px;

      .info-title {
        font-size: 16px;
        font-weight: 600;
        color: #e67e22;
      }

      .info-date {
        font-size: 13px;
        color: #909399;
        width: 200px;
      }
    }

    .info-content {
      font-size: 15px;
      color: #333;
      margin-bottom: 10px;
    }

    .info-meta {
      display: flex;
      align-items: center;
      gap: 18px;
      font-size: 13px;
      color: #666;

      .info-platform {
        font-weight: bold;
      }

      .info-link {
        color: #1e5799;
        text-decoration: none;
        margin-left: 8px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.placeholder-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  font-size: 18px;
  color: #b0b3b8;
  background-color: #fcfcfc;
  border-radius: 12px;
  border: 2px dashed #e4e7ed;
}
</style>
