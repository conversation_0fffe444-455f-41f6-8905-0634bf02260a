<template>
  <div class="tab-content">
    <!-- 用户基本信息模块 -->
    <div class="section-title">
      <i class="fas fa-user-circle" /> 账号信息
    </div>
    <div class="analysis-box account-basic-info">
      <div class="account-basic-header">
        <img :src="accountInfo.basicInfo.avatar" class="account-avatar" alt="avatar">
        <div class="account-basic-title">
          <span class="account-name">{{ accountInfo.basicInfo.nickname }}</span>
          <span
            v-if="accountInfo.basicInfo.verificationType || accountInfo.basicInfo.verificationName"
            class="account-badge"
          >
            {{ accountInfo.basicInfo.verificationType }} {{ accountInfo.basicInfo.verificationName }}
          </span>
        </div>
      </div>
      <div class="account-basic-list">
        <p v-if="accountInfo.basicInfo.id"><strong>用户ID：</strong>{{ accountInfo.basicInfo.id }}</p>
        <p v-if="accountInfo.basicInfo.homepageUrl"><strong>用户主页：</strong><a
          :href="accountInfo.basicInfo.homepageUrl"
          target="_blank"
        >{{
          accountInfo.basicInfo.homepageId }}</a></p>
        <p v-if="accountInfo.basicInfo.followers"><strong>粉丝量：</strong>{{ accountInfo.basicInfo.followers }}</p>
        <p v-if="accountInfo.basicInfo.verificationType"><strong>认证类型：</strong>{{
          accountInfo.basicInfo.verificationType }}</p>
        <p v-if="accountInfo.basicInfo.verificationName"><strong>认证名称：</strong>{{
          accountInfo.basicInfo.verificationName }}</p>
        <p v-if="accountInfo.basicInfo.location"><strong>注册地：</strong>{{ accountInfo.basicInfo.location }}</p>
      </div>
    </div>

    <!-- 账号近30天AI敏感信息模块 -->
    <div class="section-title">
      <i class="fas fa-exclamation-triangle" /> 近30天AI敏感信息
    </div>
    <div v-if="accountInfo.aiSensitiveInfos && accountInfo.aiSensitiveInfos.length > 0" class="ai-sensitive-list">
      <div v-for="(item, idx) in accountInfo.aiSensitiveInfos" :key="idx" class="ai-sensitive-item">
        <div class="ai-title-row">
          <span class="ai-title">{{ item.title }}</span>
          <span class="ai-date">发布时间：{{ item.publishTime }}</span>
        </div>
        <div class="ai-content">{{ getFormattedContent(item.content) }}</div>
        <div class="ai-meta">
          <span class="ai-platform">平台：{{ item.source }}</span>
          <a :href="item.url" class="ai-link" target="_blank">原文</a>
        </div>
      </div>
    </div>
    <div v-else class="no-sensitive-info">
      该账号近30天未发现敏感信息
    </div>
  </div>
</template>

<script>
export default {
  props: ['accountInfo'], // Prop已更新
  methods: {
    getFormattedContent(content) {
      if (!content) return ''
      return content.length > 200 ? content.slice(0, 200) + '...' : content
    }
  }
}
</script>

<style scoped lang="scss">
// 样式与 AccountProfile.vue 基本一致，可复用
.account-basic-info {
    display: flex;
    flex-direction: column;
    background: #f0f7ff;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 18px;
    border-left: 4px solid #1e5799;

    .account-basic-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .account-avatar {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            margin-right: 16px;
            border: 2px solid #e6a23c;
            object-fit: cover;
        }

        .account-basic-title {
            display: flex;
            align-items: center;

            .account-name {
                font-size: 18px;
                font-weight: bold;
                color: #303133;
                margin-right: 10px;
            }

            .account-badge {
                background: #e6a23c; // 认证信息用橙色
                color: #fff;
                font-size: 12px;
                border-radius: 10px;
                padding: 2px 8px;
                margin-left: 4px;
            }
        }
    }

    .account-basic-list p {
        font-size: 15px;
        color: #333;
        margin-bottom: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        a {
            color: #1e5799;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

.section-title {
    margin-bottom: 20px;
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
    padding-left: 10px;
    border-left: 4px solid #1e5799;

    i {
        margin-right: 10px;
        color: #1e5799;
    }
}

.ai-sensitive-list {
    display: flex;
    flex-direction: column;
    gap: 18px;

    .ai-sensitive-item {
        background: #fff7f0;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(255, 167, 38, 0.08);
        padding: 16px 14px 12px 14px;
        border-left: 4px solid #e6a23c;

        .ai-title-row {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 8px;
            gap: 10px;

            .ai-title {
                font-size: 16px;
                font-weight: 600;
                color: #e67e22;
            }

            .ai-date {
                font-size: 13px;
                color: #909399;
                width: 200px;
            }
        }

        .ai-content {
            font-size: 15px;
            color: #333;
            margin-bottom: 10px;
        }

        .ai-meta {
            display: flex;
            align-items: center;
            gap: 18px;
            font-size: 13px;
            color: #666;

            .ai-platform {
                font-weight: bold;
            }

            .ai-link {
                color: #1e5799;
                text-decoration: none;
                margin-left: 8px;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

.no-sensitive-info {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 300px;
    font-size: 18px;
    color: #b0b3b8;
    background-color: #fcfcfc;
    border-radius: 12px;
    border: 2px dashed #e4e7ed;
}

.incomplete-info-prompt {
    color: #e6a23c;
    border: 1px solid #e6a23c;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 10px;
    background-color: #fdf6ec;
    font-size: 14px;
}
</style>
