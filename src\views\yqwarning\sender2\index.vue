<template>
  <div v-loading="isLoading" class="app-container warning-sender-page">
    <div class="main-layout">
      <div v-if="pageMode === 'normal' && warningId" class="left-panel" :class="{ collapsed: isLeftPanelCollapsed }">
        <div class="collapse-button" @click="toggleLeftPanel">
          <i :class="isLeftPanelCollapsed
            ? 'el-icon-d-arrow-right'
            : 'el-icon-d-arrow-left'
            " />
        </div>
        <Detail :item="detailItem" />
      </div>
      <!-- 用于普通预警模式下快速填充 (无 warningId 时) -->
      <div v-if="pageMode === 'normal' && !warningId" class="left-panel" :class="{ collapsed: isLeftPanelCollapsed }">
        <div class="collapse-button" @click="toggleLeftPanel">
          <i :class="isLeftPanelCollapsed
            ? 'el-icon-d-arrow-right'
            : 'el-icon-d-arrow-left'
            " />
        </div>
        <paste-panel v-model="pastedContent" :is-collapsed="isLeftPanelCollapsed"
          @paste-and-parse="handlePasteAndParse" />
      </div>
      <div class="right-panel">
        <div class="mode-stamp" :class="{
          'two-text': warningText.length === 2,
          'four-text': warningText.length === 4,
        }">
          {{ warningText }}
        </div>
        <div class="card">
          <warning-form ref="warningForm" v-model="form" :rules="rules" :page-mode="pageMode"
            :institution-mode="institutionMode" :selected-institutions="selectedInstitutions"
            :is-link-invalid="isLinkInvalid" :selected-contacts="selectedContacts" :warning-id="warningId"
            :analyze-loading="isAnalyzing" 
            :max-content-bytes="maxContentBytes"
            @show-institution-dialog="showInstitutionDialog" @toggle-institution-mode="toggleInstitutionMode"
            @remove-institution="handleRemoveInstitution" @toggle-link-status="toggleLinkStatus"
            @add-contacts="showContactDialog" @remove-contact="handleRemoveSelectedContact"
            @remove-group="handleRemoveGroupContacts" @summary-image-change="handleSummaryImageChange"
            @ai-analyze="handleAiAnalyze" />
        </div>
      </div>
    </div>
    <action-buttons :send-button-text="sendButtonText" @submit="handleSubmit" @copy="copyOutput" @reset="resetForm" />
    <!-- 机构选择弹窗组件 -->
    <institution-selection-dialog :visible.sync="institutionDialogVisible" :tree-data="institutionTreeData"
      :initial-selected-ids="selectedInstitutions.map((inst) => inst.id)" @confirm="handleInstitutionConfirm" />
    <!-- 联系人选择弹窗组件 -->
    <contact-selection-dialog :visible.sync="contactDialogVisible" :tree-data="contactSelectionTree"
      :initial-selected-ids="selectedContacts.map((c) => c.contactId)" @confirm="handleContactConfirm" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { getPublicSentimentById } from '@/api/yqmonitor'
import { mapItem } from '@/views/yqmonitor/components/MainContent/articleUtil'
import {
  getInstitutionUserTree,
  sendWarnMsg,
  getAIAnalysisFromBackend
} from '@/api/yqmonitor/institution' // 引入新的API
import { mapApiTreeToComponentTree, buildContactSelectionTree, decodeHtmlEntities } from './helper' // 引入新的 helper 函数
import Detail from '@/views/yqmonitor/detail.vue'
import ImageUpload from '@/components/ImageUpload/index2.vue'
import InstitutionSelectionDialog from './components/InstitutionSelectionDialog.vue'
import ContactSelectionDialog from './components/ContactSelectionDialog.vue'
import ContactSelector from './components/ContactSelector.vue'
import WarningForm from './components/WarningForm.vue'
import PastePanel from './components/PastePanel.vue'
import ActionButtons from './components/ActionButtons.vue'


export default {
  name: 'WarningSender',
  components: {
    Detail,
    ImageUpload,
    InstitutionSelectionDialog,
    ContactSelectionDialog,
    ContactSelector,
    WarningForm,
    PastePanel,
    ActionButtons
  },
  dicts: ['warn_level', 'content_type'],
  data() {
    return {
      pageMode: 'normal',
      institutionMode: 'institution', // 'institution' or 'suspected'
      warningId: null,
      isLoading: false,
      isLeftPanelCollapsed:
        localStorage.getItem('yqwarning_left_panel_collapsed') === 'true',
      detailItem: {},
      form: {
        title: '',
        summary: '',
        source: '',
        time: null,
        link: '',
        institutions: [],
        suspectedInstitution: '',
        riskLevel: '低', // MODIFIED: Default risk level changed to "低"
        analysis: '',
        summaryContent: '',
        reportContent: '',
        contacts: [],
        detailPage: ''
      },
      summaryImages: [],
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        source: [{ required: true, message: '请输入来源', trigger: 'blur' }],
        time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        riskLevel: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ],
        summary: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
        summaryContent: [
          { required: true, message: '请输入汇总内容', trigger: 'blur' }
        ],
        reportContent: [
          { required: true, message: '请输入报告内容', trigger: 'blur' }
        ],
        institutions: [
          {
            required: true,
            type: 'array',
            min: 1,
            message: '请选择机构',
            trigger: 'change'
          }
        ],
        suspectedInstitution: [
          { required: true, message: '请输入疑似机构', trigger: 'blur' }
        ],
        contacts: [
          {
            required: true,
            type: 'array',
            min: 1,
            message: '请选择联系人',
            trigger: 'change'
          }
        ]
      },
      institutionDialogVisible: false,
      contactDialogVisible: false,
      institutionTreeData: [],
      contactSelectionTree: [], // 【新增】用于联系人弹窗的树
      selectedInstitutions: [],
      selectedContacts: [],
      manuallyRemovedContactIdsThisSession: new Set(),
      rawInstitutionTree: [], // 【新增】存储从API获取的原始树结构数据
      institutionParentMap: new Map(), // 【新增】用于存储 code -> parentCode 的映射关系
      pastedContent: '', // 用于绑定左侧粘贴板的文本
      isLinkInvalid: false, // 用于标记链接是否失效
      allContactsFlatList: [], // 用于存储所有联系人的扁平化
      defaultSelectedContacts: [], // 用于持久化存储默认联系人
      isAnalyzing: false,
    }
  },
  computed: {
    ...mapGetters(['nickName']),
    maxContentBytes() {
       if (this.pageMode === 'normal') {
        return 1800 // 预警模式限制1800
      }
      return 3000 // 汇总和早晚报模式限制3000
    },
    sendButtonText() {
      if (this.pageMode === 'normal') return '聚合预警发送'
      if (this.pageMode === 'summary') return '发送汇总'
      if (this.pageMode === 'report') return '发送早晚报'
      return '发送'
    },

    warningText() {
      if (this.pageMode === 'normal' && this.warningId) {
        return '预警'
      } else if (this.pageMode === 'normal' && !this.warningId) {
        return '普通预警'
      } else if (this.pageMode === 'summary') {
        return '汇总'
      } else if (this.pageMode === 'report') {
        return '早晚报'
      }
      return '发送' // Fallback
    }
  },
  watch: {
    selectedInstitutions(newVal, oldVal) {
      this.form.institutions = newVal.map((inst) => inst.id)
      const newIds = newVal
        .map((i) => i.id)
        .sort()
        .join(',')
      const oldIds = oldVal
        .map((i) => i.id)
        .sort()
        .join(',')
      if (
        newIds !== oldIds &&
        (this.pageMode === 'normal' || this.pageMode === 'summary')
      ) {
        this.updateContactsBasedOnInstitutions()
      }
    }
  },
  created() {
    this.determinePageModeAndLoadData()
  },
  mounted() {
    console.log('内容类型字典:', this.dict.type.content_type)
  },
  methods: {
    /**
     * 处理AI智能填充逻辑
     */
    async handleAiAnalyze() {
      if (!this.form.summary || !this.form.link) {
        this.$message.warning('请确保【摘要】和【链接】字段都已填写内容。')
        return
      }
      this.isAnalyzing  = true
      try {
        const payload = {
          summary: this.form.summary,
          link: this.form.link
        }
        // 调用后端API接口
        const response = await getAIAnalysisFromBackend(payload)
        // 后端成功状态码可能为 0 或 200，做兼容处理
        if (response.code === 0 && response.data) {
          // 根据需求文档，覆盖标题和研判分析
          if (response.data.title) {
            this.form.title = response.data.title
          }
          if (response.data.analysis) {
            this.form.analysis = response.data.analysis
          }
          this.$message.success('AI 智能填充完成！')
        } else {
          this.$message.error(response.msg || 'AI 分析返回数据格式不正确')
        }
      } catch (error) {
        console.error('AI analysis failed:', error)
        this.$message.error('AI 分析请求失败，请检查网络或联系管理员。')
      } finally {
        this.isAnalyzing  = false
      }
    },
    handleSummaryImageChange(fileList) {
      this.summaryImages = fileList
    },
    async loadInitialData() {
      this.isLoading = true
      try {
        const response = await getInstitutionUserTree()
        if (response.code === 200 && response.data) {
          // 存储原始数据并构建父级映射
          this.rawInstitutionTree = response.data
          this.buildParentMapRecursive(
            this.rawInstitutionTree,
            this.institutionParentMap
          )

          // 用于主表单的机构选择器 (这部分不变)
          this.institutionTreeData = mapApiTreeToComponentTree(response.data)
          // 用于联系人弹窗的选择树 (这部分不变)
          this.contactSelectionTree = buildContactSelectionTree(response.data)
        } else {
          this.$message.error(response.msg || '获取机构用户树失败')
        }
      } catch (error) {
        console.error('获取机构用户树失败:', error)
        this.$message.error('加载机构数据失败，请检查网络或联系管理员')
      } finally {
        this.isLoading = false
      }
    },

    async determinePageModeAndLoadData() {
      this.isLoading = true
      const query = this.$route.query
      if (query.mode) {
        const validModes = ['normal', 'summary', 'report']
        if (validModes.includes(query.mode)) {
          this.pageMode = query.mode
        } else {
          this.$message.warning(
            `无效的模式参数: ${query.mode}，将使用默认模式`
          )
          this.pageMode = 'normal' // fallback to normal
        }
      } else if (query.warningId) {
        this.pageMode = 'normal' // If warningId is present, it's normal mode
      } else {
        this.pageMode = 'normal' // Default mode
      }
      await this.loadInitialData() // Load tree data first
      if (query.warningId && this.pageMode === 'normal') {
        await this.loadWarningData(query.warningId)
      } else {
        this.resetFormFields() // Reset form for other modes or new normal warning
      }
      // 1. 构建扁平化列表
      this.buildAllContactsFlatList()
      // 2. 设置默认联系人
      this.setDefaultContacts()
      this.isLoading = false
    },
    resetFormFields() {
      this.form = {
        title: '',
        summary: '',
        source: '',
        time: null,
        link: '',
        institutions: [],
        suspectedInstitution: '',
        riskLevel: '低', // Default to "低"
        analysis: '',
        summaryContent: '',
        reportContent: '',
        contacts: [],
        detailPage: ''
      }
      this.summaryImages = []
      this.isLinkInvalid = false // Reset link status
      this.institutionMode = 'institution'
      this.selectedInstitutions = []
      this.selectedContacts = []
      this.manuallyRemovedContactIdsThisSession.clear()
      if (this.$refs.warningForm) {
        this.$refs.warningForm.resetFields()
      }
    },
    async loadWarningData(warningId) {
      if (!warningId) return
      this.isLoading = true
      this.warningId = warningId
      try {
        const response = await getPublicSentimentById(warningId)
        if (response.code === 200 && response.data) {
          const item = mapItem(response.data)
          this.form.title = decodeHtmlEntities(item.titleObj.rawTitle || item.titleObj.title)
          const rawSummary = (
            item.contentObj?.rawContent ||
            item.contentObj?.rawAsr ||
            item.contentObj?.rawOcr ||
            ''
          )
            .trim()
            .replace(/\s+/g, ' ')
          this.form.summary =
            rawSummary.length > 500
              ? rawSummary.substring(0, 500) + '......'
              : rawSummary
          this.form.source = `${item.source}—${item.author}`
          this.form.time = item.publishTime
            ? item.publishTime.substring(0, 19).replace('T', ' ')
            : null // yyyy-MM-dd HH:mm:ss
          this.form.link = item.url
          this.form.institutions = []
          this.selectedInstitutions = []
          this.selectedContacts = [] // Clear contacts when loading new warning
          this.manuallyRemovedContactIdsThisSession.clear()
          this.detailItem = item
          this.$message.success('预警信息加载成功')
        } else {
          this.$message.error(
            response.msg || `未能加载ID为 ${warningId} 的预警信息`
          )
        }
      } catch (error) {
        console.error(`加载预警详情失败 (ID: ${warningId}):`, error)
        this.$message.error('加载预警详情失败，请检查网络或联系管理员。')
      } finally {
        this.isLoading = false
      }
    },
    toggleInstitutionMode() {
      this.institutionMode =
        this.institutionMode === 'institution' ? 'suspected' : 'institution'
      this.$nextTick(() => {
        if (this.institutionMode === 'institution') {
          this.form.suspectedInstitution = ''
        } else {
          this.form.institutions = []
          this.selectedInstitutions = []
        }
        if (this.$refs.warningForm) {
          this.$refs.warningForm.clearValidate([
            'institutions',
            'suspectedInstitution'
          ])
        }
      })
    },
    showInstitutionDialog() {
      this.institutionDialogVisible = true
    },

    handleInstitutionConfirm(selectedNodes) {
      this.selectedInstitutions = selectedNodes
      // No need to call updateContactsBasedOnInstitutions here, watcher will do it.
    },
    handleRemoveInstitution(instToRemove) {
      this.selectedInstitutions = this.selectedInstitutions.filter(
        (inst) => inst.id !== instToRemove.id
      )
      // Watcher on selectedInstitutions will trigger updateContactsBasedOnInstitutions
    },
    // src/views/yqwarning/sender/index.vue -> methods

    /**
     * 【核心修改】当主表单选择机构后，自动填充该机构及所有上级机构的联系人
     */
    updateContactsBasedOnInstitutions() {
      // 1. 获取所有需要关联的机构 Code
      const allRelevantInstitutionCodes = new Set()
      this.selectedInstitutions.forEach((inst) => {
        let currentCode = inst.id
        // 使用 while 循环和 parentMap 向上追溯，直到找不到父级
        while (currentCode) {
          allRelevantInstitutionCodes.add(currentCode)
          currentCode = this.institutionParentMap.get(currentCode)
        }
      })

      // 2. 根据收集到的所有 Code，从联系人树中提取联系人
      const institutionBasedContacts = []
      const addedContactIds = new Set()

      // 遍历联系人选择树 (contactSelectionTree 已经是拍平的机构列表，效率很高)
      this.contactSelectionTree.forEach((institutionNode) => {
        // 如果当前机构的 code 在我们收集的"相关机构"列表中
        if (allRelevantInstitutionCodes.has(institutionNode.code)) {
          // 遍历机构下的所有分组（包括[未分组]）
          institutionNode.children.forEach((groupNode) => {
            // 遍历分组下的所有联系人
            groupNode.children.forEach((contactNode) => {
              // 防止重复添加同一个联系人
              if (!addedContactIds.has(contactNode.id)) {
                addedContactIds.add(contactNode.id)

                // 构造完整的联系人对象，包含其直接所属的机构和分组信息
                institutionBasedContacts.push({
                  id: contactNode.userId,
                  name: contactNode.userName,
                  phone: contactNode.phoneNumber,
                  sendType: contactNode.warnType || 'all',
                  // 注意：这里的 institutionId 和 institutionName 是联系人【直接所属】的机构信息
                  institutionId: institutionNode.code,
                  institutionName: institutionNode.label,
                  groupId: groupNode.groupId,
                  groupName: groupNode.label,
                  contactId: contactNode.id,
                  displayName: contactNode.label
                })
              }
            })
          })
        }
      })

      // 3. 合并默认联系人和机构联系人，并去重
      const mergedContactsMap = new Map()
      // 首先，添加默认联系人
      this.defaultSelectedContacts.forEach((contact) => {
        mergedContactsMap.set(contact.contactId, contact)
      })

      // 然后，添加基于机构选择的联系人，Map会自动处理重复项
      institutionBasedContacts.forEach((contact) => {
        mergedContactsMap.set(contact.contactId, contact)
      })
      // 4. 从合并后的列表中，过滤掉本会话中用户已手动移除的所有联系人
      const potentialContacts = Array.from(mergedContactsMap.values())
      const finalContacts = potentialContacts.filter(
        (contact) =>
          !this.manuallyRemovedContactIdsThisSession.has(contact.contactId)
      )
      // 5. 更新组件状态
      this.selectedContacts = finalContacts
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
    },
    /**
     * 【新增】通过点击分组标签，移除整个分组的联系人
     */
    handleRemoveGroupContacts(groupIdToRemove) {
      // 记录被移除的联系人ID，以便维护"手动移除"状态
      this.selectedContacts.forEach((contact) => {
        if (contact.groupId === groupIdToRemove) {
          this.manuallyRemovedContactIdsThisSession.add(contact.contactId)
        }
      })

      // 从当前选择中过滤掉该分组的所有联系人
      this.selectedContacts = this.selectedContacts.filter(
        (contact) => contact.groupId !== groupIdToRemove
      )
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
    },
    showContactDialog() {
      this.contactDialogVisible = true
    },

    handleContactConfirm(selectedContacts) {
      this.selectedContacts = selectedContacts
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
    },
    handleRemoveSelectedContact(contactToRemove) {
      this.selectedContacts = this.selectedContacts.filter(
        (c) => c.contactId !== contactToRemove.contactId
      )
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
      this.manuallyRemovedContactIdsThisSession.add(contactToRemove.contactId)
    },
    copyOutput() {
      let textToCopy = ''
      if (this.pageMode === 'normal') {
        textToCopy = this.$refs.warningForm.enterpriseWxPreviewContent
      } else if (this.pageMode === 'summary') {
        textToCopy = this.form.summaryContent
      } else if (this.pageMode === 'report') {
        textToCopy = this.form.reportContent
      }
      if (!textToCopy) {
        this.$message.warning('没有内容可以复制')
        return
      }
      // Create a temporary textarea element to hold the text
      const textarea = document.createElement('textarea')
      textarea.value = textToCopy
      document.body.appendChild(textarea)
      textarea.select()
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('内容已复制到剪贴板')
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
        console.error('Fallback: Oops, unable to copy', err)
      }
      document.body.removeChild(textarea)
    },
    handleSubmit() {
      this.$refs.warningForm.validate(async (valid) => {
        if (valid) {
          if (this.selectedContacts.length === 0) {
            this.$message.warning('请至少选择一个联系人')
            return
          }
          if (
            this.$refs.warningForm.currentContentByteCount >
            this.maxContentBytes
          ) {
            this.$message.error(
              `字数超出限制 (${this.$refs.warningForm.currentContentByteCount}/${this.maxContentBytes}字节)，请删减内容`
            )
            return
          }
          const confirmMessage = `确定要向 ${this.selectedContacts.length} 位联系人发送【${this.warningText}】吗？`
          this.$confirm(confirmMessage, '操作确认', {
            confirmButtonText: '确定发送',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              let warnLevelValue, contentTypeValue
              const modeToLabelMap = {
                normal: '预警',
                summary: '汇总',
                report: '早晚报'
              }
              const contentTypeLabel = modeToLabelMap[this.pageMode]
              if (this.dict.type.content_type) {
                const contentTypeDictItem = this.dict.type.content_type.find(
                  (d) => d.label === contentTypeLabel
                )
                if (contentTypeDictItem) { contentTypeValue = contentTypeDictItem.value }
              }
              if (!contentTypeValue) {
                this.$message.error(
                  `无法确定内容类型, 请检查 [content_type] 字典配置中是否存在标签为'${contentTypeLabel}'的项`
                )
                return
              }
              if (this.pageMode === 'normal') {
                if (this.dict.type.warn_level) {
                  const warnLevelDictItem = this.dict.type.warn_level.find(
                    (d) => d.label === this.form.riskLevel
                  )
                  if (warnLevelDictItem) { warnLevelValue = warnLevelDictItem.value }
                }
                if (!warnLevelValue) {
                  this.$message.error(
                    `无法确定风险等级, 请检查 [warn_level] 字典配置中是否存在标签为'${this.form.riskLevel}'的项`
                  )
                  return
                }
              }
              const userList = this.selectedContacts.map((contact) => ({
                userId: contact.id, // This is the actual userId
                mobile: contact.phone,
                warnType: contact.sendType // Ensure sendType is correct
              }))
              let payload = {}
              let baseUrl
              if (process.env.VUE_APP_ENV === 'staging') {
                baseUrl = 'https://test.tyfzyuqing.com'
              } else if (process.env.VUE_APP_ENV === 'development') {
                baseUrl = 'http://localhost'
              } else {
                baseUrl = 'https://jc.tyfzyuqing.com'
              }
              if (this.pageMode === 'normal') {
                // 对于普通预警，内容直接取自预览
                let content = this.$refs.warningForm.enterpriseWxPreviewContent
                // 如果是新建的普通预警（无warningId），则追加H5链接占位符
                if (!this.warningId) {
                  content += `点击查看更多：${baseUrl}/yqwarning/h5/common/`
                }
                payload = {
                  content: content.trim(), // 使用处理过的内容
                  warnLevel: warnLevelValue,
                  title: this.form.title,
                  contentType: contentTypeValue,
                  userList: userList,
                  publishTime: this.form.time.substring(0, 16), // Make sure format is yyyy-MM-dd HH:mm:ss
                  comeFrom: this.form.source // 'source' maps to 'comeFrom'
                }
                if (this.institutionMode === 'suspected') {
                  payload.suspectedInstitution = this.form.suspectedInstitution
                } else {
                  payload.institutions = this.selectedInstitutions.map(
                    (inst) => inst.id
                  )
                }
                // 如果当前是预警模式，并且有warningId，带上uniqueId
                if (this.pageMode === 'normal' && this.warningId) {
                  payload.uniqueId = this.warningId
                } else {
                  // 如果是新建的普通预警（无warningId）
                  payload.fromNormalWarn = 1
                }
              } else {
                // Summary or Report
                let content =
                  this.pageMode === 'summary'
                    ? this.form.summaryContent
                    : this.form.reportContent
                if (this.pageMode === 'summary') {
                  content += `\n点击查看更多：${baseUrl}/yqwarning/h5/summary/`;
                  payload.imgList = this.summaryImages.map((img, index) => ({
                    fileName: img.name,
                    fileSize: String(img.size),
                    filePath: img.originalPath,
                    xh: String(index + 1)
                  }))
                } else if (this.pageMode === 'report') {
                  content += `\n点击查看更多：${baseUrl}/yqwarning/h5/report/`;
                }
                payload = {
                  ...payload, // 继承 imgList (如果存在)
                  content: content.trim(),
                  contentType: contentTypeValue,
                  userList: userList,
                  uniqueId: this.warningId ? this.warningId : undefined
                }
                if (this.pageMode === 'summary') {
                  if (this.institutionMode === 'suspected') {
                    payload.suspectedInstitution =
                      this.form.suspectedInstitution
                  } else {
                    payload.institutions = this.selectedInstitutions.map(
                      (inst) => inst.id
                    )
                  }
                } else {
                  // report mode
                  const distinctInstitutionIds = [
                    ...new Set(
                      this.selectedContacts.map((c) => c.institutionId)
                    )
                  ]
                  payload.institutions = distinctInstitutionIds
                }
              }
              this.isLoading = true
              try {
                const response = await sendWarnMsg(payload)
                if (response.code === 0 || response.code === 200) {
                  // Assuming 0 or 200 means success
                  this.$message.success(`${this.sendButtonText} 成功`)
                  // Optionally reset form or navigate away
                } else {
                  this.$message.error(
                    response.msg || `${this.sendButtonText} 失败`
                  )
                }
              } catch (error) {
                console.error('发送失败:', error)
                this.$message.error('请求失败，请检查网络或联系管理员')
              } finally {
                this.isLoading = false
              }
            })
            .catch((err) => {
              console.error('发送失败:', err)
              this.$message({
                type: 'info',
                message: '已取消发送'
              })
            })
        } else {
          this.$message.error('请检查表单填写是否完整')
          return false
        }
      })
    },
    resetForm() {
      const currentQuery = { ...this.$route.query }
      this.resetFormFields() // Resets data including selectedInstitutions and selectedContacts
      // Re-determine page mode based on original query, as resetFormFields clears form state
      // but not the underlying mode logic
      if (currentQuery.mode) {
        this.pageMode = currentQuery.mode
      } else if (currentQuery.warningId) {
        this.pageMode = 'normal'
        // If resetting a form that had a warningId, we might want to reload that warning's data
        // or clear warningId and treat as a new normal warning.
        // For now, let's assume reset means start fresh for the current mode.
        // If warningId was present, re-load it to re-populate specific fields.
        // This line will be hit after resetFormFields clear warningId. So it will setup as a new warning.
      } else {
        this.pageMode = 'normal'
      }
      if (currentQuery.warningId && this.pageMode === 'normal') {
        this.loadWarningData(currentQuery.warningId) // Reload data if it was a specific warning
      }
      this.$message.info('表单已重置')
    },
    toggleLeftPanel() {
      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed
      localStorage.setItem(
        'yqwarning_left_panel_collapsed',
        this.isLeftPanelCollapsed
      )
    },
    /**
     * 【新增】递归构建机构的父子关系映射表
     * @param {Array} nodes - 原始机构树节点数组
     * @param {Map} map - 要填充的Map对象
     */
    buildParentMapRecursive(nodes, map) {
      if (!nodes || !Array.isArray(nodes)) return
      nodes.forEach((node) => {
        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => {
            // 记录下每个子节点的 code 指向其父节点的 code
            map.set(child.code, node.code)
          })
          // 继续向更深层级递归
          this.buildParentMapRecursive(node.children, map)
        }
      })
    },
    /**
     * 【新增】处理粘贴内容并解析填充到表单
     * @param {string} text - 从文本域粘贴的完整内容
     */
    handlePasteAndParse(text) {
      if (!text || !text.trim()) {
        // 如果粘贴内容为空，可以选择清空表单或什么都不做
        // 这里我们选择清空与粘贴相关的字段
        const fieldsToClear = [
          'suspectedInstitution', 'title', 'riskLevel', 'summary',
          'source', 'time', 'link', 'analysis', 'detailPage'
        ];
        fieldsToClear.forEach(field => {
          if (this.form.hasOwnProperty(field)) {
            // 根据字段类型重置
            this.form[field] = (field === 'time') ? null : '';
          }
        });
        // 如果需要，也可以重置风险等级为默认值
        this.form.riskLevel = '低';
        this.$message.info('粘贴内容为空，已清空相关字段。');
        return
      }

      const fieldMapping = {
        机构: 'suspectedInstitution',
        标题: 'title',
        风险等级: 'riskLevel',
        摘要: 'summary',
        来源: 'source',
        时间: 'time',
        链接: 'link',
        研判分析: 'analysis',
        作者: 'author',
        详情页: 'detailPage'
      }
      const multiLineFields = ['summary', 'analysis']

      // 1. 创建一个临时的、干净的、包含所有可能被粘贴的字段的对象
      const parsedForm = {};
      for (const key in fieldMapping) {
        // 从 fieldMapping 的 value 中获取所有字段名
        const formKey = fieldMapping[key];
        // author 是临时字段，不需要在最终的form中初始化
        if (formKey !== 'author') {
          parsedForm[formKey] = ''; // 初始化为空字符串
        }
      }

      // 接下来的解析逻辑不变...
      const extractedData = {}
      const lines = text.trim().split('\n')
      let currentFieldKey = null
      lines.forEach((line) => {
        let matched = false
        for (const key in fieldMapping) {
          const regex = new RegExp(`^${key.trim()}[：:]`)
          if (regex.test(line.trim())) {
            currentFieldKey = fieldMapping[key]
            extractedData[currentFieldKey] = line
              .trim()
              .replace(regex, '')
              .trim()
            matched = true
            break
          }
        }
        if (
          !matched &&
          currentFieldKey &&
          multiLineFields.includes(currentFieldKey)
        ) {
          extractedData[currentFieldKey] += '\n' + line.trim()
        }
      })

      // 2. 将解析出的数据填充到临时的 parsedForm 中
      // Object.assign 会用 extractedData 中的值覆盖 parsedForm 中的初始空值
      Object.assign(parsedForm, extractedData);


      // --- 数据填充和特殊处理 ---
      // 现在我们用完整的 parsedForm 来更新 this.form

      // 1. 特殊处理"来源"字段
      if (parsedForm.author && parsedForm.source) {
        if (!parsedForm.source.includes(parsedForm.author)) {
          this.form.source = `${parsedForm.source}—${parsedForm.author}`
        } else {
          this.form.source = parsedForm.source
        }
      } else if (parsedForm.source) {
        this.form.source = parsedForm.source
      } else {
        // 如果新内容里没有来源，也要清空
        this.form.source = '';
      }

      // 2. 填充其他通用字段
      Object.keys(parsedForm).forEach((key) => {
        // 确保form对象中有这个属性，并且不是我们已特殊处理的字段
        if (
          this.form.hasOwnProperty(key) &&
          key !== 'source' &&
          key !== 'author'
        ) {
          // 对风险等级做精确匹配和默认值处理
          if (key === 'riskLevel') {
            if (['高', '中', '低'].includes(parsedForm[key])) {
              this.form[key] = parsedForm[key];
            } else {
              this.form[key] = '低'; // 如果粘贴的值无效，则默认为'低'
            }
          } else {
            this.form[key] = parsedForm[key];
          }
        }
      })

      // 3. 如果解析出"机构"，自动切换到"疑似机构"模式
      if (parsedForm.suspectedInstitution) {
        this.institutionMode = 'suspected'
        this.form.institutions = []
        this.selectedInstitutions = []
      }

      this.$nextTick(() => {
        this.$message.success('内容已解析并填充至表单！')
      })
    },
    /**
     * 切换链接的失效状态
     */
    toggleLinkStatus() {
      this.isLinkInvalid = !this.isLinkInvalid
      this.$message.info(
        `链接已标记为【${this.isLinkInvalid ? '失效' : '有效'}】状态`
      )
    },
    /**
     * 【新增】判断是否应该显示"点击查看更多"链接
     * 只有当联系人是 eric、陈雷、eric和陈雷 这三种情况时才显示
     */
    shouldShowMoreLink() {
      if (!this.selectedContacts || this.selectedContacts.length === 0) {
        return false
      }

      // 获取所有联系人的姓名
      const contactNames = this.selectedContacts
        .map((contact) => contact.name)
        .sort()

      // 定义允许显示"点击查看更多"的联系人组合
      const allowedCombinations = [
        ['eric'], // 只有 eric
        ['陈雷'], // 只有 陈雷
        ['eric', '陈雷'] // eric 和 陈雷
      ]

      // 检查当前联系人组合是否在允许列表中
      return allowedCombinations.some((combination) => {
        // 对组合进行排序后比较，确保顺序无关
        const sortedCombination = [...combination].sort()
        return (
          JSON.stringify(contactNames) === JSON.stringify(sortedCombination)
        )
      })
    },
    // 从 contactSelectionTree 构建扁平化的联系人列表
    buildAllContactsFlatList() {
      const flatList = []
      if (!this.contactSelectionTree) return

      this.contactSelectionTree.forEach((institutionNode) => {
        (institutionNode.children || []).forEach((groupNode) => {
          (groupNode.children || []).forEach((contactNode) => {
            // 构造一个完整的联系人对象，方便后续使用
            flatList.push({
              id: contactNode.userId,
              name: contactNode.userName,
              phone: contactNode.phoneNumber,
              sendType: contactNode.warnType || 'all',
              institutionId: institutionNode.code,
              institutionName: institutionNode.label,
              groupId: groupNode.groupId,
              groupName: groupNode.label,
              contactId: contactNode.id, // 这个是树节点的唯一ID，如 "contact_xxx"
              displayName: contactNode.label
            })
          })
        })
      })
      this.allContactsFlatList = flatList
    },
    setDefaultContacts() {
      // 如果扁平列表为空，则不执行
      if (!this.allContactsFlatList || this.allContactsFlatList.length === 0) {
        return
      }

      // 使用 Map 来存储默认联系人，可以自动处理重复（例如登录用户就是周晓龙）
      const defaultContactsMap = new Map()

      // 1. 查找并添加当前登录用户
      const currentUserContact = this.allContactsFlatList.find(
        (c) => c.name === this.nickName
      )
      if (currentUserContact) {
        defaultContactsMap.set(
          currentUserContact.contactId,
          currentUserContact
        )
      }

      // 2. 查找并添加周晓龙
      const zhouXiaolongContact = this.allContactsFlatList.find(
        (c) => c.name === '周晓龙'
      )
      if (zhouXiaolongContact) {
        defaultContactsMap.set(
          zhouXiaolongContact.contactId,
          zhouXiaolongContact
        )
      }
      // 将找到的默认联系人存入持久化数组
      this.defaultSelectedContacts = Array.from(defaultContactsMap.values())

      // 3. 将找到的默认联系人设置为已选状态
      // 我们将默认联系人与已有的选择合并，而不是直接覆盖
      const existingContactsMap = new Map(
        this.selectedContacts.map((c) => [c.contactId, c])
      )
      const mergedMap = new Map([
        ...defaultContactsMap,
        ...existingContactsMap
      ])

      this.selectedContacts = Array.from(mergedMap.values())
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  // CSS变量定义在组件容器级别，避免污染全局
  --primary-color: #001ff8;
  --secondary-color: #2c3e50;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --bg-color: #f5f7fa;
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --border-color: #ebeef5;
  min-width: 1200px;
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--secondary-color);
}

.main-layout {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: stretch;
}

.left-panel {
  flex: 1;
  min-width: 0;
  position: relative;
  transition: all 0.3s ease;

  &.collapsed {
    flex: 0 0 40px;
    min-width: 40px;

    // overflow: hidden;
    ::v-deep .detail-layout {
      opacity: 0;
      visibility: hidden;
    }
  }

  .collapse-button {
    position: absolute;
    right: -12px;
    top: 20%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: #fff;
    }

    i {
      font-size: 12px;
    }
  }

  ::v-deep .detail-layout {
    background-color: #fff;
    padding: 0;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;

    .main-content {
      height: 1315px;
      overflow-y: auto;
    }

    .side-info {
      display: none;
    }
  }
}

.right-panel {
  flex: 1;
  min-width: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Style for the mode stamp */
.mode-stamp {
  position: absolute;
  top: 0px;
  left: -18px;
  transform: rotate(-26deg);
  /* Adds a slight angle */
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  z-index: 10;
  /* Ensures it's above the card */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0.85;
  /* Makes it slightly transparent */
  border: 2px solid #fff;

  &.two-text {
    top: -2px;
    left: -14px;
  }

  &.four-text {
    top: 4px;
    left: -24px;
  }
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &.summary-card {
    background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
    border-left: 4px solid var(--primary-color);
  }

  &.warning-card {
    background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
    border-left: 4px solid var(--primary-color);
  }
}

.contact-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  color: var(--secondary-color);
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: var(--primary-color);
  }
}

::v-deep .el-dialog__header {
  // 覆盖 element 默认的内边距，使其更紧凑
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0; // 添加一条分割线，视觉效果更好
}
</style>
