import { getInstitutionTree } from '@/api/yqmonitor/institution'

const state = {
  institutionTree: [],
  currentNode: null,
  includeSubInstitutions: true // 默认: 包含下级机构
}

const mutations = {
  SET_INSTITUTION_TREE(state, tree) {
    state.institutionTree = tree
  },
  SET_CURRENT_NODE(state, node) {
    state.currentNode = node
  },
  SET_INCLUDE_SUB_INSTITUTIONS(state, include) {
    state.includeSubInstitutions = include
  }
}

const actions = {
  async fetchInstitutionTree({ commit }) {
    try {
      const response = await getInstitutionTree()
      // 后端接口返回的 `data` 是树形结构数组
      // el-tree 需要 id 和 label 字段，我们在这里做个转换
      // 假设后端返回的节点包含 code, name, children
      const transformTree = (nodes) => {
        if (!nodes || nodes.length === 0) return []
        return nodes.map(node => ({
          id: node.code, // 使用 code 作为唯一标识
          label: node.name, // 使用 name 作为显示标签
          children: node.children ? transformTree(node.children) : [],
        }))
      }
      const treeData = transformTree([response.data[0]])
      commit('SET_INSTITUTION_TREE', treeData)
    } catch (e) {
      console.error('获取机构树失败', e)
    }
  },
  setCurrentNode({ commit }, node) {
    commit('SET_CURRENT_NODE', node)
  },
  setIncludeSubInstitutions({ commit }, include) {
    commit('SET_INCLUDE_SUB_INSTITUTIONS', include)
  }
}

const getters = {
  institutionTree: (state) => state.institutionTree,
  currentNode: (state) => state.currentNode,
  includeSubInstitutions: (state) => state.includeSubInstitutions
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}