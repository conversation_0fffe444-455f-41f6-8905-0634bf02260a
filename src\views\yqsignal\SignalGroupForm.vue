<template>
  <el-dialog
    v-loading="loading"
    :title="isEdit ? '编辑信源组' : '新建信源组'"
    :visible="visible"
    width="30%"
    :before-close="handleDialogClose"
    @update:visible="val => $emit('update:visible', val)"
  >
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入信源组名称" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">{{ isEdit ? '保存' : '确定' }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'SignalGroupForm',
  props: {
    visible: <PERSON><PERSON><PERSON>,
    isEdit: <PERSON><PERSON><PERSON>,
    editData: Object
  },
  data() {
    return {
      form: {
        name: ''
      },
      formRules: {
        name: [{ required: true, message: '请输入信源组名称', trigger: 'blur' }]
      },
      loading: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.isEdit && this.editData) {
          this.form.name = this.editData.name
        } else {
          this.form.name = ''
        }
      }
    }
  },
  methods: {
    async submitForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        this.loading = true
        this.$emit('success', { ...this.form })
      })
    },
    close() {
      this.$message.info('已取消')
      this.$emit('update:visible', false)
    },
    handleDialogClose(done) {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      done()
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
