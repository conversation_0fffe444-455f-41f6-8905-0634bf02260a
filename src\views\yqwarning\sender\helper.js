// src/views/yqwarning/sender/helper.js

/**
 * 备注：flattenTreeData 函数在此场景下不再需要，可以删除或保留用于其他地方。
 * 扁平化机构树数据，并为每个节点添加父级和顶级机构信息。
 * 同时，将 API 返回的 code 和 name 映射到组件内部使用的 id 和 label。
 * 联系人信息也会被处理，并附加其所属机构的 code 和 name。
 * @param {Array} nodes 机构树节点数组 (API 返回的原始数据)
 * @param {String|null} parentCode 父机构 Code
 * @param {String|null} parentName 父机构 Name
 * @param {String|null} topLevelCode 顶级机构 Code
 * @param {String|null} topLevelName 顶级机构 Name
 * @returns {Array} 扁平化后的机构节点数组
 */
export function flattenTreeData(nodes, parentCode = null, parentName = null, topLevelCode = null, topLevelName = null) {
  let list = []
  if (!nodes || !Array.isArray(nodes)) {
    return list
  }

  nodes.forEach(node => {
    const currentTopLevelCode = parentCode === null ? node.code : topLevelCode
    const currentTopLevelName = parentName === null ? node.name : topLevelName

    const flatNode = {
      // 映射字段以适应组件现有逻辑
      id: node.code,
      label: node.name,
      // 保留原始字段名，以备不时之需
      originalCode: node.code,
      originalName: node.name,
      // 处理联系人，并为每个联系人附加上其直接所属机构的 code 和 name
      // API 返回的 contact 结构: { userId, userName, phoneNumber, institutionCode (可能没有), institutionName (可能没有), warnType }
      // 我们这里确保联系人对象中包含其父机构信息
      contacts: (node.contacts || []).map(contact => ({
        ...contact, // 保留原始联系人所有属性
        institutionCode: node.code, // 明确联系人所属机构的 code
        institutionName: node.name // 明确联系人所属机构的 name
      })),
      parentCode: parentCode,
      parentName: parentName,
      topLevelCode: currentTopLevelCode,
      topLevelName: currentTopLevelName,
      // aliasName 和 aliasNameList 也从 node 中获取 (如果API返回这些字段)
      aliasName: node.aliasName,
      aliasNameList: node.aliasNameList
      // 注意：扁平化列表中的节点不应包含 children 属性，以免造成混淆或循环引用
      // children 属性应在原始树结构中使用
    }
    list.push(flatNode)

    if (node.children && node.children.length > 0) {
      list = list.concat(flattenTreeData(node.children, node.code, node.name, currentTopLevelCode, currentTopLevelName))
    }
  })
  return list
}

// 用于主表单的机构选择。
export function mapApiTreeToComponentTree(apiNodes) {
  if (!apiNodes || !Array.isArray(apiNodes)) {
    return []
  }
  return apiNodes.map(node => ({
    id: node.code, // el-tree 使用的 node-key
    label: node.name, // el-tree 使用的展示文本
    originalData: node, // 保留原始节点数据
    contacts: (node.contacts || []).map(contact => ({ // 保持联系人结构，后续统一处理
      ...contact
    })),
    children: mapApiTreeToComponentTree(node.children) // 递归处理子节点
  }))
}

/**
 * 【新增辅助函数】
 * 递归地将API返回的嵌套机构树拍平为一级列表。
 * @param {Array} nodes - API返回的机构节点数组
 * @returns {Array} - 扁平化的机构对象列表
 */
function flattenApiTree(nodes) {
  let list = []
  if (!nodes || !Array.isArray(nodes)) {
    return list
  }

  nodes.forEach(node => {
    // 将当前节点（不包含其children属性）添加到列表中
    const { children, ...rest } = node
    list.push(rest)

    // 如果存在子节点，则递归地拍平它们并合并到结果中
    if (children && children.length > 0) {
      list = list.concat(flattenApiTree(children))
    }
  })
  return list
}

/**
 * 【最终修正版】
 * 将API返回的机构用户树转换为联系人选择器所需的树形结构。
 * 此版本通过先拍平树来解决深层嵌套机构的联系人无法显示的问题。
 * @param {Array} apiNodes - 从 getInstitutionUserTree API 获取的原始数据
 * @returns {Array} - 适用于 el-tree 的三级结构树
 */
export function buildContactSelectionTree(apiNodes) {
  if (!apiNodes || !Array.isArray(apiNodes)) return []

  // 步骤 1: 将整个嵌套树结构拍平为单个机构列表
  const flatInstitutionList = flattenApiTree(apiNodes)

  // 步骤 2: 遍历扁平化的列表，为每个包含联系人的机构创建节点
  return flatInstitutionList.map(institution => {
    const institutionNode = {
      id: `institution_${institution.code}`,
      label: institution.name,
      isInstitution: true,
      code: institution.code,
      disabled: true,
      children: []
    }

    // 将机构自带的分组和未分组的联系人统一处理
    const allGroups = institution.groups ? [...institution.groups] : []
    if (institution.contacts && institution.contacts.length > 0) {
      allGroups.push({
        groupId: `ungrouped_${institution.code}`,
        groupName: '[未分组]', // 给用户一个清晰的标识
        contacts: institution.contacts
      })
    }

    // 遍历所有分组，创建子节点
    allGroups.forEach(group => {
      if (group.contacts && group.contacts.length > 0) {
        const groupNode = {
          id: `group_${group.groupId}`,
          label: group.groupName,
          isGroup: true,
          groupId: group.groupId,
          children: (group.contacts || []).map(contact => ({
            id: `contact_${contact.userId}`,
            label: `${contact.userName} (${contact.phoneNumber || ''})`.trim(), // 增加健壮性
            isContact: true,
            contactId: `contact_${contact.userId}`,
            ...contact
          }))
        }
        institutionNode.children.push(groupNode)
      }
    })

    // 只有当机构下确实有子节点（即有有效联系人）时，才返回这个机构节点
    if (institutionNode.children.length > 0) {
      return institutionNode
    }
    return null
  }).filter(Boolean) // 过滤掉所有为 null 的节点（即没有任何联系人的机构）
}
