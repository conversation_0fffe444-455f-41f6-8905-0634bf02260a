# 普通预警在有舆情id的情况下也要展示媒体播放组件

## 1. 舆情id获取的入口
在src/views/yqwarning/h5/indexCommon.vue中的getSmsMsgInfoByMsgId的回调数据处理中  如果存在：形如`详情页:https://jc.tyfzyuging.com/yqmonitor/detail/douyin_7522824082267442491` 这样的字符串数据
就尝试解析，以/yqmonitor/detail/:id的模式来匹配出其中的舆情id


## 2. 根据舆情id获取媒体数据
步骤1拿到舆情id后就可以参考src/views/yqwarning/h5/index.vue中的fetchPublicSentimentById接口调用代码，以及src/views/yqwarning/h5/components/InfoDetail.vue、src/views/yqwarning/h5/components/VideoPlayer.vue中媒体组件播放代码 完成以下逻辑：

在src/views/yqwarning/h5/components/SimpleInfoDetail.vue中添加div.video-container这个媒体播放的入口以及media-player播放组件代码