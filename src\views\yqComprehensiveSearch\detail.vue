<template>
  <div class="comprehensive-search-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <p>正在加载详情数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <p class="error-message">{{ error }}</p>
      <el-button type="primary" @click="fetchDetailData">重试</el-button>
    </div>

    <!-- 详情数据 -->
    <div v-else-if="detailData" class="detail-grid-container">
      <!-- Left Panel: Post Content -->
      <div class="post-panel">
        <div class="title-container">
          <h1 class="post-title">{{ detailData.title }}</h1>
          <div class="title-tags">
            <el-tag type="info" effect="plain">失效</el-tag>
            <el-tag>非敏感</el-tag>
          </div>
        </div>

        <div class="post-meta">
          <a :href="detailData.url" target="_blank" class="post-url">{{ detailData.url }}</a>
          <div class="meta-line">
            <span class="author-name">懂车帝</span>
            <span class="post-time">发布于：{{ detailData.post_create_time }}</span>
            <div class="post-actions">
              <span><i class="el-icon-share"></i> 0</span>
              <span><i class="el-icon-chat-dot-round"></i> 0</span>
              <span><i class="el-icon-thumb"></i> 0</span>
            </div>
          </div>
        </div>

        <el-divider></el-divider>

        <div class="post-body">
          <h3 class="body-heading">正文</h3>
          <p class="body-content">{{ detailData.content }}</p>
        </div>
      </div>

      <!-- Right Panel: Info & Analysis -->
      <div class="info-panel">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <div class="info-card">
              <div class="info-item">
                <span class="item-label">作品ID</span>
                <span class="item-value">{{ workId }}</span>
              </div>
            </div>

            <div class="info-card">
              <h4 class="card-title">作者信息</h4>
              <div class="author-header">
                <el-avatar :size="40" :src="detailData.avatar"></el-avatar>
                <div class="author-name-details">
                  <span class="author-nickname">{{ detailData.nickname }}</span>
                  <el-tag type="info" size="small">未认证</el-tag>
                </div>
              </div>
              <div class="info-item">
                <span class="item-label">用户主页:</span>
                <a :href="detailData.user_url" target="_blank" class="user-profile-link">{{ detailData.user_url }}</a>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>
    </div>

    <!-- 默认状态 -->
    <div v-else class="initial-state">
      <p>当前无数据显示，请从列表选择一项查看详情。</p>
    </div>
  </div>
</template>

<script>
import { getSearchDetail, getToken } from '@/api/yqComprehensiveSearch'

export default {
  name: 'ComprehensiveSearchDetail',
  data() {
    return {
      searchId: '',
      detailData: null,
      loading: false,
      error: null,
      activeTab: 'basic',
      token: '', // 新增 token 变量
    }
  },
  computed: {
    workId() {
      if (!this.detailData || !this.detailData.url) {
        return 'N/A';
      }
      try {
        const url = new URL(this.detailData.url);
        const domain = url.hostname.split('.')[0]; // e.g., 'dongchedi'
        const pathParts = url.pathname.split('/');
        const articleId = pathParts[pathParts.length - 1];
        if (domain && articleId && !isNaN(Number(articleId))) {
          return `app_${domain}_${articleId}`;
        }
      } catch (e) {
        console.error("Error parsing URL for workId", e);
      }
      // Fallback if parsing fails
      const match = this.detailData.url.match(/article\/(\d+)/);
      if (match && match[1]) {
        return `app_dongchedi_${match[1]}`;
      }
      return 'ID解析失败';
    }
  },
  created() {
    this.parseUrlParams()
    if (this.searchId) {
      // 先获取 token
      getToken().then(res => {
        console.log('getToken', res);
        if (res && res.code === 200 && res.data) {
          this.token = res.data.xiaoyToken;
        } else {
          this.token = '';
        }
        this.fetchDetailData();
      }).catch(() => {
        this.token = '';
        this.fetchDetailData();
      });
    }
  },
  methods: {
    parseUrlParams() {
      const path = this.$route.path
      const pathSegments = path.split('/')
      const detailIndex = pathSegments.findIndex(segment => segment === 'detail')
      if (detailIndex !== -1 && detailIndex + 1 < pathSegments.length) {
        this.searchId = decodeURIComponent(pathSegments[detailIndex + 1])
        console.log('解析到的搜索ID:', this.searchId)
      } else {
        console.warn('未在URL中找到搜索ID参数')
      }
    },
    async fetchDetailData() {
      if (!this.searchId) {
        this.error = '无效的搜索ID'
        return
      }

      this.loading = true
      this.error = null
      this.detailData = null

      try {
        const response = await getSearchDetail({
          unity_id: this.searchId
        }, this.token)

        if (response && response.code === 0) {
          this.detailData = response.data
          console.log('获取到的详情数据:', this.detailData)
        } else {
          throw new Error(response.message || '获取数据失败，响应格式不正确')
        }
      } catch (error) {
        console.error('获取详情数据失败:', error)
        this.error = error.message || '获取详情数据失败，请重试'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.comprehensive-search-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.detail-grid-container {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Left Panel: Post Details */
.post-panel {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.post-title {
  font-size: 22px;
  font-weight: 600;
  line-height: 1.4;
  white-space: pre-wrap;
  margin: 0;
}

.title-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-top: 4px;
}

.post-meta {
  font-size: 14px;
  color: #909399;
}

.post-url {
  display: block;
  margin-bottom: 8px;
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.post-url:hover {
  text-decoration: underline;
}

.meta-line {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.author-name {
  color: #606266;
}

.post-actions {
  display: flex;
  gap: 20px;
  align-items: center;
  color: #606266;
  margin-left: auto;
}

.post-actions span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-body {
  margin-top: 20px;
}

.body-heading {
  font-size: 16px;
  font-weight: 500;
  padding-left: 8px;
  border-left: 3px solid #409eff;
  margin-bottom: 16px;
}

.body-content {
  font-size: 15px;
  line-height: 1.8;
  color: #303133;
  white-space: pre-wrap;
}

/* Right Panel: Analysis Info */
.info-panel {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.detail-tabs {
  padding: 0 20px 20px;
}

.info-card {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #f0f2f5;
  border-radius: 4px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #303133;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.item-label {
  color: #606266;
  white-space: nowrap;
  margin-right: 10px;
}

.item-value {
  color: #303133;
  word-break: break-all;
  text-align: right;
}

.author-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.author-name-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.author-nickname {
  font-weight: 500;
  color: #303133;
}

.user-profile-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  word-break: break-all;
  text-align: right;
}

.user-profile-link:hover {
  text-decoration: underline;
}

/* States */
.loading-state,
.error-state,
.initial-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  color: #909399;
}

.loading-state p {
  margin-top: 16px;
}

.error-state {
  background-color: #fef0f0;
  border-color: #fde2e2;
}

.error-message {
  color: #f56c6c;
  margin-bottom: 16px;
}

.el-divider {
  margin: 20px 0;
}
</style>