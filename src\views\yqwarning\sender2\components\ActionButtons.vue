<template>
  <div class="card">
    <div class="action-buttons">
      <el-button
        type="primary"
        icon="el-icon-s-promotion"
        @click="handleSubmit"
      >
        {{ sendButtonText }}
      </el-button>
      <el-button
        icon="el-icon-document-copy"
        @click="handleCopy"
      >
        复制输出内容
      </el-button>
      <el-button
        icon="el-icon-refresh"
        @click="handleReset"
      >
        重置
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActionButtons',
  props: {
    sendButtonText: {
      type: String,
      default: '发送'
    }
  },
  methods: {
    handleSubmit() {
      this.$emit('submit')
    },
    handleCopy() {
      this.$emit('copy')
    },
    handleReset() {
      this.$emit('reset')
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
