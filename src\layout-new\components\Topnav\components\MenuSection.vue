<template>
  <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal">
    <template v-for="menu in topMenus">
      <!-- 情况一：渲染成可点击的顶级菜单项 -->
      <!-- 条件：只有一个子路由，并且该子路由的path是'index -->
      <template v-if="menu.children && menu.children.length === 1 && menu.children[0].path === 'index'">
        <el-menu-item :index="menu.path" :key="'top-' + menu.path">
          <router-link :to="resolvePath(menu.path, menu.children[0].path)">
            <svg-icon v-if="menu.meta && menu.meta.icon" :icon-class="menu.meta.icon" />
            <span>{{ menu.meta.title }}</span>
          </router-link>
        </el-menu-item>
      </template>

      <!-- 情况二：渲染成带下拉的顶级菜单 -->
      <!-- 条件：有多个子路由 -->
      <el-submenu v-else-if="menu.children && menu.children.length > 0" :key="menu.path" :index="menu.path">
        <template slot="title">
          <svg-icon v-if="menu.meta && menu.meta.icon" :icon-class="menu.meta.icon" />
          <span>{{ menu.meta.title }}</span>
        </template>
        <template v-for="child in menu.children">
          <!-- 注意：这里不渲染path为 'index' 的子菜单，因为它已经被父级代表了 -->
          <el-menu-item v-if="!child.hidden && child.path !== 'index'" :key="child.path"
            :index="resolvePath(menu.path, child.path)" class="my-submenu">
            <router-link :to="resolvePath(menu.path, child.path)">
              <svg-icon v-if="child.meta && child.meta.icon" :icon-class="child.meta.icon" />
              <span>{{ child.meta.title }}</span>
            </router-link>
          </el-menu-item>
        </template>
      </el-submenu>

      <!-- 情况三：独立的、没有子路由的菜单项 (如外链) -->
      <el-menu-item v-else-if="!menu.children" :key="'lonely-' + menu.path" :index="menu.path"
        @click="handleLink(menu)">
        <template v-if="isExternal(menu.path)">
          <svg-icon v-if="menu.meta && menu.meta.icon" :icon-class="menu.meta.icon" />
          <span>{{ menu.meta && menu.meta.title }}</span>
        </template>
        <router-link v-else :to="menu.path">
          <svg-icon v-if="menu.meta && menu.meta.icon" :icon-class="menu.meta.icon" />
          <span>{{ menu.meta && menu.meta.title }}</span>
        </router-link>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: 'MenuSection',
  computed: {
    ...mapGetters(["sidebarRouters"]),
    /**
    * 计算当前激活的菜单项索引
    */
    activeIndex() {
      const route = this.$route;
      const { path } = route;
      // 我们的目标是获取顶级菜单的路径，通常是路径的第一部分
      // 例如，当前路径是 /yqmonitor/index 或 /yqmonitor/detail/1
      // 我们需要提取出 /yqmonitor 作为激活的 index
      const pathSegments = path.split('/');
      if (pathSegments.length > 1 && pathSegments[1]) {
        return '/' + pathSegments[1];
      }
      return path; // 如果是根路径或者其他简单路径，则直接返回
    },
    topMenus() {
      // 深拷贝一份路由数据，避免直接修改Vuex中的state，这是一个好的实践
      const routers = JSON.parse(JSON.stringify(this.sidebarRouters));

      // 1. 先对顶级菜单进行过滤，参照 permission.js 中的 findFirstValidRoute 函数
      const excludedBusinessRoutes = [
        '/yqmonitor-business',
        '/followed-events-business', 
        '/target-monitor-business'
      ];
      
      const firstLevelFiltered = routers.filter(
        (item) =>
          !item.hidden &&
          !excludedBusinessRoutes.includes(item.path) &&
          item.path !== ''
      );

      // 2. 对所有菜单进行递归过滤，并返回最终结果
      const finalMenus = this.filterRecursive(firstLevelFiltered);

      console.log("最终渲染的菜单:", finalMenus);
      return finalMenus;
    }
  },
  methods: {
    /**
 * 递归过滤函数
 * @param {Array} routes - 需要过滤的路由数组
 * @returns {Array} - 过滤后的路由数组
 */
    filterRecursive(routes) {
      const result = [];
      for (const route of routes) {
        // 如果路由本身是 hidden: true，直接跳过
        if (route.hidden) {
          continue;
        }

        // 如果有子路由，则递归过滤子路由
        if (route.children && route.children.length > 0) {
          route.children = this.filterRecursive(route.children);
        }

        // **核心逻辑**：
        // 一个菜单项应该被保留，必须满足以下条件之一：
        // 1. 它没有子菜单 (它本身就是一个可点击项)。
        // 2. 或者，在递归过滤之后，它仍然拥有子菜单。
        if (!route.children || route.children.length > 0) {
          result.push(route);
        }
      }
      return result;
    },
    isExternal(path) {
      return /^(https?:|mailto:|tel:)/.test(path)
    },
    handleLink(menu) {
      if (this.isExternal(menu.path)) {
        window.open(menu.path, '_blank')
      }
    },
    /**
 * 路径解析函数 (最终简化版)
 * @param {string} basePath 基础路径 (例如：'/system')
 * @param {string} routePath 路由路径 (例如：'user')
 */
    resolvePath(basePath, routePath) {
      // 检查子路由路径是否是外部链接，如果是，直接返回
      if (/^(https?:|mailto:|tel:)/.test(routePath)) {
        return routePath;
      }

      // 直接进行安全的字符串拼接
      // 确保 basePath 和 routePath 之间只有一个斜杠
      return (basePath.endsWith('/') ? basePath : basePath + '/') + routePath;
    }
  }
}
</script>

<style scoped lang="scss">
.el-menu-demo {
  flex: 1;
  margin-left: 20px;
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;

  // 自定义横向滚动条样式
  &::-webkit-scrollbar {
    height: 6px; // 设置滚动条高度为6px，更细
  }

  &::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  >.el-menu-item,
  >.el-submenu {
    height: 100%;
    line-height: 50px;

    span {
      margin-left: 5px;
    }
  }

  >.el-submenu {

    ::v-deep>.el-submenu__title,
    >.el-submenu__title:hover {
      height: 50px;
      line-height: 50px;
    }
  }
}

.my-submenu {
  span {
    margin-left: 5px;
  }
}
</style>
