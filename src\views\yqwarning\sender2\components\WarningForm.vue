<template>
  <el-form ref="warningForm" :model="value" :rules="rules" label-width="100px">
    <template v-if="pageMode === 'normal'">
      <el-form-item label="标题" prop="title" class="form-row">
        <el-input v-model="localForm.title" placeholder="请输入标题" @input="handleInput" />
      </el-form-item>
      <el-form-item label="来源" prop="source" class="form-row">
        <el-input v-model="localForm.source" placeholder="请输入来源" @input="handleInput" />
      </el-form-item>
      <el-form-item label="摘要" prop="summary" class="form-row">
        <el-input v-model="localForm.summary" type="textarea" :rows="10" placeholder="请输入摘要" @input="handleInput" />
      </el-form-item>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <el-form-item label="时间" prop="time">
            <el-date-picker v-model="localForm.time" type="datetime" placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm" style="width: 100%" @change="handleInput" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="链接" prop="link">
            <!-- 使用 append slot 来添加按钮 -->
            <el-input v-model="localForm.link" placeholder="请输入链接" @input="handleInput">
              <el-button slot="append" :type="isLinkInvalid ? 'success' : ''" style="width: 70px;"
                @click="handleToggleLinkStatus">
                {{ isLinkInvalid ? '恢复' : '失效' }}
              </el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item v-if="institutionMode === 'institution'" label="机构" prop="institutions" required class="form-row">
        <div style="display: flex; align-items: center; width: 100%;">
          <div class="institution-selector" style="flex-grow: 1;" @click="handleShowInstitutionDialog">
            <el-tag v-for="inst in selectedInstitutions" :key="inst.id" closable @close="handleRemoveInstitution(inst)">
              {{ inst.label }}
            </el-tag>
            <span v-if="!selectedInstitutions.length" class="placeholder-text">点击选择机构</span>
          </div>
          <el-button type="text" style="margin-left: 10px; flex-shrink: 0;"
            @click="handleToggleInstitutionMode">切换疑似机构</el-button>
        </div>
      </el-form-item>
      <el-form-item v-else label="疑似机构" prop="suspectedInstitution" required class="form-row">
        <div style="display: flex; align-items: center; width: 100%;">
          <el-input v-model="localForm.suspectedInstitution" placeholder="请输入疑似机构名称" style="flex-grow: 1;"
            @input="handleInput" />
          <el-button type="text" style="margin-left: 10px; flex-shrink: 0;"
            @click="handleToggleInstitutionMode">切换机构</el-button>
        </div>
      </el-form-item>
      <el-form-item label="风险等级" prop="riskLevel" class="form-row">
        <el-radio-group v-model="localForm.riskLevel" @change="handleInput">
          <el-radio-button label="高"><i class="el-icon-warning" style="color: #f56c6c" /> 高</el-radio-button>
          <el-radio-button label="中"><i class="el-icon-warning" style="color: #e6a23c" /> 中</el-radio-button>
          <el-radio-button label="低"><i class="el-icon-warning" style="color: #67c23a" /> 低</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="研判分析" prop="analysis" class="form-row" v-loading="analyzeLoading">
        <el-input v-model="localForm.analysis" type="textarea" :rows="4" autosize placeholder="请输入研判分析，或点击下方按钮由 AI 自动填充"
          @input="handleInput" />
        <!-- AI 填充按钮 -->
        <div style="display: flex; align-items: center; margin-top: 8px;">
          <el-button type="primary" icon="el-icon-magic-stick" size="small" plain @click="$emit('ai-analyze')">
            AI 智能填充
          </el-button>
          <span style="font-size: 12px; color: #909399; margin-left: 10px;">
            将使用【摘要】和【链接】进行分析，并覆盖当前【标题】和【研判分析】内容
          </span>
        </div>
      </el-form-item>
      <el-form-item label="选择联系人" prop="contacts" required>
        <contact-selector :selected-contacts="selectedContacts" :selected-institutions="selectedInstitutions"
          :page-mode="pageMode" :institution-mode="institutionMode" @remove-contact="$emit('remove-contact', $event)"
          @remove-group="$emit('remove-group', $event)" @add="$emit('add-contacts')" />
      </el-form-item>
      <el-form-item label="预警内容预览" class="form-row">
        <div class="preview-box">{{ enterpriseWxPreviewContent }}</div>
        <div class="char-count" :class="{ 'char-count-warning': previewCharCount > maxContentBytes }">
          企业微信内容字节数: {{ previewCharCount }} / {{ maxContentBytes }}
          <span v-if="previewCharCount > maxContentBytes">(已超长，将截断)</span>
        </div>
      </el-form-item>
    </template>
    <template v-if="pageMode === 'summary'">
      <el-form-item label="汇总内容" prop="summaryContent" class="form-row">
        <el-input v-model="localForm.summaryContent" type="textarea" :rows="20" placeholder="请输入汇总内容"
          @input="handleInput" />
        <div class="char-count" :class="{ 'char-count-warning': currentContentByteCount > maxContentBytes }">
          内容字节数: {{ currentContentByteCount }} / {{ maxContentBytes }}
          <span v-if="currentContentByteCount > maxContentBytes">(已超长，将无法发送)</span>
        </div>
      </el-form-item>
      <!-- 汇总图片上传 -->
      <el-form-item label="上传图片" prop="summaryImages" class="form-row">
        <ImageUpload ref="summaryImageUpload" :action="'/business/smsMsg/upload/image'" :limit="9" :file-size="10"
          @on-change="$emit('summary-image-change', $event)" />
      </el-form-item>
      <el-form-item v-if="institutionMode === 'institution'" label="机构" prop="institutions" required class="form-row">
        <div style="display: flex; align-items: center; width: 100%;">
          <div class="institution-selector" style="flex-grow: 1;" @click="handleShowInstitutionDialog">
            <el-tag v-for="inst in selectedInstitutions" :key="inst.id" closable @close="handleRemoveInstitution(inst)">
              {{ inst.label }}
            </el-tag>
            <span v-if="!selectedInstitutions.length" class="placeholder-text">点击选择机构</span>
          </div>
        </div>
      </el-form-item>
      <el-form-item v-else label="疑似机构" prop="suspectedInstitution" required class="form-row">
        <div style="display: flex; align-items: center; width: 100%;">
          <el-input v-model="localForm.suspectedInstitution" placeholder="请输入疑似机构名称" style="flex-grow: 1;"
            @input="handleInput" />
          <el-button type="text" style="margin-left: 10px; flex-shrink: 0;"
            @click="handleToggleInstitutionMode">切换机构</el-button>
        </div>
      </el-form-item>
      <el-form-item label="选择联系人" prop="contacts" required>
        <contact-selector :selected-contacts="selectedContacts" :selected-institutions="selectedInstitutions"
          :page-mode="pageMode" :institution-mode="institutionMode" @remove-contact="$emit('remove-contact', $event)"
          @remove-group="$emit('remove-group', $event)" @add="$emit('add-contacts')" />
      </el-form-item>
    </template>
    <template v-if="pageMode === 'report'">
      <el-form-item label="报告内容" prop="reportContent" class="form-row">
        <el-input v-model="localForm.reportContent" type="textarea" :rows="20" placeholder="请输入早晚报内容"
          @input="handleInput" />
        <div class="char-count" :class="{ 'char-count-warning': currentContentByteCount > maxContentBytes }">
          内容字节数: {{ currentContentByteCount }} / {{ maxContentBytes }}
          <span v-if="currentContentByteCount > maxContentBytes">(已超长，将无法发送)</span>
        </div>
      </el-form-item>
      <el-form-item label="选择联系人" prop="contacts" required>
        <contact-selector :selected-contacts="selectedContacts" :selected-institutions="selectedInstitutions"
          :page-mode="pageMode" :institution-mode="institutionMode" @remove-contact="$emit('remove-contact', $event)"
          @remove-group="$emit('remove-group', $event)" @add="$emit('add-contacts')" />
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import ContactSelector from './ContactSelector.vue'
import ImageUpload from '@/components/ImageUpload/index2.vue'


export default {
  name: 'WarningForm',
  components: {
    ContactSelector,
    ImageUpload
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    },
    pageMode: {
      type: String,
      default: 'normal'
    },
    institutionMode: {
      type: String,
      default: 'institution'
    },
    selectedInstitutions: {
      type: Array,
      default: () => []
    },
    isLinkInvalid: {
      type: Boolean,
      default: false
    },
    selectedContacts: {
      type: Array,
      default: () => []
    },
    warningId: {
      type: [String, Number],
      default: null
    },
    analyzeLoading: {
      type: Boolean,
      default: false
    },
    maxContentBytes: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      localForm: { ...this.value }
    }
  },
  computed: {
    /**
     * 一个辅助计算属性，用于生成最终显示的链接
     */
    displayLink() {
      if (!this.localForm.link) return ''
      // 如果链接被标记为失效，则附加提示信息
      return this.isLinkInvalid ? `${this.localForm.link}【链接已无法查看】` : this.localForm.link
    },
    enterpriseWxPreviewContent() {
      if (this.pageMode !== 'normal') return ''
      let institutionLine
      if (this.institutionMode === 'suspected') {
        institutionLine = `机构：${this.localForm.suspectedInstitution || '未指定'}\n`
      } else {
        const institutionNames = this.selectedInstitutions.map(inst => inst.originalData?.shortName || inst.label).join('、')
        institutionLine = `机构：${institutionNames || '未指定'}\n`
      }
      let content = institutionLine
      content += `标题：${this.localForm.title || ''}\n`
      content += `风险等级：${this.localForm.riskLevel || ''}\n`
      content += `摘要：${this.localForm.summary || ''}\n`
      content += `来源：${this.localForm.source || ''}\n`
      content += `时间：${this.localForm.time ? this.localForm.time.substring(0, 16) : ''}\n`
      // 使用新的 displayLink 计算属性来获取链接内容
      content += `链接：${this.displayLink || ''}\n`
      // 只有当研判分析有内容时才添加
      if (this.localForm.analysis && this.localForm.analysis.trim()) {
        content += `研判分析：${this.localForm.analysis}\n`
      }
      // 只有当详情页有内容时才添加
      if (this.localForm.detailPage && this.localForm.detailPage.trim()) {
        content += `详情页：${this.localForm.detailPage}\n`
      }
      if (this.warningId) {
        let baseUrl
        if (process.env.VUE_APP_ENV === 'staging') {
          baseUrl = 'https://test.tyfzyuqing.com'
        } else if (process.env.VUE_APP_ENV === 'development') {
          baseUrl = 'http://localhost'
        } else {
          baseUrl = 'https://jc.tyfzyuqing.com'
        }
        content += `点击查看更多：${baseUrl}/yqwarning/h5/${this.warningId}`
      }
      return content.trimStart()
    },
    previewCharCount() {
      if (this.pageMode !== 'normal') return 0
      return new TextEncoder().encode(this.enterpriseWxPreviewContent).length
    },
    currentContent() {
      if (this.pageMode === 'normal') {
        return this.enterpriseWxPreviewContent
      } else if (this.pageMode === 'summary') {
        return this.localForm.summaryContent
      } else if (this.pageMode === 'report') {
        return this.localForm.reportContent
      }
      return ''
    },
    currentContentByteCount() {
      return new TextEncoder().encode(this.currentContent).length
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.localForm = { ...newVal }
      },
      deep: true
    }
  },
  methods: {
    handleInput() {
      this.$emit('input', { ...this.localForm })
    },
    handleShowInstitutionDialog() {
      this.$emit('show-institution-dialog')
    },
    handleToggleInstitutionMode() {
      this.$emit('toggle-institution-mode')
    },
    handleRemoveInstitution(inst) {
      this.$emit('remove-institution', inst)
    },
    handleToggleLinkStatus() {
      this.$emit('toggle-link-status')
    },
    // 暴露表单验证方法
    validate(callback) {
      return this.$refs.warningForm.validate(callback)
    },
    resetFields() {
      this.$refs.warningForm.resetFields()
    },
    clearValidate(props) {
      this.$refs.warningForm.clearValidate(props)
    }
  }
}
</script>

<style scoped lang="scss">
.form-row {
  margin-bottom: 18px;
}

.institution-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 42px;
  line-height: normal;
  cursor: pointer;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--primary-color);
  }
}

.placeholder-text {
  color: #c0c4cc;
}

.preview-box {
  border: 1px solid var(--border-color);
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  min-height: 150px;
  max-height: 250px;
  overflow: auto;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.char-count {
  text-align: right;
  color: var(--info-color);
  margin-top: 8px;
  font-size: 13px;

  &.char-count-warning {
    color: var(--danger-color);
    font-weight: 500;
  }
}

/* CSS变量定义 */
:root {
  --primary-color: #001ff8;
  --border-color: #ebeef5;
  --info-color: #909399;
  --danger-color: #f56c6c;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-textarea__inner {
  resize: vertical;
  min-height: 80px !important;
  overflow-y: auto !important;
}

::v-deep .el-radio-button__inner {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
