<template>
  <div class="operations-maintenance-bg">
    <div class="card">
      <div class="card-title">消息队列管理</div>
      <div class="btn-group">
        <el-button type="primary" @click="handleConsumer('start', 'wyyl')">开启万有引力消费者</el-button>
        <el-button type="danger" @click="handleConsumer('stop', 'wyyl')">关闭万有引力消费者</el-button>
        <el-button type="primary" @click="handleConsumer('start', 'delay')">开启延迟队列消费者</el-button>
        <el-button type="danger" @click="handleConsumer('stop', 'delay')">关闭延迟队列消费者</el-button>
      </div>
      <div class="result-box">
        <span>操作结果：</span>
        <div class="result-content">{{ consumerResult }}</div>
      </div>
    </div>
    <div class="card">
      <div class="card-title">缓存与日志管理</div>
      <div class="btn-group">
        <el-button type="primary" @click="handleCache('bds')">刷新菜单表达式缓存</el-button>
        <el-button type="primary" @click="handleCache('jg')">刷新机构缓存</el-button>
        <el-button type="success" @click="handleCache('log0')">开启HTTP日志</el-button>
        <el-button type="danger" @click="handleCache('log1')">关闭HTTP日志</el-button>
      </div>
      <div class="result-box">
        <span>缓存/日志操作结果：</span>
        <div class="result-content">{{ cacheResult }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { controlConsumer, refreshCache } from '@/api/yqmonitor/operationsMaintenance'

export default {
  name: 'OperationsMaintenance',
  data() {
    return {
      consumerResult: '',
      cacheResult: ''
    }
  },
  methods: {
    async handleConsumer(action, type) {
      // type参数可用于区分不同消费者，若接口一致可省略
      try {
        const res = await controlConsumer(action)
        this.consumerResult = res.data || res.msg || '无返回'
      } catch (e) {
        this.consumerResult = '请求失败'
      }
    },
    async handleCache(type) {
      try {
        const res = await refreshCache(type)
        this.cacheResult = res.msg || '无返回'
      } catch (e) {
        this.cacheResult = '请求失败'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.operations-maintenance-bg {
    background: #f4f6fa;
    min-height: 100vh;
    padding: 32px;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
    padding: 32px 28px 24px 28px;
    margin-bottom: 0;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 24px;
    letter-spacing: 1px;
}

.btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
}

.result-box {
    margin-bottom: 0;
    .result-content {
        min-height: 40px;
        background: #f5f7fa;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 10px;
        margin-top: 8px;
        color: #333;
        font-size: 15px;
    }
}
</style>

