<template>
  <div v-loading="isLoading" class="app-container warning-sender-page">
    <div class="main-layout">
      <div v-if="pageMode === 'normal' && warningId" class="left-panel" :class="{ 'collapsed': isLeftPanelCollapsed }">
        <div class="collapse-button" @click="toggleLeftPanel">
          <i :class="isLeftPanelCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'" />
        </div>
        <Detail :item="detailItem" />
      </div>
      <!-- 用于普通预警模式下快速填充 (无 warningId 时) -->
      <div v-if="pageMode === 'normal' && !warningId" class="left-panel" :class="{ 'collapsed': isLeftPanelCollapsed }">
        <div class="collapse-button" @click="toggleLeftPanel">
          <i :class="isLeftPanelCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'" />
        </div>
        <div class="card" style="padding: 20px; height: 100%; display: flex; flex-direction: column;">
          <el-form label-position="top" style="height: 100%; display: flex; flex-direction: column;">
            <el-form-item
              label="粘贴内容以快速填充"
              style="flex-grow: 1; margin-bottom: 0; display: flex; flex-direction: column;"
            >
              <el-input
                v-show="!isLeftPanelCollapsed"
                v-model="pastedContent"
                type="textarea"
                :rows="20"
                placeholder="请在此处粘贴标准格式的内容（支持标准发送格式和来自其他系统的格式），系统将自动解析并填充右侧表单。"
                class="fill-textarea"
                @change="handlePasteAndParse"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="right-panel">
        <div
          class="mode-stamp"
          :class="{ 'two-text': warningText.length === 2, 'four-text': warningText.length === 4 }"
        >
          {{ warningText }}
        </div>
        <div class="card">
          <el-form ref="warningForm" :model="form" :rules="rules" label-width="100px">
            <template v-if="pageMode === 'normal'">
              <el-form-item label="标题" prop="title" class="form-row">
                <el-input v-model="form.title" placeholder="请输入标题" />
              </el-form-item>
              <el-form-item label="来源" prop="source" class="form-row">
                <el-input v-model="form.source" placeholder="请输入来源" />
              </el-form-item>
              <el-form-item label="摘要" prop="summary" class="form-row">
                <el-input v-model="form.summary" type="textarea" :rows="10" placeholder="请输入摘要" />
              </el-form-item>
              <el-row :gutter="20" class="form-row">
                <el-col :span="12">
                  <el-form-item label="时间" prop="time">
                    <el-date-picker
                      v-model="form.time"
                      type="datetime"
                      placeholder="选择日期时间"
                      value-format="yyyy-MM-dd HH:mm"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="链接" prop="link">
                    <!-- 使用 append slot 来添加按钮 -->
                    <el-input v-model="form.link" placeholder="请输入链接">
                      <el-button
                        slot="append"
                        :type="isLinkInvalid ? 'success' : ''"
                        style="width: 70px;"
                        @click="toggleLinkStatus"
                      >
                        {{ isLinkInvalid ? '恢复' : '失效' }}
                      </el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item
                v-if="institutionMode === 'institution'"
                label="机构"
                prop="institutions"
                required
                class="form-row"
              >
                <div style="display: flex; align-items: center; width: 100%;">
                  <div class="institution-selector" style="flex-grow: 1;" @click="showInstitutionDialog">
                    <el-tag
                      v-for="inst in selectedInstitutions"
                      :key="inst.id"
                      closable
                      @close="handleRemoveInstitution(inst)"
                    >
                      {{ inst.label }}
                    </el-tag>
                    <span v-if="!selectedInstitutions.length" class="placeholder-text">点击选择机构</span>
                  </div>
                  <el-button
                    type="text"
                    style="margin-left: 10px; flex-shrink: 0;"
                    @click="toggleInstitutionMode"
                  >切换疑似机构</el-button>
                </div>
              </el-form-item>
              <el-form-item v-else label="疑似机构" prop="suspectedInstitution" required class="form-row">
                <div style="display: flex; align-items: center; width: 100%;">
                  <el-input
                    v-model="form.suspectedInstitution"
                    placeholder="请输入疑似机构名称"
                    style="flex-grow: 1;"
                  />
                  <el-button
                    type="text"
                    style="margin-left: 10px; flex-shrink: 0;"
                    @click="toggleInstitutionMode"
                  >切换机构</el-button>
                </div>
              </el-form-item>
              <el-form-item label="风险等级" prop="riskLevel" class="form-row">
                <el-radio-group v-model="form.riskLevel">
                  <el-radio-button label="高"><i class="el-icon-warning" style="color: #f56c6c" />
                    高</el-radio-button>
                  <el-radio-button label="中"><i class="el-icon-warning" style="color: #e6a23c" />
                    中</el-radio-button>
                  <el-radio-button label="低"><i class="el-icon-warning" style="color: #67c23a" />
                    低</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="研判分析" prop="analysis" class="form-row">
                <el-input
                  v-model="form.analysis"
                  type="textarea"
                  :rows="4"
                  autosize
                  placeholder="请输入研判分析，或点击下方按钮由 AI 自动填充"
                />
                <!-- <div style="display: flex; align-items: center; margin-top: 8px;">
                  <el-button
                    type="primary"
                    icon="el-icon-magic-stick"
                    @click="handleAiAnalyze"
                    size="small"
                    plain
                    >
                    AI 智能填充
                  </el-button>
                  <span style="font-size: 12px; color: #909399; margin-left: 10px;">
                    将使用【摘要】和【链接】进行分析，并覆盖当前【标题】和【研判分析】内容
                  </span>
                </div> -->
              </el-form-item>
              <el-form-item label="选择联系人" prop="contacts" required>
                <div class="contact-selector">
                  <div class="selected-contacts">
                    <div style="font-weight: 500; margin-bottom: 8px; color: var(--secondary-color);">
                      已选联系人 <span style="color: var(--info-color); font-size: 13px">({{ selectedContacts.length
                      }})</span>
                    </div>

                    <!-- 此处使用新的分组标签逻辑 -->
                    <div
                      v-if="selectionGroupTags.length > 0"
                      class="institution-group-tags"
                      style="margin-bottom: 10px; display:flex; flex-wrap:wrap; gap:8px;"
                    >
                      <el-tag
                        v-for="tagInfo in selectionGroupTags"
                        :key="tagInfo.id"
                        closable
                        type="info"
                        style="cursor: pointer;"
                        effect="light"
                        size="small"
                        @close="handleRemoveGroupContacts(tagInfo.id)"
                      >
                        <!-- 【修改】显示格式：机构名 - 分组名 -->
                        {{ tagInfo.institutionName }} - {{ tagInfo.name }}
                      </el-tag>
                    </div>

                    <!-- 已选联系人列表显示逻辑保持不变 -->
                    <template v-if="selectedContacts.length > 0">
                      <el-tag
                        v-for="contact in selectedContacts"
                        :key="contact.contactId"
                        closable
                        class="contact-tag"
                        @close="handleRemoveSelectedContact(contact)"
                      >
                        {{ contact.name }} ({{ contact.institutionName }})
                      </el-tag>
                    </template>
                    <div v-else style="color: #c0c4cc; text-align: center; padding: 10px 0">
                      <span v-if="selectedInstitutions.length > 0">当前机构(含上级)无可选联系人或已被移除。</span>
                      <span v-else>暂无联系人</span>
                    </div>
                  </div>
                  <div
                    v-if="(pageMode === 'normal' || pageMode === 'summary') && !selectedInstitutions.length && institutionMode === 'institution'"
                    style="
                      color: #e6a23c;
                      font-size: 13px;
                      margin-top: 10px;
                      padding: 8px;
                      background: #fdf6ec;
                      border-radius: 4px;
                    "
                  >
                    <i class="el-icon-info" />
                    请先选择机构，系统将自动预填相关联系人
                  </div>
                  <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    style="width: 100%; margin-top: 15px"
                    @click="showContactDialog"
                  >
                    {{ (pageMode === 'normal' || pageMode === 'summary') && selectedInstitutions.length ? "补充选择联系人" :
                      "选择联系人" }}
                  </el-button>
                </div>
              </el-form-item>
              <el-form-item label="预警内容预览" class="form-row">
                <div class="preview-box">{{ enterpriseWxPreviewContent }}</div>
                <div class="char-count" :class="{ 'char-count-warning': previewCharCount > maxContentBytes }">
                  企业微信内容字节数: {{ previewCharCount }} / {{ maxContentBytes }}
                  <span v-if="previewCharCount > maxContentBytes">(已超长，将截断)</span>
                </div>
              </el-form-item>
            </template>
            <template v-if="pageMode === 'summary'">
              <el-form-item label="汇总内容" prop="summaryContent" class="form-row">
                <el-input v-model="form.summaryContent" type="textarea" :rows="20" placeholder="请输入汇总内容" />
                <div class="char-count" :class="{ 'char-count-warning': currentContentByteCount > maxContentBytes }">
                  内容字节数: {{ currentContentByteCount }} / {{ maxContentBytes }}
                  <span v-if="currentContentByteCount > maxContentBytes">(已超长，将无法发送)</span>
                </div>
              </el-form-item>
              <!-- 汇总图片上传 -->
              <el-form-item label="上传图片" prop="summaryImages" class="form-row">
                <ImageUpload
                  ref="summaryImageUpload"
                  :action="'/business/smsMsg/upload/image'"
                  :limit="9"
                  :file-size="10"
                  @on-change="handleSummaryImageChange"
                />
              </el-form-item>
              <el-form-item
                v-if="institutionMode === 'institution'"
                label="机构"
                prop="institutions"
                required
                class="form-row"
              >
                <div style="display: flex; align-items: center; width: 100%;">
                  <div class="institution-selector" style="flex-grow: 1;" @click="showInstitutionDialog">
                    <el-tag
                      v-for="inst in selectedInstitutions"
                      :key="inst.id"
                      closable
                      @close="handleRemoveInstitution(inst)"
                    >
                      {{ inst.label }}
                    </el-tag>
                    <span v-if="!selectedInstitutions.length" class="placeholder-text">点击选择机构</span>
                  </div>
                  <!-- <el-button type="text" @click="toggleInstitutionMode"
                    style="margin-left: 10px; flex-shrink: 0;">切换疑似机构</el-button> -->
                </div>
              </el-form-item>
              <el-form-item v-else label="疑似机构" prop="suspectedInstitution" required class="form-row">
                <div style="display: flex; align-items: center; width: 100%;">
                  <el-input
                    v-model="form.suspectedInstitution"
                    placeholder="请输入疑似机构名称"
                    style="flex-grow: 1;"
                  />
                  <el-button
                    type="text"
                    style="margin-left: 10px; flex-shrink: 0;"
                    @click="toggleInstitutionMode"
                  >切换机构</el-button>
                </div>
              </el-form-item>
              <el-form-item label="选择联系人" prop="contacts" required>
                <div class="contact-selector">
                  <div class="selected-contacts">
                    <div style="font-weight: 500; margin-bottom: 8px; color: var(--secondary-color);">
                      已选联系人 <span style="color: var(--info-color); font-size: 13px">({{ selectedContacts.length
                      }})</span>
                    </div>

                    <!-- 【修改】此处使用新的分组标签逻辑 -->
                    <div
                      v-if="selectionGroupTags.length > 0"
                      class="institution-group-tags"
                      style="margin-bottom: 10px; display:flex; flex-wrap:wrap; gap:8px;"
                    >
                      <el-tag
                        v-for="tagInfo in selectionGroupTags"
                        :key="tagInfo.id"
                        closable
                        type="info"
                        style="cursor: pointer;"
                        effect="light"
                        size="small"
                        @close="handleRemoveGroupContacts(tagInfo.id)"
                      >
                        <!-- 【修改】显示格式：机构名 - 分组名 -->
                        {{ tagInfo.institutionName }} - {{ tagInfo.name }}
                      </el-tag>
                    </div>

                    <!-- 已选联系人列表显示逻辑保持不变 -->
                    <template v-if="selectedContacts.length > 0">
                      <el-tag
                        v-for="contact in selectedContacts"
                        :key="contact.contactId"
                        closable
                        class="contact-tag"
                        @close="handleRemoveSelectedContact(contact)"
                      >
                        {{ contact.name }} ({{ contact.institutionName }})
                      </el-tag>
                    </template>
                    <div v-else style="color: #c0c4cc; text-align: center; padding: 10px 0">
                      <span v-if="selectedInstitutions.length > 0">当前机构(含上级)无可选联系人或已被移除。</span>
                      <span v-else>暂无联系人</span>
                    </div>
                  </div>
                  <div
                    v-if="(pageMode === 'normal' || pageMode === 'summary') && !selectedInstitutions.length && institutionMode === 'institution'"
                    style="
                      color: #e6a23c;
                      font-size: 13px;
                      margin-top: 10px;
                      padding: 8px;
                      background: #fdf6ec;
                      border-radius: 4px;
                    "
                  >
                    <i class="el-icon-info" />
                    请先选择机构，系统将自动预填相关联系人
                  </div>
                  <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    style="width: 100%; margin-top: 15px"
                    @click="showContactDialog"
                  >
                    {{ (pageMode === 'normal' || pageMode === 'summary') && selectedInstitutions.length ? "补充选择联系人" :
                      "选择联系人" }}
                  </el-button>
                </div>
              </el-form-item>
            </template>
            <template v-if="pageMode === 'report'">
              <el-form-item label="报告内容" prop="reportContent" class="form-row">
                <el-input v-model="form.reportContent" type="textarea" :rows="20" placeholder="请输入早晚报内容" />
                <div class="char-count" :class="{ 'char-count-warning': currentContentByteCount > maxContentBytes }">
                  内容字节数: {{ currentContentByteCount }} / {{ maxContentBytes }}
                  <span v-if="currentContentByteCount > maxContentBytes">(已超长，将无法发送)</span>
                </div>
              </el-form-item>
              <el-form-item label="选择联系人" prop="contacts" required>
                <div class="contact-selector">
                  <div class="selected-contacts">
                    <div style="font-weight: 500; margin-bottom: 8px; color: var(--secondary-color);">
                      已选联系人 <span style="color: var(--info-color); font-size: 13px">({{ selectedContacts.length
                      }})</span>
                    </div>

                    <!-- 【修改】此处使用新的分组标签逻辑 -->
                    <div
                      v-if="selectionGroupTags.length > 0"
                      class="institution-group-tags"
                      style="margin-bottom: 10px; display:flex; flex-wrap:wrap; gap:8px;"
                    >
                      <el-tag
                        v-for="tagInfo in selectionGroupTags"
                        :key="tagInfo.id"
                        closable
                        type="info"
                        style="cursor: pointer;"
                        effect="light"
                        size="small"
                        @close="handleRemoveGroupContacts(tagInfo.id)"
                      >
                        <!-- 【修改】显示格式：机构名 - 分组名 -->
                        {{ tagInfo.institutionName }} - {{ tagInfo.name }}
                      </el-tag>
                    </div>

                    <!-- 已选联系人列表显示逻辑保持不变 -->
                    <template v-if="selectedContacts.length > 0">
                      <el-tag
                        v-for="contact in selectedContacts"
                        :key="contact.contactId"
                        closable
                        class="contact-tag"
                        @close="handleRemoveSelectedContact(contact)"
                      >
                        {{ contact.name }} ({{ contact.institutionName }})
                      </el-tag>
                    </template>
                    <div v-else style="color: #c0c4cc; text-align: center; padding: 10px 0">
                      <span v-if="selectedInstitutions.length > 0">当前机构(含上级)无可选联系人或已被移除。</span>
                      <span v-else>暂无联系人</span>
                    </div>
                  </div>
                  <div
                    v-if="pageMode === 'normal' && !selectedInstitutions.length"
                    style="
                      color: #e6a23c;
                      font-size: 13px;
                      margin-top: 10px;
                      padding: 8px;
                      background: #fdf6ec;
                      border-radius: 4px;
                    "
                  >
                    <i class="el-icon-info" />
                    请先选择机构，系统将自动预填相关联系人
                  </div>
                  <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    style="width: 100%; margin-top: 15px"
                    @click="showContactDialog"
                  >
                    {{ pageMode === 'normal' && selectedInstitutions.length ? "补充选择联系人" : "选择联系人" }}
                  </el-button>
                </div>
              </el-form-item>
            </template>
          </el-form>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-s-promotion" @click="handleSubmit">{{ sendButtonText }}</el-button>
        <el-button icon="el-icon-document-copy" @click="copyOutput">复制输出内容</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm">重置</el-button>
      </div>
    </div>
    <el-dialog title="选择机构" :visible.sync="institutionDialogVisible" width="50%" :show-close="false">
      <div slot="title" class="dialog-header-actions">
        <span class="dialog-title">选择机构</span>
        <div class="dialog-buttons">
          <el-button size="small" @click="institutionDialogVisible = false">取 消</el-button>
          <el-button type="primary" size="small" @click="confirmInstitutionSelection">确 定</el-button>
        </div>
      </div>
      <el-input v-model="institutionSearchText" placeholder="输入机构名称进行搜索" clearable style="margin-bottom: 15px" />
      <el-tree
        ref="institutionTree"
        :data="filteredInstitutionTreeData"
        show-checkbox
        node-key="id"
        :check-strictly="true"
        :props="{ label: 'label', children: 'children' }"
        :default-checked-keys="selectedInstitutions.map((inst) => inst.id)"
        :filter-node-method="filterTreeNode"
        @check="handleInstitutionCheck"
      />
    </el-dialog>
    <!-- "选择联系人"弹窗中的 el-tree -->
    <el-dialog title="选择联系人" :visible.sync="contactDialogVisible" width="60%" :show-close="false">
      <div slot="title" class="dialog-header-actions">
        <span class="dialog-title">选择联系人</span>
        <div class="dialog-buttons">
          <el-button size="small" @click="contactDialogVisible = false">取 消</el-button>
          <el-button type="primary" size="small" @click="confirmContactSelection">确 定</el-button>
        </div>
      </div>
      <el-input v-model="contactSearchText" placeholder="输入联系人姓名或机构/分组搜索" clearable style="margin-bottom: 15px" />
      <!-- 【修改】使用新的 contactSelectionTree 数据和自定义节点渲染 -->
      <el-tree
        ref="contactTree"
        :data="contactSelectionTree"
        show-checkbox
        node-key="id"
        :props="{ label: 'label', children: 'children', disabled: 'disabled' }"
        :default-checked-keys="selectedContacts.map((c) => c.contactId)"
        :filter-node-method="filterContactTreeNode"
        check-on-click-node
        default-expand-all
        @check="handleContactDialogCheck"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i v-if="data.isInstitution" class="el-icon-office-building" style="color: #409EFF;" />
          <i v-if="data.isGroup" class="el-icon-collection-tag" style="color: #67C23A;" />
          <i v-if="data.isContact" class="el-icon-user" style="color: #E6A23C;" />
          <span style="margin-left: 5px;">{{ node.label }}</span>
        </span>
      </el-tree>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { getPublicSentimentById } from '@/api/yqmonitor'
import { mapItem } from '@/views/yqmonitor/components/MainContent/articleUtil'
import {
  getInstitutionUserTree,
  sendWarnMsg,
  getAIAnalysisFromBackend
} from '@/api/yqmonitor/institution' // 引入新的API
import {
  mapApiTreeToComponentTree,
  buildContactSelectionTree
} from './helper' // 引入新的 helper 函数
import Detail from '@/views/yqmonitor/detail.vue'
import ImageUpload from '@/components/ImageUpload/index2.vue'
// 定义内容最大字节数常量
const MAX_CONTENT_BYTES = 1800
export default {
  name: 'WarningSender',
  components: {
    Detail,
    ImageUpload
  },
  dicts: ['warn_level', 'content_type'],
  data() {
    return {
      pageMode: 'normal',
      institutionMode: 'institution', // 'institution' or 'suspected'
      warningId: null,
      isLoading: false,
      isLeftPanelCollapsed: localStorage.getItem('yqwarning_left_panel_collapsed') === 'true',
      detailItem: {},
      form: {
        title: '',
        summary: '',
        source: '',
        time: null,
        link: '',
        institutions: [],
        suspectedInstitution: '',
        riskLevel: '低', // MODIFIED: Default risk level changed to "低"
        analysis: '',
        summaryContent: '',
        reportContent: '',
        contacts: []
      },
      summaryImages: [],
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        source: [{ required: true, message: '请输入来源', trigger: 'blur' }],
        time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
        summary: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
        summaryContent: [{ required: true, message: '请输入汇总内容', trigger: 'blur' }],
        reportContent: [{ required: true, message: '请输入报告内容', trigger: 'blur' }],
        institutions: [
          { required: true, type: 'array', min: 1, message: '请选择机构', trigger: 'change' }
        ],
        suspectedInstitution: [
          { required: true, message: '请输入疑似机构', trigger: 'blur' }
        ],
        contacts: [
          { required: true, type: 'array', min: 1, message: '请选择联系人', trigger: 'change' }
        ]
      },
      institutionDialogVisible: false,
      contactDialogVisible: false,
      institutionSearchText: '',
      contactSearchText: '',
      institutionTreeData: [],
      contactSelectionTree: [], // 【新增】用于联系人弹窗的树
      selectedInstitutions: [],
      _tempSelectedInstitutionsForDialog: [],
      selectedContacts: [],
      _tempSelectedContactsForDialog: [],
      manuallyRemovedContactIdsThisSession: new Set(),
      rawInstitutionTree: [], // 【新增】存储从API获取的原始树结构数据
      institutionParentMap: new Map(), // 【新增】用于存储 code -> parentCode 的映射关系
      pastedContent: '', // 用于绑定左侧粘贴板的文本
      isLinkInvalid: false, // 用于标记链接是否失效
      allContactsFlatList: [] // 用于存储所有联系人的扁平化
    }
  },
  computed: {
    ...mapGetters(['nickName']),
    maxContentBytes() {
      return MAX_CONTENT_BYTES
    },
    sendButtonText() {
      if (this.pageMode === 'normal') return '聚合预警发送'
      if (this.pageMode === 'summary') return '发送汇总'
      if (this.pageMode === 'report') return '发送早晚报'
      return '发送'
    },
    currentContent() {
      if (this.pageMode === 'normal') {
        return this.enterpriseWxPreviewContent
      } else if (this.pageMode === 'summary') {
        return this.form.summaryContent
      } else if (this.pageMode === 'report') {
        return this.form.reportContent
      }
      return ''
    },
    currentContentByteCount() {
      return new TextEncoder().encode(this.currentContent).length
    },
    /**
 * 一个辅助计算属性，用于生成最终显示的链接
 */
    displayLink() {
      if (!this.form.link) return ''
      // 如果链接被标记为失效，则附加提示信息
      return this.isLinkInvalid ? `${this.form.link}【链接已无法查看】` : this.form.link
    },
    enterpriseWxPreviewContent() {
      if (this.pageMode !== 'normal') return ''
      let institutionLine
      if (this.institutionMode === 'suspected') {
        institutionLine = `机构：${this.form.suspectedInstitution || '未指定'}\n`
      } else {
        // const institutionNames = this.selectedInstitutions.map(inst => inst.label).join('、'); // Changed separator
        const institutionNames = this.selectedInstitutions.map(inst => inst.originalData.shortName || inst.label).join('、')
        institutionLine = `机构：${institutionNames || '未指定'}\n` // Added fallback
      }
      let content = institutionLine
      content += `标题：${this.form.title || ''}\n`
      content += `风险等级：${this.form.riskLevel || ''}\n`
      content += `摘要：${this.form.summary || ''}\n`
      content += `来源：${this.form.source || ''}\n`
      content += `时间：${this.form.time ? this.form.time.substring(0, 16) : ''}\n` // 修改这里，只取到分钟
      // 使用新的 displayLink 计算属性来获取链接内容
      content += `链接：${this.displayLink || ''}\n`
      // 只有当研判分析有内容时才添加
      if (this.form.analysis && this.form.analysis.trim()) {
        content += `研判分析：${this.form.analysis}\n`
      }
      if (this.warningId) {
        let baseUrl
        if (process.env.VUE_APP_ENV === 'staging') {
          baseUrl = 'https://test.tyfzyuqing.com'
        } else if (process.env.VUE_APP_ENV === 'development') {
          baseUrl = 'http://localhost'
        } else {
          baseUrl = 'https://jc.tyfzyuqing.com'
        }
        content += `点击查看更多：${baseUrl}/yqwarning/h5/${this.warningId}`
      }
      return content.trimStart()
    },
    previewCharCount() {
      if (this.pageMode !== 'normal') return 0
      return new TextEncoder().encode(this.enterpriseWxPreviewContent).length
    },
    filteredInstitutionTreeData() {
      if (!this.institutionSearchText && this.$refs.institutionTree) {
        // this.$refs.institutionTree.filter(""); // Avoid unnecessary filter calls on empty
      } else if (this.$refs.institutionTree) {
        this.$refs.institutionTree.filter(this.institutionSearchText)
      }
      return this.institutionTreeData
    },
    allContactsForDialogTree() {
      const institutionMap = {}
      this.allContacts.forEach((contact) => {
        if (!institutionMap[contact.institutionId]) {
          institutionMap[contact.institutionId] = {
            id: contact.institutionId, // Use institutionId for grouping
            displayName: contact.institutionName,
            contactId: `group_${contact.institutionId}`, // Unique key for group node
            children: [],
            disabled: true // Group node itself is not checkable
          }
        }
        institutionMap[contact.institutionId].children.push({
          ...contact // Spread contact properties
          // contactId is already unique per contact
        })
      })
      return Object.values(institutionMap)
    },
    filteredContactTreeForDialog() {
      if (!this.contactSearchText && this.$refs.contactTree) {
        // this.$refs.contactTree.filter("");
      } else if (this.$refs.contactTree) {
        this.$refs.contactTree.filter(this.contactSearchText)
      }
      return this.allContactsForDialogTree
    },
    warningText() {
      if (this.pageMode === 'normal' && this.warningId) {
        return '预警'
      } else if (this.pageMode === 'normal' && !this.warningId) {
        return '普通预警'
      } else if (this.pageMode === 'summary') {
        return '汇总'
      } else if (this.pageMode === 'report') {
        return '早晚报'
      }
      return '发送' // Fallback
    },
    /**
   * 【新增】根据已选联系人，生成顶部的分组标签
   */
    selectionGroupTags() {
      if (this.selectedContacts.length === 0) return []

      const groups = new Map()
      this.selectedContacts.forEach(contact => {
        // 确保联系人对象上有 groupId 属性
        if (contact.groupId) {
          if (!groups.has(contact.groupId)) {
            groups.set(contact.groupId, {
              id: contact.groupId, // 分组的唯一ID
              name: contact.groupName, // 分组的名称
              institutionName: contact.institutionName // 分组所属的机构名
            })
          }
        }
      })

      return Array.from(groups.values())
    }
  },
  watch: {
    selectedInstitutions(newVal, oldVal) {
      this.form.institutions = newVal.map((inst) => inst.id)
      const newIds = newVal.map(i => i.id).sort().join(',')
      const oldIds = oldVal.map(i => i.id).sort().join(',')
      if (newIds !== oldIds && (this.pageMode === 'normal' || this.pageMode === 'summary')) {
        this.updateContactsBasedOnInstitutions()
      }
    },
    institutionSearchText(val) {
      if (this.$refs.institutionTree) {
        this.$refs.institutionTree.filter(val)
      }
    },
    contactSearchText(val) {
      if (this.$refs.contactTree) {
        this.$refs.contactTree.filter(val)
      }
    }
  },
  created() {
    this.determinePageModeAndLoadData()
  },
  mounted() {
    console.log('内容类型字典:', this.dict.type.content_type)
  },
  methods: {
    /**
     * 【新增】处理AI智能填充逻辑
     */
    async handleAiAnalyze() {
      if (!this.form.summary || !this.form.link) {
        this.$message.warning('请确保【摘要】和【链接】字段都已填写内容。')
        return
      }
      this.isLoading = true
      try {
        const payload = {
          summary: this.form.summary,
          link: this.form.link
        }
        // 调用真实的后端API接口
        const response = await getAIAnalysisFromBackend(payload)
        if (response && response.code === 200 && response.data) {
          // 根据需求文档，覆盖标题和研判分析
          if (response.data.title) {
            this.form.title = response.data.title
          }
          if (response.data.analysis) {
            this.form.analysis = response.data.analysis
          }
          this.$message.success('AI 智能填充完成！')
        } else {
          this.$message.error(response.msg || 'AI 分析返回数据格式不正确')
        }
      } catch (error) {
        console.error('AI analysis failed:', error)
        this.$message.error('AI 分析请求失败，请检查网络或联系管理员。')
      } finally {
        this.isLoading = false
      }
    },
    handleSummaryImageChange(fileList) {
      this.summaryImages = fileList
    },
    async loadInitialData() {
      this.isLoading = true
      try {
        const response = await getInstitutionUserTree()
        if (response.code === 200 && response.data) {
          // 存储原始数据并构建父级映射
          this.rawInstitutionTree = response.data
          this.buildParentMapRecursive(this.rawInstitutionTree, this.institutionParentMap)

          // 用于主表单的机构选择器 (这部分不变)
          this.institutionTreeData = mapApiTreeToComponentTree(response.data)
          // 用于联系人弹窗的选择树 (这部分不变)
          this.contactSelectionTree = buildContactSelectionTree(response.data)
        } else {
          this.$message.error(response.msg || '获取机构用户树失败')
        }
      } catch (error) {
        console.error('获取机构用户树失败:', error)
        this.$message.error('加载机构数据失败，请检查网络或联系管理员')
      } finally {
        this.isLoading = false
      }
    },
    filterTreeNode(value, data) {
      if (!value) return true
      return data.label.toLowerCase().includes(value.toLowerCase())
    },
    filterContactTreeNode(value, data, node) {
      // 如果搜索框为空，则显示所有节点
      if (!value) return true

      // 检查当前节点或其任何父节点的标签是否包含搜索值
      // `node` 对象包含了父节点的引用，我们可以利用它向上追溯
      let currentNode = node
      while (currentNode) {
        // 使用不区分大小写的匹配
        if (currentNode.data.label && currentNode.data.label.toLowerCase().includes(value.toLowerCase())) {
          // 只要路径上有一个节点匹配，就显示当前节点
          return true
        }
        // 移动到父节点继续检查
        currentNode = currentNode.parent
      }

      // 如果整条路径上都没有匹配的，则隐藏该节点
      return false
    },
    async determinePageModeAndLoadData() {
      this.isLoading = true
      const query = this.$route.query
      if (query.mode) {
        const validModes = ['normal', 'summary', 'report']
        if (validModes.includes(query.mode)) {
          this.pageMode = query.mode
        } else {
          this.$message.warning(
            `无效的模式参数: ${query.mode}，将使用默认模式`
          )
          this.pageMode = 'normal' // fallback to normal
        }
      } else if (query.warningId) {
        this.pageMode = 'normal' // If warningId is present, it's normal mode
      } else {
        this.pageMode = 'normal' // Default mode
      }
      await this.loadInitialData() // Load tree data first
      if (query.warningId && this.pageMode === 'normal') {
        await this.loadWarningData(query.warningId)
      } else {
        this.resetFormFields() // Reset form for other modes or new normal warning
      }
      // 1. 构建扁平化列表
      this.buildAllContactsFlatList()
      // 2. 设置默认联系人
      this.setDefaultContacts()
      this.isLoading = false
    },
    resetFormFields() {
      this.form = {
        title: '',
        summary: '',
        source: '',
        time: null,
        link: '',
        institutions: [],
        suspectedInstitution: '',
        riskLevel: '低', // Default to "低"
        analysis: '',
        summaryContent: '',
        reportContent: '',
        contacts: []
      }
      this.summaryImages = []
      this.isLinkInvalid = false // Reset link status
      this.institutionMode = 'institution'
      this.selectedInstitutions = []
      this.selectedContacts = []
      this.manuallyRemovedContactIdsThisSession.clear()
      if (this.$refs.warningForm) {
        this.$refs.warningForm.resetFields()
      }
    },
    async loadWarningData(warningId) {
      if (!warningId) return
      this.isLoading = true
      this.warningId = warningId
      try {
        const response = await getPublicSentimentById(warningId)
        if (response.code === 200 && response.data) {
          const item = mapItem(response.data)
          this.form.title = item.titleObj.rawTitle || item.titleObj.title
          const rawSummary = (item.contentObj?.rawContent ||
            item.contentObj?.rawAsr ||
            item.contentObj?.rawOcr || '')
            .trim()
            .replace(/\s+/g, ' ')
          this.form.summary = rawSummary.length > 500 ? rawSummary.substring(0, 500) + '......' : rawSummary
          this.form.source = `${item.source}—${item.author}`
          this.form.time = item.publishTime ? item.publishTime.substring(0, 19).replace('T', ' ') : null // yyyy-MM-dd HH:mm:ss
          this.form.link = item.url
          this.form.institutions = []
          this.selectedInstitutions = []
          this.selectedContacts = [] // Clear contacts when loading new warning
          this.manuallyRemovedContactIdsThisSession.clear()
          this.detailItem = item
          this.$message.success('预警信息加载成功')
        } else {
          this.$message.error(response.msg || `未能加载ID为 ${warningId} 的预警信息`)
        }
      } catch (error) {
        console.error(`加载预警详情失败 (ID: ${warningId}):`, error)
        this.$message.error('加载预警详情失败，请检查网络或联系管理员。')
      } finally {
        this.isLoading = false
      }
    },
    toggleInstitutionMode() {
      this.institutionMode = this.institutionMode === 'institution' ? 'suspected' : 'institution'
      this.$nextTick(() => {
        if (this.institutionMode === 'institution') {
          this.form.suspectedInstitution = ''
        } else {
          this.form.institutions = []
          this.selectedInstitutions = []
        }
        if (this.$refs.warningForm) {
          this.$refs.warningForm.clearValidate(['institutions', 'suspectedInstitution'])
        }
      })
    },
    showInstitutionDialog() {
      this._tempSelectedInstitutionsForDialog = [...this.selectedInstitutions]
      this.institutionDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.institutionTree) {
          this.$refs.institutionTree.setCheckedKeys(
            this.selectedInstitutions.map((i) => i.id)
          )
        }
      })
    },
    handleInstitutionCheck(clickedNode, checkedStatus) {
      this._tempSelectedInstitutionsForDialog = checkedStatus.checkedNodes.map(
        (node) => ({ id: node.id, label: node.label, originalData: node.originalData })
      )
    },
    confirmInstitutionSelection() {
      this.selectedInstitutions = [...this._tempSelectedInstitutionsForDialog]
      this.institutionDialogVisible = false
      // No need to call updateContactsBasedOnInstitutions here, watcher will do it.
    },
    handleRemoveInstitution(instToRemove) {
      this.selectedInstitutions = this.selectedInstitutions.filter(
        (inst) => inst.id !== instToRemove.id
      )
      // Watcher on selectedInstitutions will trigger updateContactsBasedOnInstitutions
    },
    // src/views/yqwarning/sender/index.vue -> methods

    /**
     * 【核心修改】当主表单选择机构后，自动填充该机构及所有上级机构的联系人
     */
    updateContactsBasedOnInstitutions() {
      // 1. 获取所有需要关联的机构 Code
      const allRelevantInstitutionCodes = new Set()
      this.selectedInstitutions.forEach(inst => {
        let currentCode = inst.id
        // 使用 while 循环和 parentMap 向上追溯，直到找不到父级
        while (currentCode) {
          allRelevantInstitutionCodes.add(currentCode)
          currentCode = this.institutionParentMap.get(currentCode)
        }
      })

      // 2. 根据收集到的所有 Code，从联系人树中提取联系人
      const newSelectedContacts = []
      const addedContactIds = new Set()

      // 遍历联系人选择树 (contactSelectionTree 已经是拍平的机构列表，效率很高)
      this.contactSelectionTree.forEach(institutionNode => {
        // 如果当前机构的 code 在我们收集的"相关机构"列表中
        if (allRelevantInstitutionCodes.has(institutionNode.code)) {
          // 遍历机构下的所有分组（包括[未分组]）
          institutionNode.children.forEach(groupNode => {
            // 遍历分组下的所有联系人
            groupNode.children.forEach(contactNode => {
              // 防止重复添加同一个联系人
              if (!addedContactIds.has(contactNode.id)) {
                addedContactIds.add(contactNode.id)

                // 构造完整的联系人对象，包含其直接所属的机构和分组信息
                newSelectedContacts.push({
                  id: contactNode.userId,
                  name: contactNode.userName,
                  phone: contactNode.phoneNumber,
                  sendType: contactNode.warnType || 'all',
                  // 注意：这里的 institutionId 和 institutionName 是联系人【直接所属】的机构信息
                  institutionId: institutionNode.code,
                  institutionName: institutionNode.label,
                  groupId: groupNode.groupId,
                  groupName: groupNode.label,
                  contactId: contactNode.id,
                  displayName: contactNode.label
                })
              }
            })
          })
        }
      })

      // 3. 更新组件状态
      this.selectedContacts = newSelectedContacts
      this.form.contacts = this.selectedContacts.map(c => c.contactId)
      // 清空手动移除记录，因为是全新填充
      this.manuallyRemovedContactIdsThisSession.clear()
    },
    /**
 * 【新增】通过点击分组标签，移除整个分组的联系人
 */
    handleRemoveGroupContacts(groupIdToRemove) {
      // 记录被移除的联系人ID，以便维护"手动移除"状态
      this.selectedContacts.forEach(contact => {
        if (contact.groupId === groupIdToRemove) {
          this.manuallyRemovedContactIdsThisSession.add(contact.contactId)
        }
      })

      // 从当前选择中过滤掉该分组的所有联系人
      this.selectedContacts = this.selectedContacts.filter(
        contact => contact.groupId !== groupIdToRemove
      )
      this.form.contacts = this.selectedContacts.map(c => c.contactId)
    },
    showContactDialog() {
      this._tempSelectedContactsForDialog = [...this.selectedContacts]
      this.contactDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.contactTree) {
          this.$refs.contactTree.setCheckedKeys(
            this.selectedContacts.map((c) => c.contactId)
          )
        }
      })
    },
    handleContactDialogCheck(clickedNode, checkedStatus) {
      // Filter out group nodes, only take actual contacts (leaf nodes with contactId not starting with "group_")
      this._tempSelectedContactsForDialog = checkedStatus.checkedNodes.filter(
        (node) => node.contactId && !node.contactId.startsWith('group_') && node.id // Ensure 'id' (userId) exists
      )
    },
    // 弹窗确认联系人选择
    confirmContactSelection() {
      // 1. 获取所有新勾选的联系人节点
      const checkedNodes = this.$refs.contactTree.getCheckedNodes(true) // true 表示只获取叶子节点

      // 2. 将节点转换为我们需要的 contact 对象格式
      const newContactsMap = new Map()
      checkedNodes.forEach(contactNode => {
        // 通过父节点获取分组和机构信息
        const groupNode = this.$refs.contactTree.getNode(contactNode.id).parent
        const institutionNode = groupNode.parent

        newContactsMap.set(contactNode.id, {
          id: contactNode.userId,
          name: contactNode.userName,
          phone: contactNode.phoneNumber,
          sendType: contactNode.warnType || 'all',
          institutionId: institutionNode.data.code,
          institutionName: institutionNode.data.label,
          groupId: groupNode.data.groupId,
          groupName: groupNode.data.label,
          contactId: contactNode.id,
          displayName: contactNode.label
        })
      })

      this.selectedContacts = Array.from(newContactsMap.values())
      this.form.contacts = this.selectedContacts.map(c => c.contactId)
      this.contactDialogVisible = false
    },
    handleRemoveSelectedContact(contactToRemove) {
      this.selectedContacts = this.selectedContacts.filter(
        (c) => c.contactId !== contactToRemove.contactId
      )
      this.form.contacts = this.selectedContacts.map((c) => c.contactId)
      this.manuallyRemovedContactIdsThisSession.add(contactToRemove.contactId)
    },
    copyOutput() {
      let textToCopy = ''
      if (this.pageMode === 'normal') {
        textToCopy = this.enterpriseWxPreviewContent
      } else if (this.pageMode === 'summary') {
        textToCopy = this.form.summaryContent
      } else if (this.pageMode === 'report') {
        textToCopy = this.form.reportContent
      }
      if (!textToCopy) {
        this.$message.warning('没有内容可以复制')
        return
      }
      // Create a temporary textarea element to hold the text
      const textarea = document.createElement('textarea')
      textarea.value = textToCopy
      document.body.appendChild(textarea)
      textarea.select()
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('内容已复制到剪贴板')
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
        console.error('Fallback: Oops, unable to copy', err)
      }
      document.body.removeChild(textarea)
    },
    handleSubmit() {
      this.$refs.warningForm.validate(async(valid) => {
        if (valid) {
          if (this.selectedContacts.length === 0) {
            this.$message.warning('请至少选择一个联系人')
            return
          }
          if (this.currentContentByteCount > this.maxContentBytes) {
            this.$message.error(`字数超出限制 (${this.currentContentByteCount}/${this.maxContentBytes}字节)，请删减内容`)
            return
          }
          const confirmMessage = `确定要向 ${this.selectedContacts.length} 位联系人发送【${this.warningText}】吗？`
          this.$confirm(confirmMessage, '操作确认', {
            confirmButtonText: '确定发送',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async() => {
            let warnLevelValue, contentTypeValue
            const modeToLabelMap = {
              normal: '预警',
              summary: '汇总',
              report: '早晚报'
            }
            const contentTypeLabel = modeToLabelMap[this.pageMode]
            if (this.dict.type.content_type) {
              const contentTypeDictItem = this.dict.type.content_type.find(d => d.label === contentTypeLabel)
              if (contentTypeDictItem) contentTypeValue = contentTypeDictItem.value
            }
            if (!contentTypeValue) {
              this.$message.error(`无法确定内容类型, 请检查 [content_type] 字典配置中是否存在标签为'${contentTypeLabel}'的项`)
              return
            }
            if (this.pageMode === 'normal') {
              if (this.dict.type.warn_level) {
                const warnLevelDictItem = this.dict.type.warn_level.find(d => d.label === this.form.riskLevel)
                if (warnLevelDictItem) warnLevelValue = warnLevelDictItem.value
              }
              if (!warnLevelValue) {
                this.$message.error(`无法确定风险等级, 请检查 [warn_level] 字典配置中是否存在标签为'${this.form.riskLevel}'的项`)
                return
              }
            }
            const userList = this.selectedContacts.map(contact => ({
              userId: contact.id, // This is the actual userId
              mobile: contact.phone,
              warnType: contact.sendType // Ensure sendType is correct
            }))
            let payload = {}
            let baseUrl
            if (process.env.VUE_APP_ENV === 'staging') {
              baseUrl = 'https://test.tyfzyuqing.com'
            } else if (process.env.VUE_APP_ENV === 'development') {
              baseUrl = 'http://localhost'
            } else {
              baseUrl = 'https://jc.tyfzyuqing.com'
            }
            if (this.pageMode === 'normal') {
              // 对于普通预警，内容直接取自预览
              let content = this.enterpriseWxPreviewContent
              // 如果是新建的普通预警（无warningId），则追加H5链接占位符
              if (!this.warningId) {
                content += `点击查看更多：${baseUrl}/yqwarning/h5/common/`
              }
              payload = {
                content: content.trim(), // 使用处理过的内容
                warnLevel: warnLevelValue,
                title: this.form.title,
                contentType: contentTypeValue,
                userList: userList,
                publishTime: this.form.time.substring(0, 16), // Make sure format is yyyy-MM-dd HH:mm:ss
                comeFrom: this.form.source // 'source' maps to 'comeFrom'
              }
              if (this.institutionMode === 'suspected') {
                payload.suspectedInstitution = this.form.suspectedInstitution
              } else {
                payload.institutions = this.selectedInstitutions.map(inst => inst.id)
              }
              // 如果当前是预警模式，并且有warningId，带上uniqueId
              if (this.pageMode === 'normal' && this.warningId) {
                payload.uniqueId = this.warningId
              } else {
                // 如果是新建的普通预警（无warningId）
                payload.fromNormalWarn = 1
              }
            } else { // Summary or Report
              const content = this.pageMode === 'summary' ? this.form.summaryContent : this.form.reportContent
              if (this.pageMode === 'summary') {
                // content += `\n点击查看更多：${baseUrl}/yqwarning/h5/summary/`;
                payload.imgList = this.summaryImages.map((img, index) => ({ fileName: img.name, fileSize: String(img.size), filePath: img.originalPath, xh: String(index + 1) }))
              }
              payload = {
                ...payload, // 继承 imgList (如果存在)
                content: content.trim(),
                contentType: contentTypeValue,
                userList: userList,
                uniqueId: this.warningId ? this.warningId : undefined
              }
              if (this.pageMode === 'summary') {
                if (this.institutionMode === 'suspected') { payload.suspectedInstitution = this.form.suspectedInstitution } else { payload.institutions = this.selectedInstitutions.map(inst => inst.id) }
              } else { // report mode
                const distinctInstitutionIds = [...new Set(this.selectedContacts.map(c => c.institutionId))]
                payload.institutions = distinctInstitutionIds
              }
            }
            this.isLoading = true
            try {
              const response = await sendWarnMsg(payload)
              if (response.code === 0 || response.code === 200) { // Assuming 0 or 200 means success
                this.$message.success(`${this.sendButtonText} 成功`)
                // Optionally reset form or navigate away
              } else {
                this.$message.error(response.msg || `${this.sendButtonText} 失败`)
              }
            } catch (error) {
              console.error('发送失败:', error)
              this.$message.error('请求失败，请检查网络或联系管理员')
            } finally {
              this.isLoading = false
            }
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消发送'
            })
          })
        } else {
          this.$message.error('请检查表单填写是否完整')
          return false
        }
      })
    },
    resetForm() {
      const currentQuery = { ...this.$route.query }
      this.resetFormFields() // Resets data including selectedInstitutions and selectedContacts
      // Re-determine page mode based on original query, as resetFormFields clears form state
      // but not the underlying mode logic
      if (currentQuery.mode) {
        this.pageMode = currentQuery.mode
      } else if (currentQuery.warningId) {
        this.pageMode = 'normal'
        // If resetting a form that had a warningId, we might want to reload that warning's data
        // or clear warningId and treat as a new normal warning.
        // For now, let's assume reset means start fresh for the current mode.
        // If warningId was present, re-load it to re-populate specific fields.
        // This line will be hit after resetFormFields clear warningId. So it will setup as a new warning.
      } else {
        this.pageMode = 'normal'
      }
      if (currentQuery.warningId && this.pageMode === 'normal') {
        this.loadWarningData(currentQuery.warningId) // Reload data if it was a specific warning
      }
      this.$message.info('表单已重置')
    },
    toggleLeftPanel() {
      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed
      localStorage.setItem('yqwarning_left_panel_collapsed', this.isLeftPanelCollapsed)
    },
    /**
 * 【新增】递归构建机构的父子关系映射表
 * @param {Array} nodes - 原始机构树节点数组
 * @param {Map} map - 要填充的Map对象
 */
    buildParentMapRecursive(nodes, map) {
      if (!nodes || !Array.isArray(nodes)) return
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            // 记录下每个子节点的 code 指向其父节点的 code
            map.set(child.code, node.code)
          })
          // 继续向更深层级递归
          this.buildParentMapRecursive(node.children, map)
        }
      })
    },
    /**
* 【新增】处理粘贴内容并解析填充到表单
* @param {string} text - 从文本域粘贴的完整内容
*/
    handlePasteAndParse(text) {
      if (!text || !text.trim()) {
        return
      }

      // 定义所有可能的字段及其在表单中的映射
      const fieldMapping = {
        '机构': 'suspectedInstitution',
        '标题': 'title',
        '风险等级': 'riskLevel',
        '摘要': 'summary',
        '来源': 'source',
        '时间': 'time',
        '链接': 'link',
        '研判分析': 'analysis',
        '作者': 'author' // 为格式1.2准备的临时字段
      }
      // 定义一个允许拥有多行内容的字段白名单
      const multiLineFields = ['summary', 'analysis']

      const extractedData = {}
      const lines = text.trim().split('\n')
      let currentFieldKey = null

      lines.forEach(line => {
        let matched = false
        // 遍历所有可能的key，检查当前行是否以某个key开头
        for (const key in fieldMapping) {
          // 使用正则表达式匹配 "key" + "中/英文冒号" 的组合
          const regex = new RegExp(`^${key.trim()}[：:]`)
          if (regex.test(line.trim())) {
            currentFieldKey = fieldMapping[key]
            // 提取冒号后的内容作为值
            extractedData[currentFieldKey] = line.trim().replace(regex, '').trim()
            matched = true
            break // 找到匹配的key后，处理下一行
          }
        }

        // 只有当 currentFieldKey 在白名单中时，才允许追加多行内容
        if (!matched && currentFieldKey && multiLineFields.includes(currentFieldKey)) {
          extractedData[currentFieldKey] += '\n' + line.trim()
        }
      })

      // --- 数据填充和特殊处理 ---

      // 1. 特殊处理"来源"字段 (格式1.2)
      if (extractedData.author && extractedData.source) {
        // 只有当来源中不包含作者时，才进行拼接，避免重复
        if (!extractedData.source.includes(extractedData.author)) {
          this.form.source = `${extractedData.source}—${extractedData.author}`
        } else {
          this.form.source = extractedData.source
        }
      } else if (extractedData.source) {
        this.form.source = extractedData.source
      }

      // 2. 填充其他通用字段
      Object.keys(extractedData).forEach(key => {
        // 确保form对象中有这个属性，并且不是我们已特殊处理的字段
        if (this.form.hasOwnProperty(key) && key !== 'source' && key !== 'author') {
          // 对风险等级做精确匹配，防止无效值
          if (key === 'riskLevel' && ['高', '中', '低'].includes(extractedData[key])) {
            this.form[key] = extractedData[key]
          } else if (key !== 'riskLevel') {
            this.form[key] = extractedData[key]
          }
        }
      })

      // 3. 如果解析出"机构"，自动切换到"疑似机构"模式，方便用户编辑
      if (extractedData.suspectedInstitution) {
        this.institutionMode = 'suspected'
        // 清空机构选择，避免冲突
        this.form.institutions = []
        this.selectedInstitutions = []
      }

      this.$nextTick(() => {
        this.$message.success('内容已解析并填充至表单！')
      })
    },
    /**
 * 切换链接的失效状态
 */
    toggleLinkStatus() {
      this.isLinkInvalid = !this.isLinkInvalid
      this.$message.info(`链接已标记为【${this.isLinkInvalid ? '失效' : '有效'}】状态`)
    },
    /**
* 【新增】判断是否应该显示"点击查看更多"链接
* 只有当联系人是 eric、陈雷、eric和陈雷 这三种情况时才显示
*/
    shouldShowMoreLink() {
      if (!this.selectedContacts || this.selectedContacts.length === 0) {
        return false
      }

      // 获取所有联系人的姓名
      const contactNames = this.selectedContacts.map(contact => contact.name).sort()

      // 定义允许显示"点击查看更多"的联系人组合
      const allowedCombinations = [
        ['eric'], // 只有 eric
        ['陈雷'], // 只有 陈雷
        ['eric', '陈雷'] // eric 和 陈雷
      ]

      // 检查当前联系人组合是否在允许列表中
      return allowedCombinations.some(combination => {
        // 对组合进行排序后比较，确保顺序无关
        const sortedCombination = [...combination].sort()
        return JSON.stringify(contactNames) === JSON.stringify(sortedCombination)
      })
    },
    // 从 contactSelectionTree 构建扁平化的联系人列表
    buildAllContactsFlatList() {
      const flatList = []
      if (!this.contactSelectionTree) return

      this.contactSelectionTree.forEach(institutionNode => {
        (institutionNode.children || []).forEach(groupNode => {
          (groupNode.children || []).forEach(contactNode => {
            // 构造一个完整的联系人对象，方便后续使用
            flatList.push({
              id: contactNode.userId,
              name: contactNode.userName,
              phone: contactNode.phoneNumber,
              sendType: contactNode.warnType || 'all',
              institutionId: institutionNode.code,
              institutionName: institutionNode.label,
              groupId: groupNode.groupId,
              groupName: groupNode.label,
              contactId: contactNode.id, // 这个是树节点的唯一ID，如 "contact_xxx"
              displayName: contactNode.label
            })
          })
        })
      })
      this.allContactsFlatList = flatList
    },
    setDefaultContacts() {
      // 如果扁平列表为空，则不执行
      if (!this.allContactsFlatList || this.allContactsFlatList.length === 0) {
        return
      }

      // 使用 Map 来存储默认联系人，可以自动处理重复（例如登录用户就是周晓龙）
      const defaultContactsMap = new Map()

      // 1. 查找并添加当前登录用户
      const currentUserContact = this.allContactsFlatList.find(
        c => c.name === this.nickName
      )
      if (currentUserContact) {
        defaultContactsMap.set(currentUserContact.contactId, currentUserContact)
      }

      // 2. 查找并添加周晓龙
      const zhouXiaolongContact = this.allContactsFlatList.find(
        c => c.name === '周晓龙'
      )
      if (zhouXiaolongContact) {
        defaultContactsMap.set(zhouXiaolongContact.contactId, zhouXiaolongContact)
      }

      // 3. 将找到的默认联系人设置为已选状态
      // 我们将默认联系人与已有的选择合并，而不是直接覆盖
      const existingContactsMap = new Map(this.selectedContacts.map(c => [c.contactId, c]))
      const mergedMap = new Map([...defaultContactsMap, ...existingContactsMap])

      this.selectedContacts = Array.from(mergedMap.values())
      this.form.contacts = this.selectedContacts.map(c => c.contactId)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  // CSS变量定义在组件容器级别，避免污染全局
  --primary-color: #001ff8;
  --secondary-color: #2c3e50;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --bg-color: #f5f7fa;
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --border-color: #ebeef5;
  min-width: 1200px;
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--secondary-color);
}

.main-layout {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: stretch;
}

.left-panel {
  flex: 1;
  min-width: 0;
  position: relative;
  transition: all 0.3s ease;

  &.collapsed {
    flex: 0 0 40px;
    min-width: 40px;

    // overflow: hidden;
    ::v-deep .detail-layout {
      opacity: 0;
      visibility: hidden;
    }
  }

  .collapse-button {
    position: absolute;
    right: -12px;
    top: 20%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: #fff;
    }

    i {
      font-size: 12px;
    }
  }

  ::v-deep .detail-layout {
    background-color: #fff;
    padding: 0;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;

    .main-content {
      height: 1315px;
      overflow-y: auto;
    }

    .side-info {
      display: none;
    }
  }
}

.right-panel {
  flex: 1;
  min-width: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Style for the mode stamp */
.mode-stamp {
  position: absolute;
  top: 0px;
  left: -18px;
  transform: rotate(-26deg);
  /* Adds a slight angle */
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  z-index: 10;
  /* Ensures it's above the card */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0.85;
  /* Makes it slightly transparent */
  border: 2px solid #fff;

  &.two-text {
    top: -2px;
    left: -14px;
  }

  &.four-text {
    top: 4px;
    left: -24px
  }
}

// .mode-stamp {
//   position: absolute;
//   top: -10px;
//   left: -20px;
//   width: 90px;
//   height: 90px;
//   border-radius: 50%; /* This makes it a circle */
//   border: 3px dashed var(--danger-color); /* Dashed border with a red color */
//   color: var(--danger-color); /* Text color matches the border */
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   text-align: center;
//   font-weight: 600;
//   font-size: 18px;
//   transform: rotate(-15deg); /* Keeps the angle */
//   z-index: 10;
//   background-color: rgba(255, 255, 255, 0.6); /* A faint white background to ensure text is readable */
// }
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &.summary-card {
    background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
    border-left: 4px solid var(--primary-color);
  }

  &.warning-card {
    background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
    border-left: 4px solid var(--primary-color);
  }
}

.contact-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  color: var(--secondary-color);
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: var(--primary-color);
  }
}

.form-row {
  margin-bottom: 18px;
}

.institution-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 42px;
  line-height: normal;
  cursor: pointer;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--primary-color);
  }
}

.placeholder-text {
  color: #c0c4cc;
}

.preview-box {
  border: 1px solid var(--border-color);
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  min-height: 150px;
  /* 【新增】增加最大高度和滚动条 */
  max-height: 250px;
  /* 您可以根据需要调整这个最大高度值 */
  overflow: auto;
  /* 当内容超出时，自动显示垂直或水平滚动条 */
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.char-count {
  text-align: right;
  color: var(--info-color);
  margin-top: 8px;
  font-size: 13px;

  &.char-count-warning {
    color: var(--danger-color);
    font-weight: 500;
  }
}

.selected-contacts {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background: #fff;
}

.contact-tag {
  margin: 0 8px 8px 0;
  background: #ecf5ff;
  border-color: #d9ecff;
  color: var(--primary-color);
  display: inline-flex;
  align-items: center;

  ::v-deep .el-icon-close {
    color: var(--primary-color);

    &:hover {
      background-color: var(--primary-color);
      color: white;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

// Element UI 样式覆写
::v-deep .el-form-item {
  margin-bottom: 18px;
}

/* 【新增】针对文本域的样式调整 */
::v-deep .el-textarea__inner {
  // 重新启用手动调整大小的功能，这里设置为仅垂直方向
  resize: vertical;
  // 设置一个最小高度，防止用户拖得太小
  // 这里的数值可以根据 :rows 的值估算，例如 :rows="3" 对应一个合适的高度
  min-height: 80px !important;
  // 确保当内容溢出时，总是显示滚动条
  overflow-y: auto !important;
}

::v-deep .el-radio-button__inner {
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background: #fff;
}

.selected-contacts {
  min-height: 150px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background: #fafafa;
}

::v-deep .el-dialog__header {
  // 覆盖 element 默认的内边距，使其更紧凑
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0; // 添加一条分割线，视觉效果更好
}

.dialog-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}
</style>
