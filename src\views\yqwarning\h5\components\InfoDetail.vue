<template>
  <div class="tab-content active">
    <div class="header">
      <div class="title-section">
        <div class="title">
          <a :href="infoDetail.titleLink" target="_blank">{{ infoDetail.title }}</a>
          <span class="copy-link-icon" title="复制原文链接" @click="copyLink(infoDetail.titleLink)">
            <i class="el-icon-link" />
          </span>
        </div>
        <div class="stamp" :class="riskClass">{{ riskLevelText }}</div>
      </div>
      <div class="agency">机构:{{ infoDetail.agency }}</div>
      <div class="meta-info">
        <div class="time">
          <i class="far fa-clock" /> {{ infoDetail.time }}
        </div>
        <div class="source">
          来源：
          <template v-if="infoDetail.sourceLink">
            <a :href="infoDetail.sourceLink">{{ infoDetail.source }}</a>
          </template>
          <template v-else>
            {{ infoDetail.source }}
          </template>
        </div>
      </div>
    </div>
    <div v-if="mediaInfo" class="video-container" @click="openMediaPlayer">
      <div class="video-placeholder">
        <!-- 使用 v-if 确保有封面图才渲染 img 标签 -->
        <img v-if="mediaInfo.cover" :src="mediaInfo.cover" alt="媒体封面" class="cover-image">
      </div>
      <div class="play-button">
        <svg-icon icon-class="play_icon" class-name="play-icon" />
      </div>
    </div>
    <div v-if="infoDetail.analysis" class="section-title">
      <i class="fas fa-chart-line" /> 研判分析
    </div>
    <div v-if="infoDetail.analysis" class="analysis-box">
      <div class="analysis-content">
        <p>{{ infoDetail.analysis }}</p>
      </div>
    </div>
    <div class="section-title">
      <i class="fas fa-file-alt" /> 摘要
    </div>
    <div class="summary">
      <div class="summary-content">
        <p>{{ infoDetail.summary }}</p>
      </div>
    </div>
    <div class="copy-btn" @click="copyContent">
      <svg-icon icon-class="copy" class-name="copy-icon" />
    </div>

    <media-player
      :visible.sync="mediaPlayerVisible"
      :media-type="activeMedia.type"
      :video-src="activeMedia.videoSrc"
      :image-sources="activeMedia.imageSources"
      :audio-src="activeMedia.audioSrc"
      :ocr-text="activeMedia.ocrText"
    />
  </div>
</template>

<script>
import MediaPlayer from './MediaPlayer.vue'

export default {
  components: {
    MediaPlayer
  },
  props: ['infoDetail', 'rawContent', 'sentimentData'],
  data() {
    return {
      mediaPlayerVisible: false,
      activeMedia: {
        type: '', // 'video' or 'image'
        videoSrc: '',
        imageSources: [],
        audioSrc: '',
        ocrText: ''
      }
    }
  },
  computed: {
    mediaInfo() {
      // 首先检查数据是否存在，以及平台是否是抖音
      if (!this.sentimentData || this.sentimentData.platform !== 'douyin') {
        // 如果不是抖音平台，暂时不处理其媒体信息，直接返回null
        // 这是为了避免其他平台（如微博、快手）的资源因防盗链而无法访问
        return null
      }

      // --- 后续逻辑只对抖音平台生效 ---

      const data = this.sentimentData
      const images = data.images && Array.isArray(data.images) && data.images.length > 0 ? data.images : null
      const playUrl = data.playUrl && data.playUrl.length > 0 ? data.playUrl[0] : null
      const coverInfo = data.videoInfo ? data.videoInfo.coverInfo : null
      const coverUrl = coverInfo ? coverInfo.onlineUrl : null
      const ocrText = coverInfo ? coverInfo.coverOcr : ''

      // 规则1: 抖音特殊图文处理 (带背景音频)
      // 根据后端说明，postType='1' 是抖音图文的专用区分字段。
      if (data.postType === '1') {
        if (!images) return null // 如果是图文但没有图片，则不显示
        return {
          type: 'image', // 媒体类型是图文
          cover: coverUrl || images[0], // 优先用封面，否则用第一张图
          imageSources: images, // 图片列表
          audioSrc: playUrl, // playUrl此时是音频
          videoSrc: null, // 没有视频
          ocrText: ocrText
        }
      }

      // 规则2: 抖音视频作品处理 (根据 postCategory)
      // 对于抖音，postCategory=2 且 postType不为'1'的情况
      if (data.postCategory === 2) {
        if (!playUrl || (data.videoInfo && data.videoInfo.duration === 0)) {
          return null
        }
        return {
          type: 'video',
          cover: coverUrl,
          videoSrc: playUrl,
          imageSources: [],
          audioSrc: null,
          ocrText: ocrText
        }
      }

      // 如果以上所有条件都不满足，则认为没有可展示的媒体
      return null
    },
    riskClass() {
      if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('低')) {
        return 'risk-low'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('中')) {
        return 'risk-medium'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('高')) {
        return 'risk-high'
      } else {
        return ''
      }
    },
    riskLevelText() {
      if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('低')) {
        return '低风险'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('中')) {
        return '中风险'
      } else if (this.infoDetail.riskStamp && this.infoDetail.riskStamp.includes('高')) {
        return '高风险'
      } else {
        return ''
      }
    }
  },
  methods: {
    openMediaPlayer() {
      if (!this.mediaInfo) return

      this.activeMedia = {
        type: this.mediaInfo.type,
        videoSrc: this.mediaInfo.videoSrc || '',
        imageSources: this.mediaInfo.imageSources || [],
        audioSrc: this.mediaInfo.audioSrc || '',
        ocrText: this.mediaInfo.ocrText || ''
      }

      this.mediaPlayerVisible = true
    },
    copyLink(text) {
      if (!text) {
        this.$message.error('链接不存在，无法复制')
        return
      }
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
          .then(() => {
            this.$message.success('原文链接已复制')
          })
          .catch(err => {
            console.error('Failed to copy link: ', err)
            this.$message.error('复制失败')
          })
      } else {
        this.$message.error('浏览器不支持复制功能')
      }
    },
    copyContent() {
      if (!this.rawContent) {
        this.$message.error('内容不存在，无法复制')
        return
      }

      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(this.rawContent)
          .then(() => {
            this.$message({
              message: '内容已复制到剪贴板！',
              type: 'success',
              duration: 2000
            })
          })
          .catch(err => {
            console.error('Failed to copy: ', err)
            this.$message.error('复制失败，请手动复制。')
          })
      } else {
        this.$message.error('浏览器不支持粘贴功能')
      }
    }
  }
}
</script>

<style scoped lang="scss">
/* 原有样式保持不变，这里只添加新的抽屉相关样式 */
.header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 20px;

    .title-section {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;

        .title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            flex: 1;
            margin-right: 15px;
            display: flex;
            align-items: center;

            a {
                color: #1e5799;
                text-decoration: none;
            }

            .copy-link-icon {
                margin-left: 10px;
                cursor: pointer;
                color: #909399;
                font-size: 18px;

                &:hover {
                    color: #1e5799;
                }
            }
        }

        .stamp {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: bold;
            transform: rotate(-5deg);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            letter-spacing: 1px;
            flex-shrink: 0;
        }

        .risk-low {
            border: 2px dashed #FFD700;
            background: linear-gradient(145deg, #FFFACD, #FFF8DC);
            color: #FFD700;
        }

        .risk-medium {
            border: 2px dashed orange;
            background: linear-gradient(145deg, #fff5e6, #ffe6cc);
            color: orange;
        }

        .risk-high {
            border: 2px dashed red;
            background: linear-gradient(145deg, #fff0f0, #ffdede);
            color: red;
        }
    }

    .agency {
        font-size: 13px;
        color: #909399;
        margin-bottom: 10px;
    }

    .meta-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        font-size: 13px;
        color: #909399;

        .time {
            margin-right: 20px;
        }

        .source a {
            color: #67c23a;
            text-decoration: none;
        }
    }
}

.video-container {
    position: relative;
    .video-placeholder {
        width: 100%;
        position: relative;
        background-color: #e9ecef;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 56.25%;
        /* 16:9 宽高比占位 */
    }

    .cover-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s;
        z-index: 1;

        .play-icon {
            font-size: 30px;
            color: #1e5799;
        }
    }
}

.section-title {
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    padding-left: 10px;
    border-left: 4px solid #1e5799;

    i {
        margin-right: 10px;
        color: #1e5799;
    }
}

.analysis-box {
    background: #f0f7ff;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 25px;
    border-left: 4px solid #1e5799;

    .analysis-content {
        font-size: 15px;
        color: #333;
        line-height: 1.7;

        p {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.summary {
    background: #f8f8f9;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 25px;

    .summary-content {
        font-size: 15px;
        color: #555;
        line-height: 1.7;

        p {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.copy-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #1e5799;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(30, 87, 153, 0.4);
    transition: all 0.3s;
    z-index: 1000;

    .copy-icon {
        color: #fff;
        font-size: 32px;
    }

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 14px rgba(30, 87, 153, 0.5);
        background: #207cca;
    }

    i {
        font-size: 22px;
    }
}

@media (max-width: 480px) {
    .header {
        .title-section {
            .title {
                font-size: 18px;
            }
        }
    }

    .section-title {
        font-size: 16px;
    }

    .analysis-box {
        .analysis-content {
            font-size: 14px;
        }
    }

    .summary {
        .summary-content {
            font-size: 14px;
        }
    }

}

/* 抽屉遮罩层 */
.video-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-drawer-overlay.show {
    opacity: 1;
}

/* 底部抽屉容器 */
.video-drawer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-height: 80vh;
    overflow: hidden;

    &.video-drawer-open {
        transform: translateY(0);
    }
}

/* 抽屉头部 */
.video-drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;

    .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f0f0f0;
        }
    }
}

/* 抽屉内容区域 */
.video-drawer-content {
    padding: 0;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

/* 抽屉内的视频样式 */
.drawer-video {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
    background: #000;
    display: block;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .video-drawer {
        max-height: 70vh;
    }

    .video-drawer-header {
        padding: 12px 16px;

        .drawer-title {
            font-size: 14px;
        }
    }
}
</style>
