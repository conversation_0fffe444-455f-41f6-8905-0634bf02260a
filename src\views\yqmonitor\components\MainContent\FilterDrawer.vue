<template>
  <el-drawer :visible.sync="localVisible" title="筛选" size="800px" @close="handleClose">
    <el-form :model="form" label-width="120px">
      <el-form-item label="时间范围">
        <el-button-group>
          <el-button :type="timeRangeType === 'all' ? 'primary' : 'default'"
            @click="selectTimeRange('all')">全部</el-button>
          <el-button :type="timeRangeType === 'work' ? 'primary' : 'default'"
            @click="selectTimeRange('work')">工作时间</el-button>
          <el-button :type="timeRangeType === '24h' ? 'primary' : 'default'"
            @click="selectTimeRange('24h')">近24小时</el-button>
          <el-button :type="timeRangeType === '7d' ? 'primary' : 'default'"
            @click="selectTimeRange('7d')">近7天</el-button>
          <el-button :type="timeRangeType === 'today' ? 'primary' : 'default'"
            @click="selectTimeRange('today')">当天</el-button>
          <el-button :type="timeRangeType === 'custom' ? 'primary' : 'default'"
            @click="selectTimeRange('custom')">自定义时间</el-button>
        </el-button-group>
        <el-date-picker v-if="timeRangeType === 'custom'" v-model="dateRange" type="datetimerange" range-separator="—"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss" style="margin-top: 10px" />
      </el-form-item>
      <el-form-item label="倾向性">
        <el-checkbox-group v-model="sensitivitysUI" @change="handleSensitivitysUIChange">
          <el-checkbox-button v-for="item in dict.type.filter_sentiment" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="AI舆情类型">
        <el-checkbox-group v-model="aiSentimentTypesUI" @change="handleAiSentimentTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.ai_public_opinion_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="作品发布类型">
        <el-checkbox-group v-model="publishTypesUI" @change="handlePublishTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.content_publish_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="账号认证类型">
        <el-checkbox-group v-model="accountAuthTypesUI" @change="handleAccountAuthTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.account_auth_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="信息来源">
        <el-checkbox-group v-model="platformsUI" @change="handlePlatformsUIChange">
          <el-checkbox-button v-for="item in dict.type.content_platform" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="舆情特征">
        <el-checkbox-group v-model="negativeSentimentTagsUI" @change="handleNegativeSentimentTagsUIChange">
          <el-checkbox-button v-for="item in dict.type.negative_public_opinion_tag" :key="item.value"
            :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="信源等级">
        <el-checkbox-group v-model="sourceLevelsUI" @change="handleSourceLevelsUIChange">
          <el-checkbox-button v-for="item in dict.type.source_level" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="噪音">
        <el-radio-group v-model="noiseUI">
          <el-radio-button v-for="item in dict.type.is_noise" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="机构词过滤">
        <div class="filter-group">
          <el-radio-group v-model="institutionOpenFlagUI">
            <el-radio-button v-for="item in dict.type.institution_word_filter" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="相似信息">
        <el-radio-group v-model="similarUI">
          <el-radio-button v-for="item in dict.type.is_similar" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="阅读状态">
        <el-radio-group v-model="readStatusUI">
          <el-radio-button v-for="item in readStatusDict" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="粉丝数量">
        <el-button-group>
          <el-button v-for="item in fansCountOptions" :key="item.value" :type="fansCountSelected === item.value && !fansCountCustom
            ? 'primary'
            : 'default'
            " style="margin-bottom: 10px; margin-right: 10px" @click="handleFansCountSelect(item.value)">
            {{ item.label }}
          </el-button>
          <el-button :type="fansCountCustom ? 'primary' : 'default'" @click="handleFansCountCustom">自定义</el-button>
        </el-button-group>
        <div v-if="fansCountCustom" style="margin-top: 10px">
          <el-input-number v-model="form.minFansCount" :min="0" placeholder="最小" style="width: 100px" :controls="false"
            @change="(val) => handleFansCountInput('minFansCount', val)" />
          <span style="margin: 0 8px">~</span>
          <el-input-number v-model="form.maxFansCount" :min="0" placeholder="最大" style="width: 100px" :controls="false"
            @change="(val) => handleFansCountInput('maxFansCount', val)" />
        </div>
      </el-form-item>
      <el-form-item label="必含词">
        <div v-for="(group, index) in form.mustContainGroups" :key="group.id" class="keyword-group">
          <el-input v-model="group.value" placeholder="词组内多个词请用空格分隔" style="width: 300px" />
          <el-button v-if="form.mustContainGroups.length > 1" type="danger" icon="el-icon-minus" circle size="mini"
            class="keyword-group-btn" @click="removeMustContainGroup(group.id)" />
          <el-button v-if="index === form.mustContainGroups.length - 1" type="primary" icon="el-icon-plus" circle
            size="mini" class="keyword-group-btn" @click="addMustContainGroup" />
        </div>
      </el-form-item>
      <!-- <el-form-item label="任意词">
        <el-input
          v-model="form.shouldContain"
          placeholder="多个词请用空格分隔"
          style="width: 300px"
        />
      </el-form-item> -->
      <el-form-item label="排除词">
        <div v-for="(group, index) in form.mustNotContainGroups" :key="group.id" class="keyword-group">
          <el-input v-model="group.value" placeholder="词组内多个词请用空格分隔" style="width: 300px" />
          <el-button v-if="form.mustNotContainGroups.length > 1" type="danger" icon="el-icon-minus" circle size="mini"
            class="keyword-group-btn" @click="removeMustNotContainGroup(group.id)" />
          <el-button v-if="index === form.mustNotContainGroups.length - 1" type="primary" icon="el-icon-plus" circle
            size="mini" class="keyword-group-btn" @click="addMustNotContainGroup" />
        </div>
      </el-form-item>
      <el-form-item label="标题排除词">
        <el-input v-model="form.titleMustNotContain" placeholder="多个词请用空格分隔" style="width: 300px" />
      </el-form-item>
    </el-form>
    <div class="filter-drawer-footer">
      <el-button class="btn-reset" @click="resetForm">重置</el-button>
      <el-button type="primary" @click="handleSubmit">应用</el-button>
      <el-button class="btn-save" type="warning" @click="handleSubmitAndSave">应用并保存</el-button>
    </div>
  </el-drawer>
</template>
<script>
import { getDateRangeByType } from '@/utils/yqmonitorTool'
export default {
  name: 'FilterDrawer',
  dicts: [
    'filter_sentiment',
    'ai_public_opinion_type',
    'content_publish_type',
    'account_auth_type',
    'source_level',
    'is_noise',
    'is_similar',
    'read_status',
    'follow_read_status', // 新增字典
    'target_detect_read_status', // 新增字典
    'content_platform',
    'negative_public_opinion_tag',
    'institution_word_filter'
  ],
  props: {
    visible: Boolean,
    editingTagId: {
      type: String,
      default: ''
    },
    moduleType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: {
        startTime: '',
        endTime: '',
        sensitivitys: [],
        aiSentimentTypes: [],
        publishTypes: [],
        accountAuthTypes: [],
        platforms: [],
        negativeSentimentTags: [],
        sourceLevels: [],
        noiseTypes: [],
        enableSimilarityDedup: 'fold',
        // readStatus, followReadStatus, targetDetectReadStatus 会被动态添加
        minFansCount: 0,
        maxFansCount: 0,
        mustContainGroups: [],
        mustNotContainGroups: [],
        shouldContain: '',
        titleMustNotContain: '',
        institutionOpenFlags: []
      },
      dateRange: [],
      sensitivitysUI: ['all'],
      sourceLevelsUI: ['all'],
      aiSentimentTypesUI: ['all'],
      publishTypesUI: ['all'],
      accountAuthTypesUI: ['all'],
      platformsUI: ['all'],
      negativeSentimentTagsUI: ['all'],
      readStatusUI: 'all', // 这个值会动态绑定到不同的状态
      noiseUI: 'all',
      institutionOpenFlagUI: 'all',
      similarUI: 'fold',
      localVisible: this.visible,
      fansCountOptions: [
        { label: '全部', value: 'all', min: 0, max: 0 },
        { label: '0 ~ 1000', value: '0-1000', min: 0, max: 1000 },
        { label: '1000 ~ 5000', value: '1000-5000', min: 1000, max: 5000 },
        { label: '5000 ~ 1w', value: '5000-10000', min: 5000, max: 10000 },
        { label: '1w ~ 10w', value: '10000-100000', min: 10000, max: 100000 },
        {
          label: '10w ~ 100w',
          value: '100000-1000000',
          min: 100000,
          max: 1000000
        },
        { label: '100w以上', value: '1000000-', min: 1000000, max: '' }
      ],
      fansCountSelected: '',
      fansCountCustom: false,
      timeRangeType: 'all'
    }
  },
  computed: {
    cachedTimeRangeType() {
      return this.$store.getters['yqmonitorMenu/cachedTimeRangeType']
    },
    // 新增计算属性
    readStatusKey() {
      const keyMap = {
        yqmonitor: 'readStatus',
        followedEvents: 'followReadStatus',
        targetMonitor: 'targetDetectReadStatus'
      };
      return keyMap[this.moduleType] || 'readStatus';
    },
    // 新增计算属性
    readStatusDict() {
      const dictMap = {
        yqmonitor: this.dict.type.read_status,
        followedEvents: this.dict.type.follow_read_status,
        targetMonitor: this.dict.type.target_detect_read_status
      };
      return dictMap[this.moduleType] || this.dict.type.read_status || [];
    }
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
    dateRange(val) {
      if (Array.isArray(val) && val.length === 2) {
        this.form.startTime = val[0]
        this.form.endTime = val[1]
      } else {
        this.form.startTime = ''
        this.form.endTime = ''
      }
    },
    sensitivitysUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.sensitivitys = []
      } else {
        this.form.sensitivitys = val.filter((item) => item !== 'all')
      }
    },
    aiSentimentTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.aiSentimentTypes = []
      } else {
        this.form.aiSentimentTypes = val.filter((item) => item !== 'all')
      }
    },
    publishTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.publishTypes = []
      } else {
        this.form.publishTypes = val.filter((item) => item !== 'all')
      }
    },
    accountAuthTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.accountAuthTypes = []
      } else {
        this.form.accountAuthTypes = val.filter((item) => item !== 'all')
      }
    },
    platformsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.platforms = []
      } else {
        this.form.platforms = val.filter((item) => item !== 'all')
      }
    },
    negativeSentimentTagsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.negativeSentimentTags = []
      } else {
        this.form.negativeSentimentTags = val.filter((item) => item !== 'all')
      }
    },
    sourceLevelsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.sourceLevels = []
      } else {
        this.form.sourceLevels = val.filter((item) => item !== 'all')
      }
    },
    noiseUI(val) {
      this.form.noiseTypes = val === 'all' ? [] : [val]
    },
    institutionOpenFlagUI(val) {
      this.form.institutionOpenFlags = val === 'all' ? [] : [val]
    },
    readStatusUI(val) {
      // 移除旧的key
      delete this.form.readStatus;
      delete this.form.followReadStatus;
      delete this.form.targetDetectReadStatus;
      // 设置新的key
      if (val !== 'all') {
        this.form[this.readStatusKey] = [val];
      }
    },
    similarUI(val) {
      this.form.enableSimilarityDedup = val
    },
    localVisible(val) {
      console.log('localVisible', val)
      if (val && !this.$store.state.yqmonitorMenu.isOpeningFromTag) {
        this.initForm()
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    resetForm() {
      this.sensitivitysUI = ['all']
      this.aiSentimentTypesUI = ['all']
      this.publishTypesUI = ['all']
      this.accountAuthTypesUI = ['all']
      this.platformsUI = ['all']
      this.negativeSentimentTagsUI = ['all']
      this.sourceLevelsUI = ['all']
      this.readStatusUI = 'all'
      // 移除所有可能的已读状态key
      delete this.form.readStatus;
      delete this.form.followReadStatus;
      delete this.form.targetDetectReadStatus;
      this.noiseUI = 'all'
      this.institutionOpenFlagUI = 'all'
      this.similarUI = 'fold'
      this.form.noiseTypes = []
      this.form.readStatus = []
      this.form.institutionOpenFlags = []
      this.form.enableSimilarityDedup = 'fold'
      this.timeRangeType = 'all'
      this.dateRange = []
      this.fansCountSelected = 'all'
      this.fansCountCustom = false
      this.form.minFansCount = 0
      this.form.maxFansCount = 0
      this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
      this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
      this.form.shouldContain = ''
      this.form.titleMustNotContain = ''
    },
    async handleSubmit() {
      try {
        await this.$confirm('确定要应用当前筛选条件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (this.fansCountCustom) {
          if (Number(this.form.minFansCount) > Number(this.form.maxFansCount)) {
            this.$message.error('最小粉丝数不能大于最大粉丝数')
            return
          }
        }
        const formCopy = { ...this.form }
        // 清理所有可能的已读状态字段，以防万一
        delete formCopy.readStatus;
        delete formCopy.followReadStatus;
        delete formCopy.targetDetectReadStatus;
        // 根据当前的UI选项设置正确的字段
        if (this.readStatusUI !== 'all') {
          formCopy[this.readStatusKey] = [this.readStatusUI];
        } else {
          formCopy[this.readStatusKey] = [];
        }

        formCopy.mustContainGroups = this.form.mustContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean)
        formCopy.mustNotContainGroups = this.form.mustNotContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean)

        this.$emit('submit', formCopy)
        this.handleClose()
      } catch (e) {
        console.log(e)
      }
    },
    async handleSubmitAndSave() {
      try {
        await this.$confirm('确定要应用并保存当前筛选条件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (this.fansCountCustom) {
          if (Number(this.form.minFansCount) > Number(this.form.maxFansCount)) {
            this.$message.error('最小粉丝数不能大于最大粉丝数')
            return
          }
        }
        const formCopy = { ...this.form }
        if (this.editingTagId) {
          this.$emit('updateFilterWord', {
            id: this.editingTagId,
            filterWord: formCopy
          })
          this.handleClose()
          return
        }
        this.$emit('submitAndSave', formCopy)
        this.handleClose()
      } catch (e) {
        console.log(e)
      }
    },
    handleFansCountSelect(value) {
      this.fansCountCustom = false
      this.fansCountSelected = value
      const option = this.fansCountOptions.find((opt) => opt.value === value)
      if (option) {
        this.form.minFansCount = option.min
        this.form.maxFansCount = option.max
      }
    },
    handleFansCountCustom() {
      this.fansCountCustom = true
      this.fansCountSelected = ''
      this.form.minFansCount = 0
      this.form.maxFansCount = 0
    },
    handleFansCountInput(key, val) {
      if (val === undefined || val === null || val === '') {
        this.form[key] = 0
      }
    },
    handleSensitivitysUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.sensitivitysUI = ['all']
        } else {
          this.sensitivitysUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.sensitivitysUI = ['all']
      }
    },
    handleAiSentimentTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.aiSentimentTypesUI = ['all']
        } else {
          this.aiSentimentTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.aiSentimentTypesUI = ['all']
      }
    },
    handlePublishTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.publishTypesUI = ['all']
        } else {
          this.publishTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.publishTypesUI = ['all']
      }
    },
    handleAccountAuthTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.accountAuthTypesUI = ['all']
        } else {
          this.accountAuthTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.accountAuthTypesUI = ['all']
      }
    },
    handlePlatformsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.platformsUI = ['all']
        } else {
          this.platformsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.platformsUI = ['all']
      }
    },
    handleNegativeSentimentTagsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.negativeSentimentTagsUI = ['all']
        } else {
          this.negativeSentimentTagsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.negativeSentimentTagsUI = ['all']
      }
    },
    handleSourceLevelsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.sourceLevelsUI = ['all']
        } else {
          this.sourceLevelsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.sourceLevelsUI = ['all']
      }
    },
    selectTimeRange(type) {
      this.timeRangeType = type
      this.dateRange = getDateRangeByType(type)
      if (type !== 'all' && type !== 'custom') {
        this.$store.commit('yqmonitorMenu/SET_CACHED_TIME_RANGE_TYPE', type)
      } else {
        this.$store.commit('yqmonitorMenu/SET_CACHED_TIME_RANGE_TYPE', null)
      }
    },
    initForm() {
      const lastQueryParams = this.$store.state.yqmonitorMenu.lastQueryParams
      if (lastQueryParams && Object.keys(lastQueryParams).length > 0) {
        if (
          this.cachedTimeRangeType &&
          this.cachedTimeRangeType !== 'all' &&
          this.cachedTimeRangeType !== 'custom'
        ) {
          this.selectTimeRange(this.cachedTimeRangeType)
        } else if (!lastQueryParams.startTime || !lastQueryParams.endTime) {
          this.timeRangeType = 'all'
          this.dateRange = []
        } else {
          this.timeRangeType = 'custom'
          this.dateRange = [lastQueryParams.startTime, lastQueryParams.endTime]
        }
        this.form.startTime = lastQueryParams.startTime || ''
        this.form.endTime = lastQueryParams.endTime || ''
        this.sensitivitysUI =
          lastQueryParams.sensitivitys?.length === 0
            ? ['all']
            : lastQueryParams.sensitivitys || ['all']
        this.form.sensitivitys = lastQueryParams.sensitivitys || []
        this.aiSentimentTypesUI =
          lastQueryParams.aiSentimentTypes?.length === 0
            ? ['all']
            : lastQueryParams.aiSentimentTypes || ['all']
        this.form.aiSentimentTypes = lastQueryParams.aiSentimentTypes || []
        this.publishTypesUI =
          lastQueryParams.publishTypes?.length === 0
            ? ['all']
            : lastQueryParams.publishTypes || ['all']
        this.form.publishTypes = lastQueryParams.publishTypes || []
        this.accountAuthTypesUI =
          lastQueryParams.accountAuthTypes?.length === 0
            ? ['all']
            : lastQueryParams.accountAuthTypes || ['all']
        this.form.accountAuthTypes = lastQueryParams.accountAuthTypes || []
        this.platformsUI =
          lastQueryParams.platforms?.length === 0
            ? ['all']
            : lastQueryParams.platforms || ['all']
        this.form.platforms = lastQueryParams.platforms || []
        this.negativeSentimentTagsUI =
          lastQueryParams.negativeSentimentTags?.length === 0
            ? ['all']
            : lastQueryParams.negativeSentimentTags || ['all']
        this.form.negativeSentimentTags =
          lastQueryParams.negativeSentimentTags || []
        this.sourceLevelsUI =
          lastQueryParams.sourceLevels?.length === 0
            ? ['all']
            : lastQueryParams.sourceLevels || ['all']
        this.form.sourceLevels = lastQueryParams.sourceLevels || []
        const currentReadStatusValue = lastQueryParams[this.readStatusKey];
        this.readStatusUI = currentReadStatusValue && currentReadStatusValue.length > 0
          ? currentReadStatusValue[0]
          : 'all';
        this.form.readStatus = lastQueryParams.readStatus || []
        this.noiseUI =
          lastQueryParams.noiseTypes?.length === 0
            ? 'all'
            : lastQueryParams.noiseTypes?.[0] || 'all'
        this.form.noiseTypes = lastQueryParams.noiseTypes || []
        this.institutionOpenFlagUI =
          lastQueryParams.institutionOpenFlags?.length === 0
            ? 'all'
            : lastQueryParams.institutionOpenFlags?.[0] || 'all'
        this.form.institutionOpenFlags =
          lastQueryParams.institutionOpenFlags || []
        this.similarUI = lastQueryParams.enableSimilarityDedup || 'fold'
        this.form.enableSimilarityDedup =
          lastQueryParams.enableSimilarityDedup || 'fold'
        let matched = false
        if (!lastQueryParams.minFansCount && !lastQueryParams.maxFansCount) {
          this.fansCountSelected = 'all'
          this.fansCountCustom = false
          this.form.minFansCount = 0
          this.form.maxFansCount = 0
        } else {
          for (const opt of this.fansCountOptions) {
            if (
              Number(lastQueryParams.minFansCount) === Number(opt.min) &&
              (opt.max === ''
                ? lastQueryParams.maxFansCount === '' ||
                lastQueryParams.maxFansCount === null
                : Number(lastQueryParams.maxFansCount) === Number(opt.max))
            ) {
              this.fansCountSelected = opt.value
              this.fansCountCustom = false
              matched = true
              break
            }
          }
          if (!matched) {
            this.fansCountSelected = ''
            this.fansCountCustom = true
            this.form.minFansCount = lastQueryParams.minFansCount || 0
            this.form.maxFansCount = lastQueryParams.maxFansCount || 0
          }
        }
        // 初始化必含词组
        const mustContainGroupsVal = lastQueryParams.mustContainGroups || []
        if (!mustContainGroupsVal || mustContainGroupsVal.length === 0) {
          this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
        } else {
          this.form.mustContainGroups = mustContainGroupsVal.map(
            (value, index) => ({ id: Date.now() + index, value })
          )
        }

        // 初始化排除词组
        const mustNotContainGroupsVal = lastQueryParams.mustNotContainGroups || []
        if (!mustNotContainGroupsVal || mustNotContainGroupsVal.length === 0) {
          this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
        } else {
          this.form.mustNotContainGroups = mustNotContainGroupsVal.map(
            (value, index) => ({ id: Date.now() + index + 1000, value })
          )
        }

        this.form.shouldContain = lastQueryParams.shouldContain || ''
        this.form.titleMustNotContain = lastQueryParams.titleMustNotContain || ''
      } else {
        this.resetForm()
      }
    },
    initFormFromJson(jsonStr) {
      let data
      try {
        data = JSON.parse(jsonStr)
      } catch (e) {
        this.$message.error('数据格式错误，无法解析')
        return
      }
      this.form = {
        ...this.form,
        ...data,
        sensitivitys: data.sensitivitys || [],
        aiSentimentTypes: data.aiSentimentTypes || [],
        publishTypes: data.publishTypes || [],
        accountAuthTypes: data.accountAuthTypes || [],
        platforms: data.platforms || [],
        negativeSentimentTags: data.negativeSentimentTags || [],
        sourceLevels: data.sourceLevels || [],
        noiseTypes: data.noiseTypes || [],
        readStatus: data.readStatus || [],
        institutionOpenFlags: data.institutionOpenFlags || [],
        enableSimilarityDedup: data.enableSimilarityDedup || 'fold'
      }
      const readStatusKeys = {
        readStatus: 'yqmonitor',
        followReadStatus: 'followedEvents',
        targetDetectReadStatus: 'targetMonitor'
      };
      let foundKey = 'readStatus';
      for (const key in readStatusKeys) {
        if (data[key] && data[key].length > 0) {
          foundKey = key;
          break;
        }
      }
      this.readStatusUI = data[foundKey] && data[foundKey].length > 0 ? data[foundKey][0] : 'all';
      if (data.mustContainGroups && data.mustContainGroups.length > 0) {
        this.form.mustContainGroups = data.mustContainGroups.map(
          (g, index) => ({
            id: g.id || Date.now() + index,
            value: g.value || ''
          })
        )
      } else if (data.mustContain) {
        this.form.mustContainGroups = [{ id: Date.now(), value: data.mustContain }]
      } else {
        this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
      }

      if (data.mustNotContainGroups && data.mustNotContainGroups.length > 0) {
        this.form.mustNotContainGroups = data.mustNotContainGroups.map(
          (g, index) => ({
            id: g.id || Date.now() + index + 1000,
            value: g.value || ''
          })
        )
      } else if (data.mustNotContain) {
        this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: data.mustNotContain }]
      } else {
        this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
      }

      this.form.shouldContain = data.shouldContain || ''
      this.form.titleMustNotContain = data.titleMustNotContain || ''

      if (
        data.cachedTimeRangeType &&
        data.cachedTimeRangeType !== 'all' &&
        data.cachedTimeRangeType !== 'custom'
      ) {
        this.selectTimeRange(data.cachedTimeRangeType)
      } else if (this.form.startTime && this.form.endTime) {
        this.dateRange = [this.form.startTime, this.form.endTime]
        this.timeRangeType = 'custom'
      } else {
        this.dateRange = []
        this.timeRangeType = 'all'
      }
      delete this.form.cachedTimeRangeType
      this.sensitivitysUI =
        this.form.sensitivitys.length === 0 ? ['all'] : this.form.sensitivitys
      this.aiSentimentTypesUI =
        this.form.aiSentimentTypes.length === 0
          ? ['all']
          : this.form.aiSentimentTypes
      this.publishTypesUI =
        this.form.publishTypes.length === 0 ? ['all'] : this.form.publishTypes
      this.accountAuthTypesUI =
        this.form.accountAuthTypes.length === 0
          ? ['all']
          : this.form.accountAuthTypes
      this.platformsUI =
        this.form.platforms.length === 0 ? ['all'] : this.form.platforms
      this.negativeSentimentTagsUI =
        this.form.negativeSentimentTags.length === 0
          ? ['all']
          : this.form.negativeSentimentTags
      this.sourceLevelsUI =
        this.form.sourceLevels.length === 0 ? ['all'] : this.form.sourceLevels
      this.noiseUI =
        this.form.noiseTypes.length === 0 ? 'all' : this.form.noiseTypes[0]
      this.institutionOpenFlagUI =
        this.form.institutionOpenFlags.length === 0
          ? 'all'
          : this.form.institutionOpenFlags[0]
      // this.readStatusUI =
      //   this.form.readStatus.length === 0 ? 'all' : this.form.readStatus[0]
      this.similarUI = this.form.enableSimilarityDedup
      let matched = false
      if (!this.form.minFansCount && !this.form.maxFansCount) {
        this.fansCountSelected = 'all'
        this.fansCountCustom = false
      } else {
        for (const opt of this.fansCountOptions) {
          if (
            Number(this.form.minFansCount) === Number(opt.min) &&
            (opt.max === ''
              ? this.form.maxFansCount === '' || this.form.maxFansCount === null
              : Number(this.form.maxFansCount) === Number(opt.max))
          ) {
            this.fansCountSelected = opt.value
            this.fansCountCustom = false
            matched = true
            break
          }
        }
        if (!matched) {
          this.fansCountSelected = ''
          this.fansCountCustom = true
        }
      }
    },
    addMustContainGroup() {
      this.form.mustContainGroups.push({ id: Date.now(), value: '' })
    },
    removeMustContainGroup(id) {
      const index = this.form.mustContainGroups.findIndex((g) => g.id === id)
      if (index !== -1) {
        this.form.mustContainGroups.splice(index, 1)
      }
    },
    addMustNotContainGroup() {
      this.form.mustNotContainGroups.push({ id: Date.now(), value: '' })
    },
    removeMustNotContainGroup(id) {
      const index = this.form.mustNotContainGroups.findIndex((g) => g.id === id)
      if (index !== -1) {
        this.form.mustNotContainGroups.splice(index, 1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.filter-drawer-footer {
  position: fixed;
  right: 0;
  bottom: 0;
  width: 800px;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px 40px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  z-index: 100;
}

.btn-reset {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #d9d9d9;
}

.btn-save {
  background: #fff7e6;
  color: #faad14;
  border: 1px solid #faad14;
}

::v-deep .el-drawer__body {
  padding-bottom: 100px;
}

.keyword-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.keyword-group:last-child {
  margin-bottom: 0;
}

.keyword-group-btn {
  margin-left: 8px;
}
</style>
