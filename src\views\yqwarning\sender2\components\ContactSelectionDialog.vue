<template>
  <el-dialog
    title="选择联系人"
    :visible.sync="dialogVisible"
    width="60%"
    :show-close="false"
  >
    <div slot="title" class="dialog-header-actions">
      <span class="dialog-title">选择联系人</span>
      <div class="dialog-buttons">
        <el-button size="small" @click="handleCancel">取 消</el-button>
        <el-button type="primary" size="small" @click="handleConfirm">确 定</el-button>
      </div>
    </div>
    <el-input
      v-model="searchText"
      placeholder="输入联系人姓名或机构/分组搜索"
      clearable
      style="margin-bottom: 15px"
    />
    <!-- 联系人选择树 -->
    <el-tree
      ref="contactTree"
      :data="treeData"
      show-checkbox
      node-key="id"
      :props="{ label: 'label', children: 'children', disabled: 'disabled' }"
      :default-checked-keys="initialSelectedIds"
      :filter-node-method="filterTreeNode"
      check-on-click-node
      default-expand-all
      @check="handleTreeCheck"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <i v-if="data.isInstitution" class="el-icon-office-building" style="color: #409EFF;" />
        <i v-if="data.isGroup" class="el-icon-collection-tag" style="color: #67C23A;" />
        <i v-if="data.isContact" class="el-icon-user" style="color: #E6A23C;" />
        <span style="margin-left: 5px;">{{ maskLabelPhone(node.label) }}</span>
      </span>
    </el-tree>
  </el-dialog>
</template>

<script>
export default {
  name: 'ContactSelectionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: () => []
    },
    initialSelectedIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchText: '',
      tempSelectedNodes: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    searchText(val) {
      if (this.$refs.contactTree) {
        this.$refs.contactTree.filter(val)
      }
    },
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时，重置临时选择状态
        this.tempSelectedNodes = []
        this.$nextTick(() => {
          if (this.$refs.contactTree) {
            this.$refs.contactTree.setCheckedKeys(this.initialSelectedIds)
          }
        })
      }
    }
  },
  methods: {
    filterTreeNode(value, data, node) {
      // 如果搜索框为空，则显示所有节点
      if (!value) return true

      // 检查当前节点或其任何父节点的标签是否包含搜索值
      // `node` 对象包含了父节点的引用，我们可以利用它向上追溯
      let currentNode = node
      while (currentNode) {
        // 使用不区分大小写的匹配
        if (currentNode.data.label && currentNode.data.label.toLowerCase().includes(value.toLowerCase())) {
          // 只要路径上有一个节点匹配，就显示当前节点
          return true
        }
        // 移动到父节点继续检查
        currentNode = currentNode.parent
      }

      // 如果整条路径上都没有匹配的，则隐藏该节点
      return false
    },
    /** 手机号脱敏，仅展示用 */
    maskPhone(phone) {
      if (!phone || phone.length !== 11) return phone
      return phone.substr(0, 3) + '****' + phone.substr(7, 4)
    },
    /** 只处理“姓名（手机号）”格式label，手机号脱敏 */
    maskLabelPhone(label) {
      // 匹配所有连续11位手机号并脱敏
      return label.replace(/1\d{10}/g, (phone) => this.maskPhone(phone))
    },
    handleTreeCheck(clickedNode, checkedStatus) {
      // Filter out group nodes, only take actual contacts (leaf nodes with contactId not starting with "group_")
      this.tempSelectedNodes = checkedStatus.checkedNodes.filter(
        (node) => node.contactId && !node.contactId.startsWith('group_') && node.id // Ensure 'id' (userId) exists
      )
    },
    handleConfirm() {
      // 1. 获取所有新勾选的联系人节点
      const checkedNodes = this.$refs.contactTree.getCheckedNodes(true) // true 表示只获取叶子节点

      // 2. 将节点转换为我们需要的 contact 对象格式
      const newContactsMap = new Map()
      checkedNodes.forEach(contactNode => {
        // 通过父节点获取分组和机构信息
        const groupNode = this.$refs.contactTree.getNode(contactNode.id).parent
        const institutionNode = groupNode.parent

        newContactsMap.set(contactNode.id, {
          id: contactNode.userId,
          name: contactNode.userName,
          phone: contactNode.phoneNumber,
          sendType: contactNode.warnType || 'all',
          institutionId: institutionNode.data.code,
          institutionName: institutionNode.data.label,
          groupId: groupNode.data.groupId,
          groupName: groupNode.data.label,
          contactId: contactNode.id,
          displayName: contactNode.label
        })
      })

      const selectedContacts = Array.from(newContactsMap.values())
      this.$emit('confirm', selectedContacts)
      this.dialogVisible = false
    },
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

::v-deep .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}
</style>
