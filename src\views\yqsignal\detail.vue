<template>
  <div class="app-container">
    <!-- 面包屑导航 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <router-link to="/signal/group" class="breadcrumb-link">信源组列表</router-link>
        </el-breadcrumb-item>
        <el-breadcrumb-item>信源组明细</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="main-card">
      <!-- 标题卡片 -->
      <div class="title-card">
        <div class="title-section">
          <div class="group-name">信源组明细</div>
          <div class="search-section">
            <div class="search-item">
              <span>平台：</span>
              <el-select v-model="search.platform" placeholder="平台" clearable @change="handlePlatformChange">
                <el-option
                  v-for="item in platforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="search-item">
              <span>关键词：</span>
              <el-input v-model="search.keyword" placeholder="请输入关键词" clearable>
                <template #append>
                  <el-button icon="el-icon-search" @click="handleSearch" />
                </template>
              </el-input>
            </div>
            <div class="search-item">
              <span>用户ID：</span>
              <el-input v-model="search.user_id" placeholder="请输入用户ID" clearable>
                <template #append>
                  <el-button icon="el-icon-search" @click="handleSearch" />
                </template>
              </el-input>
            </div>
            <div class="search-item">
              <span>主域名：</span>
              <el-input v-model="search.main_domain" placeholder="请输入主域名" clearable>
                <template #append>
                  <el-button icon="el-icon-search" @click="handleSearch" />
                </template>
              </el-input>
            </div>
          </div>
        </div>
        <div class="header-container">
          <div class="group-title">{{ groupName }}</div>
          <div class="operations">
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd">添加信源</el-button>
            <el-button type="danger" icon="el-icon-delete" :disabled="!multipleSelection.length" @click="handleBatchDelete">批量删除</el-button>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="sourceList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="nickname" label="账号昵称" align="center" />
        <el-table-column prop="user_id" label="用户ID" align="center" />
        <el-table-column prop="main_domain" label="主域名" align="center" />
        <el-table-column prop="platform_name" label="平台" align="center" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button type="text" class="danger-text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :total="total"
          :page-size="search.page_size"
          :current-page="search.page"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 添加信源抽屉组件 -->
    <signal-form ref="signalForm" :platforms="platforms" :direct-id="search.direct_id" @add-signal="handleAddSignal" />
  </div>
</template>

<script>
import SignalForm from './SignalForm.vue'
import { directDataList, batchCreateDetail, batchDeleteDetail } from '@/api/yqsignal'

export default {
  name: 'SignalGroupDetail',
  components: {
    SignalForm
  },
  data() {
    return {
      loading: false,
      search: {
        direct_id: undefined,
        platform: '',
        keyword: '',
        user_id: '',
        main_domain: '',
        page: 1,
        page_size: 10
      },
      platforms: [
        { label: '全部', value: '' },
        { label: '抖音', value: 'douyin' },
        { label: '微博', value: 'weibo' },
        { label: '微信', value: 'weixin' },
        { label: '头条', value: 'toutiao' },
        { label: '小红书', value: 'xiaohongshu' },
        { label: '网页', value: 'web' },
        { label: '客户端', value: 'app' },
        { label: '论坛', value: 'bbs' },
        { label: '数字报', value: 'enews' },
        { label: '境外', value: 'jingwai' },
        { label: '其他视频', value: 'shipin' }
      ],
      groupName: '',
      sourceList: [],
      total: 0,
      multipleSelection: []
    }
  },
  created() {
    this.groupName = this.$route.query.name || '未知信源组'
    this.search.direct_id = Number(this.$route.params.id)
    this.fetchData()
  },
  methods: {
    // 获取列表数据
    async fetchData() {
      try {
        this.loading = true
        const res = await directDataList(this.search)
        this.sourceList = res.data.list || []
        this.total = res.data.total || 0
      } catch (error) {
        console.error('获取信源组明细失败:', error)
        this.$message.error('获取信源组明细失败')
      } finally {
        this.loading = false
      }
    },
    // 搜索
    handleSearch() {
      this.search.page = 1
      this.fetchData()
    },
    // 平台选择改变
    handlePlatformChange() {
      this.handleSearch()
    },
    // 分页改变
    handlePageChange(page) {
      this.search.page = page
      this.fetchData()
    },
    handleAdd() {
      this.$refs.signalForm.show()
    },
    async handleAddSignal(signal) {
      try {
        const params = {
          direct_id: this.search.direct_id,
          direct_detail_list: Array.isArray(signal) ? signal : [signal]
        }
        const res = await batchCreateDetail(params)
        if (res.code === 0) {
          this.$message.success('添加信源成功!')
          // 重新获取列表
          await this.fetchData()
        } else {
          this.$message.error(res.msg || '添加信源失败!')
        }
      } catch (error) {
        console.error('添加信源失败:', error)
        this.$message.error('添加信源失败')
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该信源吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const params = {
          direct_id: this.search.direct_id,
          direct_detail_id: [row.id]
        }

        await batchDeleteDetail(params)
        this.$message.success('删除成功!')
        this.fetchData()
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除')
        } else {
          console.error('删除信源失败:', error)
          this.$message.error('删除信源失败')
        }
      }
    },
    async handleBatchDelete() {
      try {
        await this.$confirm('确认批量删除选中的信源吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const params = {
          direct_id: this.search.direct_id,
          direct_detail_id: this.multipleSelection.map(item => item.id)
        }

        await batchDeleteDetail(params)
        this.$message.success('批量删除成功!')
        this.fetchData()
        this.multipleSelection = []
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除')
        } else {
          console.error('批量删除信源失败:', error)
          this.$message.error('批量删除信源失败')
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb-link {
  color: #409EFF;
  cursor: pointer;
  text-decoration: none;
}

.breadcrumb-link:hover {
  color: #66b1ff;
}

.title-card {
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E4E7ED;
  padding-bottom: 20px;
}

.group-name {
  font-size: 18px;
  font-weight: bold;
}

.search-section {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: nowrap;
}

.search-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-item span {
  margin-right: 8px;
}

.search-item .el-input,
.search-item .el-select {
  width: 180px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.group-title {
  color: #1677ff;
  font-weight: 600;
}

.operations {
  display: flex;
  gap: 10px;
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.danger-text {
  color: #F56C6C;
}
</style>
