<template>
  <div class="token-manager-container">
    <div class="header-section">
      <h2 class="page-title">Token 获取页面 (管理员专属)</h2>
      <p class="page-description">
        请在下方内嵌页面中完成登录操作。系统将自动拦截并存储Token。
      </p>
    </div>

    <div class="main-content">
      <div class="iframe-section">
        <iframe
         ref="loginIframe"
          :key="iframeKey"
          :src="iframeSrc"
          class="login-iframe"
          frameborder="0"
          @load="onIframeLoad"
        ></iframe>
      </div>

      <div class="status-panel">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>实时状态面板</span>
          </div>

          <!-- 状态显示 (已简化) -->
          <el-alert
            :title="statusTitle"
            :type="statusType"
            :description="statusDescription"
            :closable="false"
            show-icon
            class="status-alert"
          ></el-alert>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button
              type="primary"
              icon="el-icon-refresh"
              @click="handleManualGetToken"
            >
              获取token
            </el-button>
            <el-button
              icon="el-icon-refresh-left"
              @click="reloadIframe"
            >
              重载登录页
            </el-button>
          </div>

          <!-- Token详细信息部分已根据您的要求移除 -->

        </el-card>
      </div>
    </div>
  </div>
</template>

<script>

import { storeToken } from '@/api/yqComprehensiveSearch';

export default {
  name: "TokenManagerSimple",
  data() {
    return {
      iframeSrc: "/xy",
      iframeKey: Date.now(),
      status: 'CHECKING', 
      isLoading: false, 
      lastSuccessTime: null, // [新增] 记录最后一次成功获取Token的时间
    };
  },
  computed: {
    statusTitle() {
      const titles = { CHECKING: "正在检查状态...", VALID: "Token 有效", INVALID: "Token 无效或未获取", };
      return titles[this.status];
    },
    statusType() {
      const types = { CHECKING: "info", VALID: "success", INVALID: "error", };
      return types[this.status];
    },
    // [修改] 简化描述文案，并加入“最后获取时间”
    statusDescription() {
      if (this.status === 'VALID') {
        return `系统已成功获取Token！最后获取时间: ${this.lastSuccessTime}`;
      }
      if (this.status === 'INVALID') {
        return "请在左侧页面中登录以获取新的Token。";
      }
      return ""; // CHECKING状态不需要描述
    }
  },
  mounted() {
    // 组件挂载时的初始化逻辑
  },
  beforeDestroy() {
    // 组件销毁时的清理逻辑
  },
  methods: {
    onIframeLoad() {
      console.log("Iframe 加载完成。开始尝试移除登录失效弹窗...");
      
      const iframe = this.$refs.loginIframe;
      // 确保iframe和其内容文档都已加载
      if (!iframe || !iframe.contentDocument) {
        console.error("无法访问 iframe 内容。可能是跨域或加载失败。");
        return;
      }

      // 使用轮询来处理iframe内部JS动态生成弹窗的情况
      // 每100毫秒检查一次，最多尝试50次（即5秒）
      let attempts = 0;
      const maxAttempts = 50; 
      const removalInterval = setInterval(() => {
        attempts++;
        // 获取iframe内部的document对象
        const iframeDoc = iframe.contentDocument;

        // 尝试找到遮罩层和弹窗外层容器
        const mask = iframeDoc.querySelector('.ant-modal-mask');
        const wrap = iframeDoc.querySelector('.ant-modal-wrap');

        // 如果找到了，就移除它们
        if (mask && wrap) {
          console.log("已找到弹窗元素，正在移除...");
          mask.remove();
          wrap.remove();
          
          // 成功移除后，清除定时器，停止轮询
          clearInterval(removalInterval);
          console.log("弹窗已成功移除。");
        } else if (attempts >= maxAttempts) {
          // 如果超过最大尝试次数还没找到，也停止轮询，避免无限循环
          console.warn(`在 ${maxAttempts * 100}ms 内未找到弹窗元素，停止尝试。`);
          clearInterval(removalInterval);
        }
      }, 100); // 轮询间隔：100ms
    },
    reloadIframe() {
      this.status = 'CHECKING';
      this.lastSuccessTime = null; // 重载时清空时间
      this.iframeKey = Date.now();
      this.$message.info("正在重新加载登录页面...");
    },
     /**
     * @description 从 iframe 的 localStorage 中获取 token
     * @returns {string|null} 返回获取到的 token，如果未找到则返回 null
     */
     getTokenFromIframe() {
      // 1. 通过 ref 获取 iframe 元素
      const iframe = this.$refs.loginIframe;
      if (!iframe) {
        console.error("无法找到 iframe 元素。");
        return null;
      }

      try {
        // 2. 访问 iframe 的 contentWindow，这是它的 window 对象
        const iframeWindow = iframe.contentWindow;
        
        // 3. 访问其 localStorage 并获取 item
        //    请将 'x-token' 替换为实际的 key
        const token = iframeWindow.localStorage.getItem('token'); 
        
        if (token) {
          console.log("成功从 iframe 获取到 Token:", token);
          // 在这里你可以将 token 保存到你的 Vuex store 或父组件的 data 中
          // 例如: this.retrievedToken = token;
          this.$message.success("已成功从登录页捕获Token！");
          
          // 复用原 checkTokenStatus 的回调逻辑
          if (this.status !== 'VALID') {
            this.$message.success("Token已获取成功！");
            this.lastSuccessTime = new Date().toLocaleString();
          }
          this.status = 'VALID';

          // 新增：调用 storeToken 接口保存 token
          storeToken(token).then(() => {
            console.log('Token 已保存到后端');
          }).catch(err => {
            console.error('Token 保存失败', err);
            this.$message.error('Token 保存到后端失败');
          });
          
          return token;
        } else {
          console.warn("在 iframe 的 localStorage 中未找到 key 为 'x-token' 的项。");
          this.$message.error("获取token失败，请先登录");
          return null;
        }
      } catch (error) {
        // 尽管是同源，但某些特殊情况下（如沙箱模式）仍可能出错
        console.error("访问 iframe localStorage 时出错:", error);
        this.$message.error("访问iframe内容失败，可能是安全限制。");
        return null;
      }
    },
    
    // 你可以添加一个按钮来手动触发这个操作
    handleManualGetToken() {
        this.getTokenFromIframe();
    }
  },
};
</script>
<style scoped>
/* 样式与之前版本完全相同，故省略 */
.token-manager-container { padding: 24px; background-color: #f0f2f5; height: 100vh; display: flex; flex-direction: column; }
.header-section { margin-bottom: 20px; }
.page-title { font-size: 22px; font-weight: 600; color: #333; margin: 0 0 8px 0; }
.page-description { font-size: 14px; color: #666; }
.main-content { display: flex; flex-grow: 1; gap: 24px; min-height: 0; }
.iframe-section { flex: 3; display: flex; flex-direction: column; }
.login-iframe { width: 100%; height: 100%; border: 1px solid #dcdfe6; border-radius: 4px; }
.status-panel { flex: 1; min-width: 320px; }
.status-panel .el-card { height: 100%; display: flex; flex-direction: column; }
.status-panel .el-card ::v-deep .el-card__body { flex-grow: 1; display: flex; flex-direction: column; }
.status-alert { margin-bottom: 20px; }
.action-buttons { display: flex; flex-direction: column; gap: 12px; margin-bottom: 24px; }
.action-buttons .el-button { width: 100%; margin-left: 0 !important; }
.token-details { margin-top: auto; padding: 16px; background-color: #fafafa; border-radius: 4px; font-size: 14px; color: #333; }
.token-details h4 { margin: 0 0 12px 0; font-size: 16px; font-weight: 600; }
.token-details p { margin: 0 0 8px 0; }
@media (max-width: 992px) {
  .main-content { flex-direction: column; }
  .iframe-section { min-height: 500px; }
}
</style>