import request from '@/utils/request'

// ======================= 词库管理通用接口 =======================

/**
 * 获取系统中的所有词库分类
 * @returns {Promise} 返回词库分类列表
 */
export function getDictionaryCategoryOptions() {
  return request({
    url: '/system/dictionaryCategory/options',
    method: 'get'
  })
}

// ======================= 词汇相关接口 =======================

/**
 * 新增词汇（支持批量，多个词用空格分隔）
 * @param {object} data - 请求体
 * @param {number} data.categoryId - 词库分类ID
 * @param {string} data.wordText - 词汇文本
 * @param {number[]} [data.groupIds] - (可选) 词汇分组ID集合，用于排除词库
 * @returns {Promise}
 */
export function batchAddDictionaryWord(data) {
  return request({
    url: '/system/dictionaryWord/batchAdd',
    method: 'post',
    data: data
  })
}

/**
 * 查询词库下的词汇（支持分页和关键词搜索）
 * @param {object} query - 查询参数
 * @param {number} query.pageNum - 页码
 * @param {number} query.pageSize - 每页的个数
 * @param {number} query.categoryId - 词库分类ID
 * @param {string} [query.keyword] - (可选) 搜索关键词
 * @param {string} [query.groupIdList] - (可选) 词汇分组ID列表，逗号分隔，用于排除词库
 * @returns {Promise} 返回词汇列表
 */
export function searchDictionaryWords(query) {
  return request({
    url: '/system/dictionaryWord/search',
    method: 'get',
    params: query
  })
}

/**
 * 获取词汇详情
 * @param {number|string} wordId - 词汇ID
 * @returns {Promise} 返回词汇详情
 */
export function getDictionaryWordDetail(wordId) {
  return request({
    url: `/system/dictionaryWord/${wordId}`,
    method: 'get'
  })
}

/**
 * 修改词汇
 * @param {object} data - 请求体
 * @param {number} data.wordId - 词汇ID
 * @param {string} data.wordText - 新的词汇文本
 * @returns {Promise}
 */
export function updateDictionaryWord(data) {
  return request({
    url: '/system/dictionaryWord',
    method: 'put',
    data: data
  })
}

/**
 * 删除词库下的词汇（支持批量）
 * @param {string} wordIds - 词汇ID，多个ID用逗号分隔
 * @returns {Promise}
 */
export function deleteDictionaryWords(wordIds) {
  return request({
    url: `/system/dictionaryWord/${wordIds}`,
    method: 'delete'
  })
}

/**
 * 批量设置词汇的分组关系
 * @param {object} data - 请求体
 * @param {number[]} data.wordIds - 词汇ID数组
 * @param {number[]} data.groupIds - 目标词汇分组ID数组
 * @param {string} data.operation - 操作类型，固定为 "set"
 * @returns {Promise}
 */
export function batchSetWordGroups(data) {
  return request({
    url: '/system/dictionaryWord/batchSetGroups',
    method: 'post',
    data: data
  })
}

// ======================= 词汇分组相关接口（主要用于排除词库） =======================

/**
 * 新增词汇分组
 * @param {object} data - 请求体
 * @param {number} data.categoryId - 词库分类ID
 * @param {string} data.groupName - 分组名称
 * @returns {Promise}
 */
export function addDictionaryGroup(data) {
  return request({
    url: '/system/dictionaryGroup',
    method: 'post',
    data: data
  })
}

/**
 * 根据分类查询词汇分组列表（不分页）
 * @param {number|string} categoryId - 词库分类ID
 * @returns {Promise} 返回分组列表
 */
export function getDictionaryGroupsByCategory(categoryId) {
  return request({
    url: `/system/dictionaryGroup/listByCategory/${categoryId}`,
    method: 'get'
  })
}

/**
 * 查询词汇分组列表（分页）
 * @param {object} query - 查询参数
 * @param {number} query.categoryId - 词库分类ID
 * @param {number} query.pageSize - 每页的个数
 * @param {number} query.pageNum - 页码
 * @returns {Promise} 返回分组列表
 */
export function listDictionaryGroups(query) {
  return request({
    url: '/system/dictionaryGroup/list',
    method: 'get',
    params: query
  })
}

/**
 * 修改词汇分组
 * @param {object} data - 请求体
 * @param {number} data.groupId - 分组ID
 * @param {number} data.categoryId - 词库分类ID
 * @param {string} data.groupName - 新的分组名称
 * @returns {Promise}
 */
export function updateDictionaryGroup(data) {
  return request({
    url: '/system/dictionaryGroup',
    method: 'put',
    data: data
  })
}

/**
 * 删除词汇分组（支持批量）
 * @param {string} groupIds - 分组ID，多个ID用逗号分隔
 * @returns {Promise}
 */
export function deleteDictionaryGroups(groupIds) {
  return request({
    url: `/system/dictionaryGroup/${groupIds}`,
    method: 'delete'
  })
}

// ======================= 导入导出相关接口 =======================

/**
 * 下载词汇导入模板
 * @returns {Promise} 返回文件流
 */
export function downloadImportTemplate() {
  return request({
    url: '/system/dictionaryWord/importTemplate',
    method: 'post'
    // 注意：request工具方法可能需要配置 responseType: 'blob' 来处理文件下载
  })
}

/**
 * 导出词汇数据
 * @param {object} data - 请求体
 * @param {number[]} data.categoryIds - 词库分类ID数组
 * @param {number[]} data.groupIds - 词汇分组ID数组
 * @returns {Promise} 返回文件流
 */
export function exportDictionaryData(data) {
  return request({
    url: '/system/dictionaryWord/export',
    method: 'post',
    data: data
    // 注意：request工具方法可能需要配置 responseType: 'blob' 来处理文件下载
  })
}

/**
 * 导入词汇数据
 * @param {FormData} data - 表单数据，应包含文件本身
 * @param {boolean} updateSupport - 若系统已存在，是否覆盖数据
 * @returns {Promise}
 */
export function importDictionaryData(data, updateSupport) {
  return request({
    url: '/system/dictionaryWord/importData',
    method: 'post',
    params: { updateSupport },
    data: data
    // 注意：当data为FormData时，request工具应自动设置正确的Content-Type
  })
}