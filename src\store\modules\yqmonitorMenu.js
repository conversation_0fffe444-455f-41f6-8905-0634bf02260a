import { listMenu } from "@/api/system/menu";

const state = {
  menuList: [],
  currentNode: null, // 当前被操作的节点
  lastQueryParams: {}, // 最近一次列表查询参数
  isOpeningFromTag: false, // 新增：是否从标签打开筛选表单
  cachedTimeRangeType: null, // 新增：缓存预设的时间范围类型 (work, 24h, 7d)
  searchText: "", // 新增：搜索文本
  currentBusinessType: "yqmonitor-business",
};

const mutations = {
  SET_MENU_LIST(state, list) {
    state.menuList = list;
  },
  SET_CURRENT_NODE(state, node) {
    state.currentNode = node;
  },
  SET_LAST_QUERY_PARAMS(state, params) {
    state.lastQueryParams = params;
  },
  SET_OPENING_FROM_TAG(state, value) {
    // 新增：设置是否从标签打开
    state.isOpeningFromTag = value;
  },
  SET_CACHED_TIME_RANGE_TYPE(state, type) {
    // 新增：设置缓存的时间范围类型
    state.cachedTimeRangeType = type;
  },
  SET_SEARCH_TEXT(state, text) {
    // 新增：设置搜索文本
    state.searchText = text;
  },
  SET_CURRENT_BUSINESS_TYPE(state, type) {
    state.currentBusinessType = type;
  },
};

const actions = {
  async fetchMenuList({ commit }) {
    try {
      const res = await listMenu();

      console.log("来自服务端的原始菜单数据:", res);

      // 从 state 中获取当前的业务标识符
      const businessType = state.currentBusinessType;
      // 过滤出 yqmonitor-business 相关的菜单
      const yqmonitorMenus = res.data.filter(
        (item) => item.path === businessType
      );

      /**
       * 高效构建树形结构
       * @param {Array} items 扁平的节点列表
       * @returns {Array} 树形结构的节点列表
       */
      const buildTree = (items) => {
        // 1. 创建一个Map，用于通过menuId快速查找节点，提高效率
        const itemMap = new Map();
        // 2. 创建一个结果数组，用于存放根节点
        const tree = [];

        // 第一次遍历：格式化数据，并将其存入Map
        for (const item of items) {
          const menuItem = {
            id: item.menuId,
            name: item.menuName,
            type: item.menuName.includes("本地监测")
              ? "module"
              : item.menuType === "C"
              ? "topic"
              : "category",
            parentId: item.parentId,
            status: item.showType === "2" ? 1 : 0,
            planId: item.planId,
            children: [], // 每个节点都初始化一个children数组
          };
          itemMap.set(menuItem.id, menuItem);
        }

        // 第二次遍历：构建父子关系
        for (const item of itemMap.values()) {
          // 尝试在Map中查找当前节点的父节点
          const parent = itemMap.get(item.parentId);

          if (parent) {
            // 如果找到了父节点，则将当前节点添加到父节点的children数组中
            parent.children.push(item);
          } else {
            // 如果没有找到父节点，说明这是一个根节点，将其添加到结果数组中
            tree.push(item);
          }
        }

        return tree;
      };

      const menuList = buildTree(yqmonitorMenus);
      commit("SET_MENU_LIST", menuList);
      console.log("菜单数据", menuList);
    } catch (e) {
      // 可根据需要处理错误
      // eslint-disable-next-line no-console
      console.error("获取菜单数据失败", e);
    }
  },
  setCurrentNode({ commit }, node) {
    commit("SET_CURRENT_NODE", node);
  },
  setOpeningFromTag({ commit }, value) {
    // 新增：设置是否从标签打开
    commit("SET_OPENING_FROM_TAG", value);
  },
};

const getters = {
  menuList: (state) => state.menuList,
  currentNode: (state) => state.currentNode,
  lastQueryParams: (state) => state.lastQueryParams,
  isOpeningFromTag: (state) => state.isOpeningFromTag,
  cachedTimeRangeType: (state) => state.cachedTimeRangeType,
  searchText: (state) => state.searchText, // 新增
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
