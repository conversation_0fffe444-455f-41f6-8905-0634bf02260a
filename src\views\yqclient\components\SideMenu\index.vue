<template>
    <el-aside width="240px" class="side-menu-container">
      <div class="menu-header">
        <h4>客户机构列表</h4>
        <el-switch
          v-model="localIncludeSubInstitutions"
          active-text="包含下级"
          inactive-text="仅本级"
          @change="onSwitchChange"
        />
      </div>
      <el-scrollbar class="menu-scrollbar">
        <el-tree
          v-if="institutionTree.length > 0"
          ref="tree"
          :data="institutionTree"
          :props="defaultProps"
          node-key="id"
          highlight-current
          :default-expand-all="true"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        />
        <el-empty v-else description="暂无机构数据" />
      </el-scrollbar>
    </el-aside>
  </template>
  
  <script>
  import { mapState, mapActions } from 'vuex'
  
  export default {
    name: 'ClientSideMenu',
    data() {
      return {
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        localIncludeSubInstitutions: true
      }
    },
    computed: {
      ...mapState('yqclientInstitution', ['institutionTree', 'includeSubInstitutions'])
    },
    watch: {
      includeSubInstitutions(newVal) {
        this.localIncludeSubInstitutions = newVal
      }
    },
    async created() {
      await this.fetchInstitutionTree()
      // 默认选中第一个节点
      if (this.institutionTree && this.institutionTree.length > 0) {
        const firstNode = this.institutionTree[0]
        this.setCurrentNode(firstNode)
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(firstNode.id)
          }
        })
      }
    },
    methods: {
      ...mapActions('yqclientInstitution', ['fetchInstitutionTree', 'setCurrentNode', 'setIncludeSubInstitutions']),
      handleNodeClick(data) {
        this.setCurrentNode(data)
      },
      onSwitchChange(value) {
        this.setIncludeSubInstitutions(value)
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .side-menu-container {
    background-color: #2A3143;
    color: #fff;
    border-right: 1px solid #e6e6e6;
    display: flex;
    flex-direction: column;
    height: 100%;
  
    .menu-header {
      flex-shrink: 0;
      padding: 15px;
      border-bottom: 1px solid #4a5879;
      h4 {
        margin: 0 0 15px;
        text-align: center;
        font-size: 16px;
      }
      .el-switch {
        display: flex;
        justify-content: center;
      }
      ::v-deep .el-switch__label {
          color: #fff;
      }
      ::v-deep .el-switch__label.is-active {
          color: #409EFF;
      }
    }
  
    .menu-scrollbar {
      height: 100%;
      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  
    ::v-deep .el-tree {
      background-color: transparent;
      color: inherit;
      padding: 10px 0;
      .el-tree-node__content {
          height: 40px;
          &:hover {
              background-color: #3a4561;
          }
      }
      .el-tree-node:focus > .el-tree-node__content {
          background-color: #3a4561;
      }
      .el-tree-node.is-current > .el-tree-node__content {
          background-color: #409EFF !important;
      }
    }
  }
  </style>