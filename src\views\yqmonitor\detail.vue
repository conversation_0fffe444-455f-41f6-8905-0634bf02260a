<template>
  <div class="detail-layout">
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="warning-button-container">
        <el-button size="medium" type="primary" class="warning-button" @click="handleWarning">预警</el-button>
      </div>
      <div class="article-title" @click="goToOriginalUrl" v-html="highlightHitWords(getFormattedTitle(localItem))" />
      <div class="header-row">
        <el-avatar v-if="localItem.avatar" :src="localItem.avatar" size="medium" />
        <span class="author">{{ localItem.author }}</span>
        <span class="time">{{ localItem.publishTime }}</span>
        <el-tag size="mini" type="info" class="platform">{{ localItem.source || localItem.platform }}</el-tag>
        地域:<span v-if="localItem.location" class="location" v-html="highlightHitWords(localItem.location)" />
      </div>
      <div class="content-block">
        <div class="content-title">正文内容：</div>
        <div class="content-text"
          v-html="highlightHitWords(localItem.contentObj && localItem.contentObj.rawContentHtml ? localItem.contentObj.rawContentHtml : '')" />
      </div>
      <div v-if="localItem.contentObj && localItem.contentObj.rawOcr" class="content-block">
        <div class="content-title ocr-title">OCR：</div>
        <div class="content-text" v-html="highlightHitWords(localItem.contentObj.rawOcr)" />
      </div>
      <div v-if="localItem.contentObj && localItem.contentObj.rawAsr" class="content-block">
        <div class="content-title asr-title">ASR：</div>
        <div class="content-text" v-html="highlightHitWords(localItem.contentObj.rawAsr)" />
      </div>
      <!-- AI舆情特征详情 -->
      <div v-if="aiFeatureHitWords.length" class="ai-feature-details">
        <div v-for="(feature, index) in aiFeatureHitWords" :key="feature.key" class="feature-item">
          <span class="feature-reason">{{ feature.label }}:</span>
          <span class="feature-hitword">{{ feature.value.join(', ') }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧信息卡 -->
    <div class="side-info">
      <div class="info-card">
        <div class="info-row">
          <b>原文链接：</b>
          <a :href="localItem.url" target="_blank">{{ localItem.url }}</a>
          <el-button size="mini" @click="copy(localItem.url)">复制</el-button>
        </div>
        <div v-if="wyylHitWords.length" class="info-row">
          <b>命中词：</b>
          <el-tooltip v-if="wyylHitWords.join(',').length > 8" :content="wyylHitWords.join(',')" placement="top">
            <span class="hitword-content">{{ wyylHitWords.join(',').slice(0, 8) + '...' }}</span>
          </el-tooltip>
          <span v-else class="hitword-content">{{ wyylHitWords.join(',') }}</span>
        </div>
        <div class="info-row">
          <b>万有引力倾向性：</b>
          <el-tag :type="sentimentTypeColor">{{ sentimentLabel }}</el-tag>
        </div>
        <div class="info-row">
          <b>AI舆情倾向性：</b>
          <el-tag v-if="aiPublicOpinionType">{{ aiPublicOpinionType }}</el-tag>
        </div>
        <div class="info-row"><b>浏览量：</b>{{ localItem.views }}</div>
        <div class="info-row"><b>评论数：</b>{{ localItem.comments }}</div>
        <div class="info-row"><b>点赞数：</b>{{ localItem.likes }}</div>
        <div class="info-row"><b>转发数：</b>{{ localItem.reposts }}</div>
        <div class="info-row"><b>收藏数：</b>{{ localItem.collects }}</div>
      </div>
      <a :href="localItem.authorUrl" target="_blank" class="user-card-link">
        <div class="user-card">
          <el-avatar v-if="localItem.avatar" :src="localItem.avatar" size="large" />
          <div class="user-info">
            <div class="user-name">{{ localItem.author }}</div>
            <div>粉丝：{{ localItem.fansCount }}</div>
            <div>账号ID：{{ localItem.authorId }}</div>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
import { getWyylHitWords, getAiFeatureHitWords, getInstitutionAliasHitWords, mapItem, getSensitiveHitWords } from './components/MainContent/articleUtil'
import { getPublicSentimentById } from '@/api/yqmonitor'
import { highlightRichTextMultiple } from '@/utils/yqmonitorTool'

export default {
  name: 'ArticleDetail',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localItem: {},
      sentiment: '',
      sentimentTypeColor: '',
      sentimentLabel: '',
      aiPublicOpinionType: ''
    }
  },
  computed: {
    wyylHitWords() {
      return getWyylHitWords(this.localItem.hitWordsInfo)
    },
    aiFeatureHitWords() {
      return getAiFeatureHitWords(this.localItem.hitWordsInfo, true)
    }
  },
  watch: {
    'dict.type': {
      handler(newVal) {
        if (
          newVal &&
          Array.isArray(newVal.filter_sentiment) && newVal.filter_sentiment.length &&
          Array.isArray(newVal.ai_public_opinion_type) && newVal.ai_public_opinion_type.length &&
          Array.isArray(newVal.content_publish_type) && newVal.content_publish_type.length &&
          Array.isArray(newVal.negative_public_opinion_tag) && newVal.negative_public_opinion_tag.length
        ) {
          // 如果是通过props传入的item，直接使用
          if (this.item && Object.keys(this.item).length > 0) {
            this.initItemFromProps()
          } else {
            // 否则从API获取
            this.initItemFromAPI()
          }
        }
      },
      immediate: true,
      deep: true
    },
    // 监听props中的item变化
    item: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initItemFromProps()
        }
      },
      immediate: true,
      deep: true
    }
  },
  dicts: ['filter_sentiment', 'ai_public_opinion_type', 'content_publish_type', 'negative_public_opinion_tag'],
  methods: {
    initItemFromProps() {
      this.localItem = this.item
      this.updateSentimentInfo()
    },
    initItemFromAPI() {
      const id = this.$route.params.uniqueId
      if (!id) {
        // this.$message.error('未获取到舆情ID');
        return
      }
      getPublicSentimentById(id).then(response => {
        console.log('getPublicSentimentById:', response)
        if (response.code === 200) {
          this.localItem = mapItem(response.data)
          // 将字典数据添加到item中
          this.localItem.dicts = this.dict.type
          this.updateSentimentInfo()
        } else {
          this.$message.error(response.msg || '获取舆情详情失败')
        }
      }).catch(error => {
        console.error('获取舆情详情失败:', error)
        this.$message.error('获取舆情详情失败')
      })
    },
    updateSentimentInfo() {
      this.sentiment = this.localItem.sentiment || ''
      // 倾向性label
      const sentimentDict = this.localItem.dicts?.filter_sentiment || []
      const sentimentObj = sentimentDict.find(d => String(d.value) === String(this.sentiment))
      this.sentimentLabel = sentimentObj ? sentimentObj.label : this.sentiment
      // 颜色
      const colors = { '中性': 'info', '非敏感': 'success', '敏感': 'danger' }
      this.sentimentTypeColor = colors[this.sentimentLabel] || 'info'
      // AI舆情倾向性label
      this.aiPublicOpinionType = ''
      if (this.localItem.dmxTagInfo && this.localItem.dmxTagInfo.label) {
        const aiTypeDict = this.localItem.dicts?.ai_public_opinion_type || []
        const aiTypeObj = aiTypeDict.find(d => String(d.value) === String(this.localItem.dmxTagInfo.label))
        this.aiPublicOpinionType = aiTypeObj ? aiTypeObj.label : this.localItem.dmxTagInfo.label
      }
    },
    copy(text) {
      navigator.clipboard.writeText(text)
      this.$message.success('已复制到剪切板')
    },
    highlightHitWordsOld(text) {
      if (!text) return text

      // 1. 获取敏感词
      const sensitiveWords = getSensitiveHitWords(this.localItem.hitWordsInfo)

      // 2. 获取其他所有命中词
      let otherWords = []
      otherWords = otherWords.concat(getWyylHitWords(this.localItem.hitWordsInfo))
      const aiFeatureList = getAiFeatureHitWords(this.localItem.hitWordsInfo)
      aiFeatureList.forEach(feature => {
        if (Array.isArray(feature.value)) {
          otherWords = otherWords.concat(feature.value)
        }
      })
      otherWords = otherWords.concat(getInstitutionAliasHitWords(this.localItem.hitWordsInfo))

      // 3. 按长度排序，优先匹配长词
      sensitiveWords.sort((a, b) => b.length - a.length)
      otherWords.sort((a, b) => b.length - a.length)

      let html = text

      // 4. 应用常规命中词高亮
      otherWords.forEach(word => {
        if (!word) return
        const reg = new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        html = html.replace(reg, `<span class="hitword-highlight">${word}</span>`)
      })

      // 5. 应用敏感词高亮（玫红色），后应用以覆盖（虽然不重叠，但逻辑上更安全）
      sensitiveWords.forEach(word => {
        if (!word) return
        const reg = new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        html = html.replace(reg, `<span class="sensitive-highlight">${word}</span>`)
      })

      html = html.replace(/\n/g, '<br/>')
      return html
    },
    highlightHitWords(text) {
      if (!text) return text;

      // 1. & 2. 获取所有关键词，并附加上它们的类名
      const sensitiveWords = getSensitiveHitWords(this.localItem.hitWordsInfo).map(word => ({
        word: word,
        className: 'sensitive-highlight'
      }));

      let otherWordsPayload = [];
      const wyylWords = getWyylHitWords(this.localItem.hitWordsInfo);
      const institutionAliasWords = getInstitutionAliasHitWords(this.localItem.hitWordsInfo);
      const aiFeatureList = getAiFeatureHitWords(this.localItem.hitWordsInfo);
      let aiWords = [];
      aiFeatureList.forEach(feature => {
        if (Array.isArray(feature.value)) {
          aiWords = aiWords.concat(feature.value);
        }
      });

      otherWordsPayload = [...wyylWords, ...institutionAliasWords, ...aiWords].map(word => ({
        word: word,
        className: 'hitword-highlight'
      }));

      // 将所有词合并到一个数组中
      const allWordsToHighlight = [...sensitiveWords, ...otherWordsPayload];

      // 3. 调用新的、安全的富文本高亮函数
      // 注意：长词优先的排序逻辑已在 highlightRichTextMultiple 函数内部处理
      let html = highlightRichTextMultiple(text, allWordsToHighlight);

      return html;
    },
    getFeatureReason(reason) {
      const tagDict = (this.localItem.dicts && this.localItem.dicts.negative_public_opinion_tag) || []
      const reasonDict = tagDict.find(item => String(item.value) === String(reason))
      return reasonDict ? reasonDict.label : reason
    },
    getFormattedTitle(item) {
      if (!item.titleObj) return ''

      const title = item.titleObj.rawTitle || item.titleObj.title

      // 通过字典获取文章类型
      const type = (this.localItem.dicts && this.localItem.dicts.content_publish_type)?.find(
        item => String(item.value) === String(this.localItem.isOriginal)
      )

      // 如果是非原创（转发/评论），添加类型前缀
      if (type && type.label && this.localItem.isOriginal !== 1) {
        return `${type.label}：${title}`
      }

      return title
    },
    goToOriginalUrl() {
      if (this.localItem.url) {
        window.open(this.localItem.url, '_blank')
      }
    },
    handleWarning() {
      if (this.localItem.uniqueId) {
        window.open(`/yqwarning/sender?warningId=${this.localItem.uniqueId}`, '_blank')
      } else {
        this.$message.warning('未获取到uniqueId，无法预警')
      }
    }
  }
}
</script>

<style scoped>
.detail-layout {
  display: flex;
  gap: 32px;
  padding: 32px 0;
}

.main-content {
  flex: 1;
  background: #fff;
  border-radius: 6px;
  padding: 24px 32px;
  min-width: 0;
  position: relative;
}

.article-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 18px;
  line-height: 1.3;
  cursor: pointer;
  transition: color 0.3s;
  max-width: 95%;
}

.article-title:hover {
  color: #409EFF;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 18px;
  font-size: 16px;
}

.author {
  font-weight: bold;
  margin-right: 8px;
}

.time {
  color: #888;
  margin-right: 8px;
}

.platform {
  margin-right: 8px;
}

.location {
  color: #888;
}

.content-block {
  margin-bottom: 18px;
}

.content-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.ocr-title {
  color: #e67e22;
}

.asr-title {
  color: #16a085;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 16px;
  line-height: 1.7;
}

.side-info {
  width: 340px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  background: #fff;
  border-radius: 6px;
  padding: 18px 20px;
  margin-bottom: 12px;
  font-size: 15px;
}

.info-row {
  margin-bottom: 12px;
  word-break: break-all;
}

.user-card {
  background: #fff;
  border-radius: 6px;
  padding: 18px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  font-size: 15px;
  word-break: break-all;
}

.user-name {
  font-weight: bold;
  margin-bottom: 6px;
}

.ai-feature-details {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.feature-reason {
  color: #303133;
  font-weight: 500;
  margin-right: 8px;
  flex-shrink: 0;
}

.feature-hitword {
  color: #f10a0a;
  word-break: break-all;
}

.warning-button-container {
  position: absolute;
  right: 32px;
  top: 32px;
}

.warning-button {
  background: #cce2ff;
  color: #e4393c;
  font-weight: bold;
  border-radius: 4px;
  padding: 6px 12px;
  border: none;
  transition: background 0.2s;

  &:hover {
    background: #b3d4fc;
    color: #d32f2f;
  }
}
</style>

<style>
.hitword-highlight {
  color: #e4393c;
  background: #fffbe6;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
  margin-right: 4px;
}

/* 新增：敏感词的专属玫红色高亮样式 */
.sensitive-highlight {
  color: #C71585;
  /* 玫红色 */
  background: #fffbe6;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
  margin-right: 4px;
}
</style>
