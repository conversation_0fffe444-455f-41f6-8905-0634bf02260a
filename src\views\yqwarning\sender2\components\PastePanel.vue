<template>
  <div class="card paste-panel" style="padding: 20px; height: 100%; display: flex; flex-direction: column;">
    <el-form label-position="top" style="height: 100%; display: flex; flex-direction: column;">
      <el-form-item
        label="粘贴内容以快速填充"
        style="flex-grow: 1; margin-bottom: 0; display: flex; flex-direction: column;"
      >
        <el-input
          v-show="!isCollapsed"
          v-model="localContent"
          type="textarea"
          :rows="20"
          placeholder="请在此处粘贴标准格式的内容（支持标准发送格式和来自其他系统的格式），系统将自动解析并填充右侧表单。"
          class="fill-textarea"
          @change="handleContentChange"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'PastePanel',
  props: {
    isCollapsed: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localContent: this.value
    }
  },
  watch: {
    value(newVal) {
      this.localContent = newVal
    },
    localContent(newVal) {
      this.$emit('input', newVal)
    }
  },
  methods: {
    handleContentChange() {
      this.$emit('paste-and-parse', this.localContent)
    }
  }
}
</script>

<style scoped lang="scss">
.paste-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

::v-deep .fill-textarea .el-textarea__inner {
  flex-grow: 1;
  min-height: 400px !important;
  resize: vertical;
}

::v-deep .el-form-item__content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

::v-deep .el-textarea {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

::v-deep .el-textarea__inner {
  flex-grow: 1;
}
</style>
