<template>
  <div class="app-wrapper">
    <!-- 顶部导航栏 -->
    <Topnav />
    <!-- 内容区域 -->
    <div class="content-wrap">
      <router-view />
    </div>
  </div>
</template>

<script>
import { Topnav } from './components'

export default {
  name: 'Layout',
  components: {
    Topnav
  },
  computed: {

  },
  mounted() {

  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100%;
  width: 100%;
  background-color: #F2F3F7;
  .content-wrap {
    margin-top: 50px;
    height: calc(100% - 50px);
  }
}
</style>
