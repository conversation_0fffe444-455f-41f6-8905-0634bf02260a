<template>
  <div class="app-container">
    <el-container>
      <side-menu />
      <main-content module-type="yqclient" />
    </el-container>
  </div>
</template>

<script>
import SideMenu from './components/SideMenu/index.vue'
import MainContent from '@/views/yqmonitor/components/MainContent/index.vue'

export default {
  name: 'YqclientIndex',
  components: {
    SideMenu,
    MainContent
  },
  mounted() {
    document.body.style.overflowY = 'hidden'
  },
  beforeDestroy() {
    document.body.style.overflowY = ''
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  background-color: #f5f7fa;
  padding: 0;
  .el-container {
    height: 100%;
  }
}
</style>