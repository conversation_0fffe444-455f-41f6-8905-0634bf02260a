<template>
  <el-drawer :title="isEdit ? '编辑专题' : '新增专题'" :visible="visible" direction="rtl" size="50%" :wrapper-closable="false"
    @update:visible="$emit('update:visible', $event)" @open="handleDrawerOpen">
    <div class="drawer-content" v-loading="loading">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="专题名称" prop="planName">
          <el-input v-model="formData.planName" placeholder="请输入专题名称" />
        </el-form-item>
        <el-form-item label="信源配置" required />
        <el-form-item>
          数据类型：
          <el-radio-group v-model="dataType" @change="handleDataTypeChange">
            <el-radio label="all">平台全量</el-radio>
            <el-radio label="direct">定向信源</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :prop="dataType === 'all' ? 'filterPlatform' : 'directIds'">
          <template v-if="dataType === 'all'">
            <span class="required-star">*</span> 信息来源：
            <el-checkbox-group v-model="formData.filterPlatform" size="small" @change="handlePlatformChange">
              <el-checkbox-button v-for="item in dict.type.content_platform" :key="'platform-' + item.value"
                :label="item.value">
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </template>
          <template v-else>
            <span class="required-star">*</span> 信息来源：
            <source-selector v-model="selectedDirectItems" :source-groups="sourceGroups" />
          </template>
        </el-form-item>
        <el-form-item label="排除的信源">
          <source-selector v-model="selectedExcludeItems" :source-groups="sourceGroups" button-text="添加排除项"
            title="添加排除项" placeholder="请点击右侧按钮添加排除项" />
        </el-form-item>
        <el-form-item label="关键词组合" prop="wordCombination">
          <el-input v-model="formData.wordCombination" type="textarea" placeholder="请输入关键词组合表达式" :rows="3" />
        </el-form-item>
        <el-form-item label="词距">
          <div class="word-distance-container">
            <el-switch v-model="formData.isWordDistanceEnabled" />
            <template v-if="formData.isWordDistanceEnabled">
              <el-input-number v-model="formData.wordDistance" :min="-1" :max="500" :controls="false"
                style="width: 100px;" />
              <span>按关键词先后顺序查询</span>
              <el-switch v-model="formData.inOrder" active-value="1" inactive-value="0" />
            </template>
          </div>
          <div class="el-form-item__tip">关键词之间的字距大于您的设定值时，信息将自动过滤不展示。(-1表示关闭词距)。</div>
        </el-form-item>
        <el-form-item label="排除词">
          <el-tag v-for="(tag, index) in formData.wordExcludeList" :key="index" closable class="exclude-tag"
            @close="handleRemoveTag(index)">
            {{ tag }}
          </el-tag>
          <el-input v-if="inputVisible" ref="tagInput" v-model="inputValue" class="tag-input" size="small"
            @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" />
          <el-button v-else class="button-new-tag" size="small" @click="showInput"> + 添加排除词 </el-button>
        </el-form-item>
        <el-form-item label="所属客户" prop="customerList">
          <treeselect v-model="formData.customerList" :options="institutionList" :multiple="true"
            :normalizer="normalizer" :flat="true" placeholder="请选择所属客户" :disable-branch-nodes="false" />
        </el-form-item>
        <el-form-item label="机构" prop="institutions">
          <treeselect v-model="formData.institutions" :options="institutionList" :multiple="true"
            :normalizer="normalizer" :flat="true" placeholder="请选择机构" :disable-branch-nodes="false" />
        </el-form-item>
        <el-form-item label="关注地域" prop="wordRegions">
          <treeselect v-model="formData.wordRegions" :options="regionList" :multiple="true"
            :normalizer="regionNormalizer" :flat="true" placeholder="请选择关注地域" :disable-branch-nodes="false" />
        </el-form-item>
        <el-form-item label="相似数据去重" prop="isDistinctSimilarId">
          <el-radio-group v-model="formData.isDistinctSimilarId">
            <el-radio v-for="item in dict.type.common_yes_no" :key="item.value" :label="item.value"> {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-divider>过滤条件</el-divider>
        <el-form-item label="倾向性" prop="filterSentiments">
          <el-checkbox-group v-model="formData.filterSentiments" size="small" @change="handleSentimentChange">
            <el-checkbox-button v-for="item in dict.type.filter_sentiment" :key="'sentiment-' + item.value"
              :label="item.value">
              {{ item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="作品发布类型" prop="filterOriginals">
          <el-checkbox-group v-model="formData.filterOriginals" size="small" @change="handleOriginalsChange">
            <el-checkbox-button v-for="item in dict.type.content_publish_type" :key="'original-' + item.value"
              :label="item.value"> {{ item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="账户认证" prop="filterVerifies">
          <el-checkbox-group v-model="formData.filterVerifies" size="small" @change="handleVerifiesChange">
            <el-checkbox-button v-for="item in dict.type.acct_type" :key="'verify-' + item.value" :label="item.value">
              {{
                item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="关注地域匹配" prop="filterHitRegion">
          <el-checkbox-group v-model="formData.filterHitRegion" size="small" :disabled="!hasValidWordRegions"
            @change="handleHitRegionChange">
            <el-checkbox-button v-for="item in dict.type.filter_hit_region" :key="'hit-region-' + item.value"
              :label="item.value"> {{ item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="信息级别" prop="filterSourceLevels">
          <el-checkbox-group v-model="formData.filterSourceLevels" size="small" @change="handleSourceLevelsChange">
            <el-checkbox-button v-for="item in dict.type.filter_source_level" :key="'source-level-' + item.value"
              :label="item.value"> {{ item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="关键词字段匹配" prop="filterHitFields">
          <el-checkbox-group v-model="formData.filterHitFields" size="small" @change="handleHitFieldsChange">
            <el-checkbox-button v-for="item in dict.type.filter_hit_field" :key="'hit-field-' + item.value"
              :label="item.value">
              {{ item.label }} </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">{{ isEdit ? '保存' : '确定' }}</el-button>
    </div>
  </el-drawer>
</template>
<script>
import { createPlan, getPlanDetail, updatePlan } from '@/api/yqmonitor/topic'
import { mapGetters, mapActions, mapState } from 'vuex'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cloneDeep } from 'lodash'
import SourceSelector from './SourceSelector.vue'
const findNode = (list, code) => {
  for (const node of list) {
    if (node.code === code) return node
    if (node.children) {
      const found = findNode(node.children, code)
      if (found) return found
    }
  }
  return null
}
export default {
  name: 'TopicDrawer',
  dicts: ['acct_type', 'filter_sentiment', 'content_publish_type', 'content_platform', 'filter_hit_region', 'common_yes_no', 'filter_source_level', 'filter_hit_field'],
  components: { Treeselect, SourceSelector },
  props: { visible: { type: Boolean, default: false } },
  data() {
    const validateDirectSource = (rule, value, callback) => {
      if (this.dataType === 'direct' && this.selectedDirectItems.length === 0) {
        callback(new Error('定向信源模式下，请至少选择一项信源'))
      } else {
        callback()
      }
    }
    return {
      dataType: 'all',
      formData: this.getInitialFormData(),
      rules: {
        planName: [{ required: true, message: '请输入专题名称', trigger: 'blur' }],
        wordCombination: [{ required: true, message: '请输入关键词组合', trigger: 'blur' }],
        directIds: [{ validator: validateDirectSource, trigger: 'change' }]
      },
      inputVisible: false,
      inputValue: '',
      loading: false,
      // 已选择的信源项，由 SourceSelector 组件通过 v-model 维护
      selectedDirectItems: [],
      selectedExcludeItems: []
    }
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['currentNode']),
    ...mapState('yqmonitorTopic', ['sourceGroups', 'institutionList', 'regionList']),
    isEdit() { return this.currentNode?.type === 'topic' && !!this.currentNode?.id },
    hasValidWordRegions() { return Array.isArray(this.formData.wordRegions) && this.formData.wordRegions.length > 0 }
  },
  watch: {
    selectedDirectItems() {
      if (this.dataType === 'direct') this.$refs.formRef.validateField('directIds')
    }
  },
  methods: {
    ...mapActions('yqmonitorMenu', ['fetchMenuList']),
    ...mapActions('yqmonitorTopic', ['fetchSourceGroups', 'fetchInstitutionTree', 'fetchRegionList']),
    getInitialFormData() {
      return {
        planName: '',
        directIds: [], // 提交时会根据 selectedItems 生成
        excludeDirectIds: [],
        directSourceDetailList: [],
        excludeDirectSourceDetailList: [],
        wordCombination: '',
        wordExcludeList: [],
        customerList: [],
        filterOriginals: this.getDefaultOriginalsValue(),
        filterSentiments: ['all'],
        filterSourceLevels: ['all'],
        filterVerifies: ['all'],
        filterPlatform: ['all'],
        institutions: [],
        wordRegions: [],
        filterHitRegion: ['all'],
        filterHitFields: ['title', 'content', 'ocr', 'asr'],
        isDistinctSimilarId: '1',
        isWordDistanceEnabled: false,
        wordDistance: -1,
        inOrder: '0'
      }
    },
    handleDrawerOpen() {
      this.loading = true
      this.fetchBaseData().then(() => {
        this.resetForm()
        if (this.isEdit) {
          return this.fetchPlanDetail()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    fetchBaseData() {
      return Promise.all([this.fetchSourceGroups(), this.fetchInstitutionTree(), this.fetchRegionList()])
    },
    async fetchPlanDetail() {
      if (!this.currentNode?.id) return
      try {
        const res = await getPlanDetail({ menuId: this.currentNode.id })
        if (res.code === 0) {
          const data = res.data
          this.formData.planName = data.plan_name;
          this.formData.wordCombination = data.word_combination || '';
          this.formData.wordExcludeList = data.word_exclude || [];
          this.formData.filterOriginals = data.filter_original?.length > 0 ? data.filter_original : ['all'];
          this.formData.filterSentiments = data.filter_sentiment?.length > 0 ? data.filter_sentiment : ['all'];
          this.formData.filterSourceLevels = data.filter_source_level?.length > 0 ? data.filter_source_level : ['all'];
          this.formData.filterVerifies = data.filter_verify?.length > 0 ? data.filter_verify : ['all'];
          this.formData.filterPlatform = data.filter_platform?.length > 0 ? data.filter_platform : ['all'];
          this.formData.customerList = data.customerList ? data.customerList.map(item => item.customer) : [];
          this.formData.institutions = data.institutions ? data.institutions.split(',') : [];
          this.formData.wordRegions = data.word_region ? data.word_region.map(item => Number(item.code)) : [];
          this.formData.isDistinctSimilarId = String(data.is_distinct_similar_id) || '1';
          this.formData.isWordDistanceEnabled = data.word_distance !== null && data.word_distance !== -1;
          this.formData.wordDistance = data.word_distance !== null && data.word_distance !== -1 ? data.word_distance : -1;
          this.formData.inOrder = data.word_distance !== null && data.word_distance !== -1 ? String(data.in_order) : '0';
          this.formData.filterHitRegion = data.filter_hit_region?.length > 0 ? data.filter_hit_region : ['all'];
          this.formData.filterHitFields = data.filter_hit_field?.length > 0 ? data.filter_hit_field : this.getInitialFormData().filterHitFields;
          if (data.direct_id?.length > 0 || data.directSourceDetailList?.length > 0) {
            this.dataType = 'direct'
          } else {
            this.dataType = 'all'
          }
          this.selectedDirectItems = this.formatDataForSelection(data.direct_id, data.directSourceDetailList)
          this.selectedExcludeItems = this.formatDataForSelection(data.exclude_direct_id, data.excludeDirectSourceDetailList)
        }
      } catch (e) {
        console.error('获取专题详情失败:', e)
      }
    },
    formatDataForSelection(groupIds, sourceList) {
      const groups = (groupIds || []).map(id => {
        const group = this.sourceGroups.find(g => g.value === id)
        return group ? { uniqueId: `group_${id}`, name: group.label, type: 'group', sourceData: { direct_id: id, direct_name: group.label } } : null
      }).filter(Boolean)
      const sources = (sourceList || []).map(source => ({
        uniqueId: `source_${source.platform}_${source.user_id || source.id}`,
        name: `${source.nickname} (${source.platform_name})`,
        type: 'source',
        sourceData: source
      }))
      return [...groups, ...sources]
    },
    resetForm() {
      this.formData = this.getInitialFormData()
      this.selectedDirectItems = []
      this.selectedExcludeItems = []
      this.$nextTick(() => { this.$refs.formRef?.resetFields() })
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        const submitData = cloneDeep(this.formData)
        submitData.directIds = this.selectedDirectItems.filter(i => i.type === 'group').map(i => i.sourceData.direct_id)
        submitData.directSourceDetailList = this.selectedDirectItems.filter(i => i.type === 'source').map(i => i.sourceData)
        submitData.excludeDirectIds = this.selectedExcludeItems.filter(i => i.type === 'group').map(i => i.sourceData.direct_id)
        submitData.excludeDirectSourceDetailList = this.selectedExcludeItems.filter(i => i.type === 'source').map(i => i.sourceData)
        submitData.parentMenuId = this.currentNode.id
        submitData.institutions = Array.isArray(this.formData.institutions) ? this.formData.institutions.join(',') : ''
        submitData.wordRegions = (this.formData.wordRegions || []).map(code => {
          const node = findNode(this.regionList, code)
          return node ? { code: node.code, name: node.keyword } : null
        }).filter(Boolean)
        if (submitData.isWordDistanceEnabled) {
          submitData.inOrder = parseInt(submitData.inOrder, 10)
        } else {
          submitData.wordDistance = -1
          submitData.inOrder = 0
        }
        delete submitData.isWordDistanceEnabled
        if (this.dataType === 'all') {
          submitData.directIds = []
          submitData.directSourceDetailList = []
        } else {
          submitData.filterPlatform = []
        }
        submitData.businessType = this.$store.state.yqmonitorMenu.currentBusinessType
        const action = this.isEdit ? updatePlan : createPlan
        if (this.isEdit) submitData.menuId = this.currentNode.id
        await action(submitData)
        this.$message.success(this.isEdit ? '更新专题成功' : '创建专题成功')
        this.fetchMenuList()
        this.$emit('update:visible', false)
      } catch (error) {
        if (error) console.error('提交失败:', error)
      }
    },
    handleDataTypeChange() {
      if (this.dataType === 'all') { this.selectedDirectItems = [] }
      else { this.formData.filterPlatform = ['all'] }
      this.$refs.formRef?.clearValidate(['filterPlatform', 'directIds'])
    },
    showInput() { this.inputVisible = true; this.$nextTick(() => { this.$refs.tagInput.$refs.input.focus() }) },
    handleInputConfirm() { if (this.inputValue) { this.formData.wordExcludeList.push(this.inputValue) } this.inputVisible = false; this.inputValue = '' },
    handleRemoveTag(index) { this.formData.wordExcludeList.splice(index, 1) },
    handleSentimentChange(value) { this.handleCheckboxGroupChange('filterSentiments', value) },
    handleOriginalsChange(value) { this.handleCheckboxGroupChange('filterOriginals', value) },
    handleVerifiesChange(value) { this.handleCheckboxGroupChange('filterVerifies', value) },
    handlePlatformChange(value) { this.handleCheckboxGroupChange('filterPlatform', value) },
    handleHitRegionChange(value) { this.handleCheckboxGroupChange('filterHitRegion', value) },
    handleSourceLevelsChange(value) { this.handleCheckboxGroupChange('filterSourceLevels', value) },
    handleHitFieldsChange(value) { this.handleCheckboxGroupChange('filterHitFields', value) },
    handleCheckboxGroupChange(field, value, allValue = 'all') {
      const lastSelected = value[value.length - 1];
      if (value.length > 1 && lastSelected === allValue) {
        this.formData[field] = [allValue]
      } else {
        const filtered = value.filter(item => item !== allValue);
        this.formData[field] = filtered.length > 0 ? filtered : [allValue]
      }
    },
    getDefaultOriginalsValue() {
      const list = this.dict?.type?.content_publish_type || [];
      const allItem = list.find(item => item.label === '全部');
      return allItem ? [allItem.value] : (list[0] ? [list[0].value] : [])
    },
    normalizer(node) { return { id: node.code, label: node.name, children: node.children } },
    regionNormalizer(node) { return { id: node.code, label: node.name, children: node.children } }
  }
}
</script>
<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  box-sizing: border-box;
}
.required-star {
  color: #f56c6c;
}
.exclude-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
.tag-input {
  width: 100px;
  margin-right: 10px;
  vertical-align: bottom;
}
.button-new-tag {
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.word-distance-container {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}
.el-form-item__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
}
:deep(.vue-treeselect) {
  width: 100%;
}
.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
  :deep(.el-checkbox-group) {
    display: inline-flex;
    gap: 10px;
  }
}
:deep(.el-checkbox-button__inner) {
  border-radius: 4px;
}
</style>
