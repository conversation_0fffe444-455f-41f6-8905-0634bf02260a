<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleModuleClick" />
    <div class="node-content">
      <svg-icon icon-class="all" class-name="custom-class" />
      <span class="node-label" :title="node.label" @click="handleModuleClick">{{ node.label }}</span>
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click="handlePlusClick">
        <i class="el-icon-plus" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="addCategory">添加分类</el-dropdown-item>
        <!-- <el-dropdown-item command="addTopic">添加专题</el-dropdown-item> -->
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import auth from '@/plugins/auth'

export default {
  name: 'ModuleNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handlePlusClick(event) {
      if (this.isTreeCurrentlyDragging) {
        event.stopPropagation()
        return
      }
      if (this.node.expanded) {
        event.stopPropagation()
        return
      }
    },
    handleModuleClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      if (this.node.expanded) {
        console.log('已经展开了不允许关闭')
        e && e.stopPropagation()
      }
      this.$emit('module-click', this.data)
    },
    addCategory() {
      if (!auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有添加分类的权限')
        return
      }
      const newCategory = {
        id: `category_${Date.now()}`,
        name: '新分类',
        type: 'category',
        parentId: this.data.id,
        children: []
      }

      if (!this.data.children) {
        this.$set(this.data, 'children', [])
      }
      this.data.children.push(newCategory)

      // 通知新添加的分类进入编辑状态
      this.$nextTick(() => {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
          ...newCategory,
          isEditing: true
        })
      })
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'addCategory') {
        this.addCategory()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;

    .node-content {
        display: flex;
        align-items: center;
        padding-left: 8px;
    }

    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }
}

.custom-class {
    font-size: 15px;
}

.el-dropdown-link {
    cursor: pointer;
    color: #fff;

    &:hover {
        color: #409EFF;
    }
}

.click-area {
    position: absolute;
    top: -3px;
    right: 26px;
    width: 70px;
    height: 50px;
    cursor: pointer;
}
</style>
