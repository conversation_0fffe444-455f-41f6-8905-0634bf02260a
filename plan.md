好的，收到您的需求和代码。这是一个非常清晰的优化任务，客户的需求也很明确。您提供的布局方案草图（图2）完美地诠释了客户的想法。

我们来分步实现这个优化方案。核心思路是将原本属于 `FilterTagManager.vue` 的一部分UI（筛选和刷新按钮）和逻辑提升到其父组件 `TopNav.vue` 中，并重新组织 `TopNav.vue` 的布局。

### 修改步骤

#### 第 1 步：调整 `FilterTagManager.vue`

这个组件以后将只负责标签的展示和管理，不再包含右侧的“筛选”和“刷新”按钮。

**1.1. 修改 `<template>` 部分:**

*   删除右侧的按钮组 `.right-action-buttons`。
*   调整外层容器 `.main-controls-wrapper` 的结构，现在它只包含标签区域。
*   修改 `.visible-tags-container` 的样式，允许其换行显示。

**1.2. 修改 `<script>` 部分:**

*   删除不再需要的 `props`: `currentRefreshMode`。
*   删除已经移走的按钮所对应的方法：`openFilterDialog` 和 `handleRefreshModeCommandFromDropdown`。
*   删除相关的 `watch` 和 `data` (如 `currentRefreshModeLocal`)。

**1.3. 修改 `<style>` 部分:**

*   修改 `.visible-tags-container` 的 CSS，将 `flex-wrap: nowrap;` 改为 `flex-wrap: wrap;`，并移除 `overflow-x: auto;`。为了保证两行的高度，我们可以给它一个 `min-height`。
*   删除 `.right-action-buttons` 的样式。
*   调整 `.main-controls-wrapper` 的样式，因为它的子元素变了。

---

#### 第 2 步：调整 `TopNav.vue`

这个组件将承载新的布局，整合标签区域、右侧按钮组和刷新提醒。

**2.1. 修改 `<template>` 部分:**

*   在 `FilterTagManager` 组件下方，创建一个新的 flex 容器，用于并列放置“左容器”（`FilterTagManager`）和“右容器”。
*   将从 `FilterTagManager.vue` 剪切过来的“筛选”和“手动刷新”按钮的 HTML 代码，粘贴到“右容器”中。
*   将原有的刷新提醒 `.new-info-tip` 从组件底部移动到“右容器”中，放在按钮组的下方。
*   根据客户需求，缩短 `.new-info-tip` 的提示文案。

**2.2. 修改 `<script>` 部分:**

*   添加处理“筛选”按钮点击事件的方法。这个方法需要调用 `FilterTagManager` 子组件的 `getSelectedTagId` 方法来获取当前选中的标签信息，然后再触发 `filterOpen` 事件。
*   原有的 `handleRefreshModeChange` 方法可以直接被刷新下拉菜单使用。

**2.3. 修改 `<style>` 部分:**

*   为新创建的 flex 容器和左右容器添加样式，实现图2所示的布局。
*   左容器（`FilterTagManager`）应该占据大部分空间 (`flex-grow: 1`)。
*   右容器需要设置合适的对齐方式和间距。
*   修改 `.new-info-tip` 的样式，去掉 `width: 100%`，使其适应新的父容器。

---

### 目标代码修改方案

请根据以下 diff 修改您的代码文件。

#### 针对 `src/views/yqmonitor/components/MainContent/FilterTagManager.vue` 的修改

```diff
--- a/src/views/yqmonitor/components/MainContent/FilterTagManager.vue
+++ b/src/views/yqmonitor/components/MainContent/FilterTagManager.vue
@@ -1,7 +1,7 @@
 <!-- FilterTagManager.vue -->
 <template>
   <div class="filter-tag-manager-container">
-    <div class="main-controls-wrapper">
+    <div class="tags-and-more-wrapper">
       <!-- 左侧：可见标签区 和 更多标签按钮 -->
       <div class="tags-and-more-on-same-line">
         <draggable
@@ -58,26 +58,6 @@
           class="more-tags-btn-placeholder-inline"
         />
       </div>
-      <!-- 右侧：筛选和刷新按钮组 -->
-      <div class="right-action-buttons">
-        <el-button size="small" icon="el-icon-search" @click="openFilterDialog">筛选</el-button>
-        <el-dropdown trigger="click" @command="handleRefreshModeCommandFromDropdown">
-          <el-button size="small" icon="el-icon-refresh">
-            {{ currentRefreshModeLocal === 'manual' ? '手动刷新' : '自动刷新' }}
-            <i class="el-icon-arrow-down el-icon--right" />
-          </el-button>
-          <el-dropdown-menu slot="dropdown">
-            <el-dropdown-item command="manual">手动刷新</el-dropdown-item>
-            <el-dropdown-item command="auto">自动刷新</el-dropdown-item>
-          </el-dropdown-menu>
-        </el-dropdown>
-      </div>
     </div>
   </div>
 </template>
@@ -93,10 +73,6 @@
       type: Array,
       default: () => []
     },
-    currentRefreshMode: {
-      type: String,
-      default: 'manual'
-    },
     maxVisibleTags: {
       type: Number,
       required: true
@@ -114,8 +90,7 @@
         swapThreshold: 0.65,
         preventOnFilter: false
       },
-      isMoreDropdownVisible: false,
-      currentRefreshModeLocal: this.currentRefreshMode
+      isMoreDropdownVisible: false
     }
   },
   watch: {
@@ -125,9 +100,6 @@
       },
       immediate: true,
       deep: true
-    },
-    currentRefreshMode(newVal) {
-      this.currentRefreshModeLocal = newVal
     }
   },
   methods: {
@@ -235,14 +207,6 @@
         })
       }
     },
-    openFilterDialog() {
-      const selectedTag = this.allTagsLocal.find(tag => tag.id === this.selectedTagId)
-      this.$emit('open-filter-drawer', selectedTag ? { editingTagId: selectedTag.id, paramJson: selectedTag.paramJson } : null)
-    },
-    handleRefreshModeCommandFromDropdown(command) {
-      this.currentRefreshModeLocal = command
-      this.$emit('refresh-mode-change', command)
-    },
     resetSelectedTagState() {
       this.selectedTagId = ''
     },
@@ -263,26 +227,22 @@
   width: 100%;
   padding: 8px 0;
   /* 给上下一点间距 */
 }
-.main-controls-wrapper {
-  display: flex;
-  justify-content: space-between;
-  align-items: center;
-  gap: 12px;
+.tags-and-more-wrapper {
   width: 100%;
 }
 .tags-and-more-on-same-line {
   display: flex;
-  align-items: center;
+  align-items: flex-start; /* 允许多行时从顶部对齐 */
   gap: 10px;
   flex-grow: 1;
   min-width: 0;
 }
 .visible-tags-container {
   display: flex;
-  flex-wrap: nowrap;
+  flex-wrap: wrap; /* 允许标签换行 */
   gap: 8px;
-  min-height: 28px;
-  overflow-x: auto;
-  overflow-y: hidden;
+  min-height: 60px; /* 至少提供两行按钮的高度空间 */
+  align-items: flex-start; /* 确保换行时内容从顶部开始 */
   min-width: 0;
   &::-webkit-scrollbar {
     height: 6px;
@@ -304,12 +264,6 @@
 .more-tags-btn-placeholder-inline {
   width: 1px;
 }
-.right-action-buttons {
-  display: flex;
-  align-items: center;
-  gap: 8px;
-  flex-shrink: 0;
-}
 .more-tags-dropdown-menu {
   padding: 0;
   max-height: 280px;

```

#### 针对 `src/views/yqmonitor/components/MainContent/TopNav.vue` 的修改

```diff
--- a/src/views/yqmonitor/components/MainContent/TopNav.vue
+++ b/src/views/yqmonitor/components/MainContent/TopNav.vue
@@ -39,22 +39,39 @@
         </el-button>
       </div>
     </div>
-    <!-- 下部分：标签区域 + 右侧按钮 -->
-    <FilterTagManager
-      ref="filterTagManagerRef"
-      :filter-tags="filterTags"
-      :current-refresh-mode="refreshMode"
-      :max-visible-tags="maxVisibleTags"
-      @tag-selected="handleTagManagerTagSelected"
-      @tags-config-changed="handleTagManagerTagsConfigChanged"
-      @open-filter-drawer="handleTagManagerOpenFilterDrawer"
-      @refresh-mode-change="handleRefreshModeChange"
-      @tags-updated-by-delete="handleTagsUpdatedByDeleteInManager"
-    />
-    <div v-if="showNewInfoTip && refreshMode === 'manual'" class="new-info-tip" @click="handleGetNewInfo">
-      <span>有新增信息，</span>
-      <el-button type="text">点击获取</el-button>
-      <i class="el-icon-refresh" style="cursor:pointer" />
+    <!-- 下部分修改：左侧标签区 + 右侧操作区 -->
+    <div class="tags-and-actions-row">
+      <!-- 左容器: 标签管理器 -->
+      <FilterTagManager
+        ref="filterTagManagerRef"
+        :filter-tags="filterTags"
+        :max-visible-tags="maxVisibleTags"
+        class="tags-container-left"
+        @tag-selected="handleTagManagerTagSelected"
+        @tags-config-changed="handleTagManagerTagsConfigChanged"
+        @tags-updated-by-delete="handleTagsUpdatedByDeleteInManager"
+      />
+      <!-- 右容器: 筛选/刷新/新增提醒 -->
+      <div class="right-controls-group">
+        <div class="top-buttons">
+          <el-button size="small" icon="el-icon-search" @click="handleOpenFilterDialog">筛选</el-button>
+          <el-dropdown trigger="click" @command="handleRefreshModeChange">
+            <el-button size="small" icon="el-icon-refresh">
+              {{ refreshMode === 'manual' ? '手动刷新' : '自动刷新' }}
+              <i class="el-icon-arrow-down el-icon--right" />
+            </el-button>
+            <el-dropdown-menu slot="dropdown">
+              <el-dropdown-item command="manual">手动刷新</el-dropdown-item>
+              <el-dropdown-item command="auto">自动刷新</el-dropdown-item>
+            </el-dropdown-menu>
+          </el-dropdown>
+        </div>
+        <div v-if="showNewInfoTip && refreshMode === 'manual'" class="new-info-tip" @click="handleGetNewInfo">
+          <span>有新增消息，</span>
+          <span class="get-btn">点击获取</span>
+          <i class="el-icon-refresh" style="cursor:pointer; margin-left: 4px;" />
+        </div>
+      </div>
     </div>
   </div>
 </template>
@@ -172,6 +189,14 @@
     handleTagManagerOpenFilterDrawer(selectedTagInfo) {
       this.$emit('filterOpen', selectedTagInfo)
     },
+    handleOpenFilterDialog() {
+      const selectedTagId = this.$refs.filterTagManagerRef.getSelectedTagId()
+      if (selectedTagId) {
+        const selectedTag = this.filterTags.find(tag => tag.id === selectedTagId)
+        this.$emit('filterOpen', selectedTag ? { editingTagId: selectedTag.id, paramJson: selectedTag.paramJson } : null)
+      } else {
+        this.$emit('filterOpen', null)
+      }
+    },
     handleRefresh() {
       this.$emit('refresh')
     },
@@ -231,6 +256,19 @@
 .top-nav-vertical {
   display: flex;
   flex-direction: column;
+  /* 移除 margin-bottom 以更好地控制与下方内容的间距 */
+  padding: 0 10px;
+}
+.tags-and-actions-row {
+  display: flex;
+  gap: 16px; /* 左右容器间距 */
+  align-items: flex-start; /* 顶部对齐 */
+  margin-top: 8px;
+}
+.tags-container-left {
+  flex-grow: 1; /* 占据剩余空间 */
+  min-width: 0;
+}
   margin-bottom: 10px;
   padding: 0 10px;
   .top-row {
@@ -328,29 +366,41 @@
       padding: 0 12px;
     }
   }
+  .right-controls-group {
+    display: flex;
+    flex-direction: column;
+    align-items: flex-end; /* 内容靠右对齐 */
+    gap: 8px; /* 按钮组和提示信息的垂直间距 */
+    flex-shrink: 0;
+  }
+  .top-buttons {
+    display: flex;
+    gap: 8px;
+  }
   .new-info-tip {
-    margin-top: 1px;
-    text-align: center;
+    padding: 6px 12px;
     color: #1976d2;
-    font-size: 14px;
+    font-size: 12px;
     border: 1px solid #1976d2;
     border-radius: 4px;
-    display: inline-block;
+    display: flex;
+    align-items: center;
+    justify-content: center;
+    white-space: nowrap;
     cursor: pointer;
     transition: all 0.3s;
     background: transparent;
-    width: 100%;
     &:hover {
       background: rgba(25, 118, 210, 0.1);
     }
-    .el-button {
-      margin-left: 8px;
+    .get-btn {
+      margin-left: 4px;
+      text-decoration: underline;
       color: #1976d2;
       font-weight: 500;
-      padding-top: 8px;
-      padding-bottom: 8px;
     }
     i {
-      margin-left: 8px;
       color: #1976d2;
     }
   }
```

#### 针对 `src/views/yqmonitor/components/MainContent/index.vue` 的修改

这个文件中的 `TopNav` 组件上绑定的事件需要更新，因为部分事件现在由 `TopNav` 自己处理，而不是从 `FilterTagManager` 冒泡上来的。

```diff
--- a/src/views/yqmonitor/components/MainContent/index.vue
+++ b/src/views/yqmonitor/components/MainContent/index.vue
@@ -8,7 +8,6 @@
           @nav-change="handleNavChange"
           @filterOpen="handleFilterOpen"
           @refresh="getContentList"
-          @refreshTags="handleResetAndRefresh"
           @resetFilter="handleFilterReset"
           @search="handleSearch"
           @clear-search="handleClearSearch"

```
**说明:** `@refreshTags` 事件原本是由 `FilterTagManager` 在删除标签后触发，再由 `TopNav` 透传上来。现在 `FilterTagManager` 依然会触发 `@tags-updated-by-delete`，`TopNav` 监听后会调用 `handleTagsUpdatedByDeleteInManager`，这个方法内部再向上触发 `@refreshTags`。检查 `TopNav.vue`，它确实这样做了，所以 `index.vue` 中的 `@refreshTags` 绑定可以保留。但是，为了逻辑清晰，我们把 `handleTagsUpdatedByDeleteInManager` 方法的内容直接改为 `$emit('refreshTags')`。

**修正 `TopNav.vue` 的 `handleTagsUpdatedByDeleteInManager`**

```javascript
// 在 TopNav.vue 的 methods 中
handleTagsUpdatedByDeleteInManager() {
  this.$emit('refreshTags'); // 直接向上触发，保持与 MainContent.vue 的通信
},
```

您的 `TopNav.vue` 已经有了这个方法，所以无需更改 `MainContent/index.vue`。我的上一个diff建议是多余的，现有逻辑是正确的。

### 总结

请按照上述 **`FilterTagManager.vue`** 和 **`TopNav.vue`** 的 diff 进行修改。`MainContent/index.vue` 无需改动。

完成修改后，您的页面布局将完全符合客户的需求和您的设计草图：
1.  标签区域 (`FilterTagManager`) 将会占据左侧大部分空间，并且标签会自动换行，至少有两行的显示区域。
2.  “筛选”、“手动刷新”按钮和新增消息提醒会作为一个整体，显示在右侧。
3.  新增消息提醒的文案和样式也已按要求调整。

如果还有不清楚的地方，或者需要进一步的调整，请随时告诉我。