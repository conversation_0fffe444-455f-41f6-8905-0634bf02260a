import axios from 'axios'
import globalRequest from '@/utils/request'

// 创建测试接口专用的axios实例
const request = axios.create({
  baseURL: '/xy/api',
  timeout: 30000
})

// 请求拦截器 - 动态添加认证头
request.interceptors.request.use(
  config => {
    // 检查请求配置中是否传入了动态token
    if (config.token) {
      config.headers.authorization = `Bearer ${config.token}`
    } 
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

/**
 * 综合搜索
 * @param {object} params 请求参数
 * @param {string} token  动态传入的认证Token
 */
export function getSearchList(params, token) {
  // 将token通过请求配置的自定义字段传入
  return request.post('/service/search/post_list', params, { token })
}

/**
 * 获取搜索详情
 * @param {object} params 请求参数
 * @param {string} token  动态传入的认证Token
 */
export function getSearchDetail(params, token) {
  // 将token通过请求配置的自定义字段传入
  return request.post('/service/search/post_info', params, { token })
}

/**
 * 存储Token
 * @param {string} xiaoyToken
 * @returns {Promise}
 */
export function storeToken(xiaoyToken) {
  // 使用全局request实例
  return globalRequest.post('/xiaoy/storeToken', { xiaoyToken });
}

/**
 * 获取Token
 * @returns {Promise}
 */
export function getToken() {
  // 使用全局request实例
  return globalRequest.get('/xiaoy/getToken');
}

// 默认导出
export default {
  getSearchList,
  getSearchDetail,
  storeToken,
  getToken
}