import Mock from 'mockjs'
import dogImg from '@/assets/images/OIP-C.jpg'
import avatarImg from '@/assets/images/profile.jpg'

// 生成信源组列表数据
Mock.mock(/\/yqms\/universalGravitation\/direct\/directList/, 'post', {
  'data': {
    'list|10': [{
      'direct_id|+1': 1,
      'direct_name': '@ctitle(4, 8)'
    }],
    'total': 100
  },
  'code': 0,
  'msg': 'success'
})

// 生成舆情列表数据
Mock.mock(/\/yqms\/monitor\/getPublicSentimentList/, 'post', {
  'rows|10': [{
    'uniqueId|+1': '@guid',
    'postId|+1': '@id',
    'url': '@url',
    'index': '@increment(0)',
    'playUrl': function() {
      const index = this.index
      // 前2条是视频布局
      if (index < 2) return ['https://ustatic.hudongmiao.com/joymewScreen/video/weddingCarSign.mp4']
      // 第3-4条是图片布局
      if (index < 4) return []
      // 第5-6条是纯文本布局
      if (index < 6) return []
      // 第7-8条是图文布局
      if (index < 8) return []
      // 最后2条随机
      return Math.random() > 0.5 ? ['https://ustatic.hudongmiao.com/joymewScreen/video/weddingCarSign.mp4'] : []
    },
    'images': function() {
      const index = this.index
      // 前2条是视频布局
      if (index < 2) return []
      // 第3-4条是图片布局
      if (index < 4) return [dogImg]
      // 第5-6条是纯文本布局
      if (index < 6) return []
      // 第7-8条是图文布局
      if (index < 8) return [dogImg]
      // 最后2条随机
      return Math.random() > 0.5 ? [dogImg] : []
    },
    'title': '@ctitle(10, 30)',
    'content': function() {
      const index = this.index
      // 前2条是视频布局
      if (index < 2) return ''
      // 第3-4条是图片布局
      if (index < 4) return ''
      // 第5-6条是纯文本布局
      if (index < 6) return Mock.Random.cparagraph(3, 7)
      // 第7-8条是图文布局
      if (index < 8) return Mock.Random.cparagraph(3, 7)
      // 最后2条随机
      return Math.random() > 0.5 ? Mock.Random.cparagraph(3, 7) : ''
    },
    'sourceType|1': ['news', 'social', 'forum'],
    'sourceLevel|1': ['0', '1', '2', '3'],
    'videoInfo': function() {
      const index = this.index
      if (index < 2 || (index >= 8 && Math.random() > 0.5)) {
        return {
          'duration|60-300': 1,
          'coverInfo': {
            'onlineUrl': dogImg,
            'coverOcr': '@csentence'
          }
        }
      }
      return null
    },
    'postPublishTime': '@datetime',
    'isOriginal|0-2': 1,
    'platform|1': ['weibo', 'douyin'],
    'platformName|1': ['微博', '抖音'],
    'postCategory|1-5': 1,
    'postType|1': ['图文', '视频', '直播'],
    'postStats': {
      'respondCount|100-10000': 1,
      'likeCount|100-5000': 1,
      'commentCount|50-1000': 1,
      'repostCount|10-500': 1,
      'viewCount|1000-50000': 1,
      'collectCount|10-200': 1
    },
    'userInfo': {
      'userId': '@id',
      'avatar': avatarImg,
      'url': '@url',
      'nickname': '@cname',
      'fansCount|1000-1000000': 1,
      'verifyInfo': {
        'verifyType|1': ['企业认证', '政务认证', '机构认证'],
        'verifyName': '@ctitle(4, 8)'
      }
    },
    'basedLocation': {
      'publicLocation': {
        'province': '@province',
        'city': '@city',
        'location': '@county @ctitle(5, 10)'
      }
    },
    'feature': {
      'isNoise|1': ['0', '1'],
      'sensitive|0-2': 1,
      'ocr': '@cparagraph(1, 2)',
      'asr': '@cparagraph(1, 2)'
    }
  }],
  'total': 100,
  'code': 200,
  'msg': 'success'
})
