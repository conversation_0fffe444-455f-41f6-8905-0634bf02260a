<template>
  <div class="right-wrap">
    <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
      <div class="avatar-wrapper">
        <span class="user-name">{{ nickName }}</span>
        <i class="el-icon-caret-bottom" />
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item @click.native="handleUpdate">
          <span>修改昵称</span>
        </el-dropdown-item>
        <el-dropdown-item divided @click.native="logout">
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 修改昵称对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" maxlength="30" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getUserProfile, updateUserProfile } from '@/api/system/user'

export default {
  name: 'UserSection',
  computed: {
    ...mapGetters(['nickName'])
  },
  data() {
    return {
      // 弹出层标题
      title: '修改昵称',
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: '用户昵称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 修改按钮操作
    handleUpdate() {
      getUserProfile().then(response => {
        this.form = response.data
        this.open = true
      })
    },
    // 表单提交
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateUserProfile(this.form).then(response => {
            this.$modal.msgSuccess('修改成功')
            this.$store.dispatch('GetInfo').then(() => {
              this.open = false
            })
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        nickName: undefined
      }
      this.resetForm('form')
    },
    logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/'
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.right-wrap {
  padding-right: 24px;
  .avatar-container {
    .avatar-wrapper {
      display: flex;
      align-items: center;
      padding: 0 12px;
      border-radius: 20px;
      transition: background 0.2s;
      height: 48px;
      min-width: 80px;
      cursor: pointer;
      &:hover {
        background: rgba(60, 60, 60, 0.06);
      }
      .user-name {
        font-size: 16px;
        font-weight: 500;
        color: #444;
        margin-right: 6px;
        letter-spacing: 1px;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .el-icon-caret-bottom {
        font-size: 14px;
        color: #888;
        margin-top: 2px;
      }
    }
  }
}
</style>
