# Directory Structure
```
src/api/yqmonitor/institution.js
src/store/modules/yqclientInstitution.js
src/store/modules/yqmonitorMenu.js
src/views/yqclient/index.vue
src/views/yqmonitor/components/MainContent/Article.vue
src/views/yqmonitor/components/MainContent/ContentList.vue
src/views/yqmonitor/components/MainContent/index.vue
src/views/yqmonitor/components/SideMenu/CategoryNode.vue
src/views/yqmonitor/components/SideMenu/index.vue
src/views/yqmonitor/components/SideMenu/ModuleNode.vue
src/views/yqmonitor/components/SideMenu/TopicNode.vue
src/views/yqmonitor/index.vue
```

# Files

## File: src/store/modules/yqclientInstitution.js
```javascript

```

## File: src/views/yqclient/index.vue
```vue
<template>
  <div class="yqclient-index">
    <h1>欢迎来到 yqclient 页面</h1>
  </div>
</template>
<script>
export default {
  name: 'YqclientIndex',
  data() {
    return {
    }
  },
  methods: {
  },
  mounted() {
  }
}
</script>
<style scoped>
.yqclient-index {
  padding: 20px;
}
</style>
```

## File: src/views/yqmonitor/components/SideMenu/index.vue
```vue
<template>
  <el-aside width="200px">
    <el-scrollbar style="height: 95%;">
      <div class="menu-wrapper">
        <el-tree
          ref="tree"
          :data="currentDisplayMenu"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="true"
          :current-node-key="currentKey"
          highlight-current
          :default-expanded-keys="expandedKeys"
          draggable
          :allow-drop="allowDrop"
          @node-drag-start="handleNodeDragStart"
          @node-drop="handleNodeDrop"
          @node-drag-end="handleNodeDragEnd"
        >
          <template slot-scope="{ node, data }">
            <component
              :is="getNodeComponent(data.type)"
              ref="treeNode"
              :node="node"
              :data="data"
              :is-tree-currently-dragging="isTreeDragging"
              @command="handleCommand"
              @module-click="handleModuleNodeClick"
              @category-click="handleCategoryNodeClick"
            />
          </template>
        </el-tree>
      </div>
    </el-scrollbar>
  </el-aside>
</template>
<script>
import ModuleNode from './ModuleNode.vue'
import CategoryNode from './CategoryNode.vue'
import TopicNode from './TopicNode.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'SideMenu',
  components: {
    ModuleNode,
    CategoryNode,
    TopicNode
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['menuList'])
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentKey: null,
      expandedKeys: [],
      currentDisplayMenu: [],
      isTreeDragging: false,
      localStorageKey: 'sideMenuOrder'
    }
  },
  watch: {
    editNodeId(newVal) {
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    menuList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          let orderedList = JSON.parse(JSON.stringify(newVal))
          const savedOrder = this.loadOrderFromLocalStorage()
          if (savedOrder) {
            orderedList = this.applyStoredOrderToData(orderedList, savedOrder)
          }
          this.currentDisplayMenu = orderedList
          this.expandedKeys = [newVal[0].id]
        } else {
          this.currentDisplayMenu = []
        }
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
    if (this.currentDisplayMenu && this.currentDisplayMenu.length > 0) {
      if (!this.$store.state.yqmonitorMenu.currentNode || !this.findNodeById(this.currentDisplayMenu, this.$store.state.yqmonitorMenu.currentNode.id)) {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', this.currentDisplayMenu[0])
        this.currentKey = this.currentDisplayMenu[0].id
      } else {
        this.currentKey = this.$store.state.yqmonitorMenu.currentNode.id
      }
    }
  },
  methods: {
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node
        if (node.children) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    getNodeComponent(type) {
      const componentMap = {
        module: 'ModuleNode',
        category: 'CategoryNode',
        topic: 'TopicNode'
      }
      return componentMap[type]
    },
    handleCommand({ command, data, value }) {
      if (this.isTreeDragging) return
      switch (command) {
        case 'addTopic':
          this.addTopic(data)
          break
        case 'editTopic':
          this.editTopic(data)
          break
      }
    },
    addTopic(node) {
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
        ...node,
        isAddingTopic: true
      })
      this.$store.commit('yqmonitorTopic/SET_TOPIC_DRAWER_VISIBLE', true)
    },
    editTopic(node) {
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', node)
      this.$store.commit('yqmonitorTopic/SET_TOPIC_DRAWER_VISIBLE', true)
    },
    handleModuleNodeClick(node) {
      if (this.isTreeDragging) return
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', node)
      this.currentKey = node.id
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(node.id)
      })
    },
    handleCategoryNodeClick(node) {
      if (this.isTreeDragging) return
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
        ...node,
        isEditing: false
      })
      this.currentKey = node.id
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(node.id)
      })
    },
    handleNodeDragStart(node, event) {
      this.isTreeDragging = true
    },
    handleNodeDrop(draggingNode, dropNode, dropType, ev) {
      this.$nextTick(() => {
        this.saveOrderToLocalStorage(this.currentDisplayMenu)
      })
    },
    handleNodeDragEnd(draggingNode, dropNode, dropType, ev) {
      this.isTreeDragging = false
    },
    allowDrop(draggingNode, dropNode, type) {
      const draggingData = draggingNode.data
      const dropData = dropNode.data
      if (!dropNode || !draggingData || !dropData ||
                !draggingNode.parent || !dropNode.parent ||
                !draggingNode.parent.data || !dropNode.parent.data ||
                typeof draggingNode.parent.data.id === 'undefined' ||
                typeof dropNode.parent.data.id === 'undefined' ||
                typeof draggingData.id === 'undefined' ||
                typeof dropData.id === 'undefined') {
        console.log('allowDrop: Missing critical data (node, data, parent, id).', { draggingNode, dropNode, type })
        return false
      }
      if (draggingData.type === 'module') {
        console.log(false)
        return false
      }
      if (type === 'inner') {
        return false
      }
      if (draggingData.id === dropData.id) {
        console.log(false)
        return false
      }
      const isSameType = draggingData.type === dropData.type
      const isSameParent = draggingNode.parent.data.id === dropNode.parent.data.id
      if (isSameType && isSameParent) {
        if (draggingData.type === 'category' || draggingData.type === 'topic') {
          console.log(true)
          return true
        }
      }
      console.log(false)
      return false
    },
    generateOrderMap(nodes) {
      const orderMap = {}
      orderMap['root_modules'] = nodes.filter(n => n.type === 'module').map(n => n.id)
      const traverse = (currentNodes) => {
        currentNodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            orderMap[node.id] = node.children.map(child => child.id)
            traverse(node.children)
          }
        })
      }
      traverse(nodes)
      return orderMap
    },
    saveOrderToLocalStorage(menuData) {
      if (!menuData) return
      const orderMap = this.generateOrderMap(menuData)
      localStorage.setItem(this.localStorageKey, JSON.stringify(orderMap))
    },
    loadOrderFromLocalStorage() {
      const storedOrder = localStorage.getItem(this.localStorageKey)
      return storedOrder ? JSON.parse(storedOrder) : null
    },
    applyStoredOrderToData(data, storedOrderMap) {
      if (!storedOrderMap) return data
      if (storedOrderMap['root_modules']) {
        data.sort((a, b) => {
          const aIndex = storedOrderMap['root_modules'].indexOf(a.id)
          const bIndex = storedOrderMap['root_modules'].indexOf(b.id)
          if (aIndex === -1 && bIndex === -1) return 0
          if (aIndex === -1) return 1
          if (bIndex === -1) return -1
          return aIndex - bIndex
        })
      }
      const sortChildren = (nodesToSort) => {
        if (!nodesToSort) return
        nodesToSort.forEach(node => {
          if (node.children && node.children.length > 0 && storedOrderMap[node.id]) {
            node.children.sort((a, b) => {
              const aIndex = storedOrderMap[node.id].indexOf(a.id)
              const bIndex = storedOrderMap[node.id].indexOf(b.id)
              if (aIndex === -1 && bIndex === -1) return 0
              if (aIndex === -1) return 1
              if (bIndex === -1) return -1
              return aIndex - bIndex
            })
            sortChildren(node.children)
          }
        })
      }
      sortChildren(data)
      return data
    }
  }
}
</script>
<style lang="scss" scoped>
.el-aside {
    background-color: #2A3143;
    border-right: 1px solid #e6e6e6;
    margin-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    .menu-wrapper {
        padding: 0;
        color: #fff;
    }
    ::v-deep .el-tree {
        background-color: transparent;
        color: inherit;
        .el-tree-node {
            &:focus>.el-tree-node__content {
                background-color: #F2F3F7;
                color: #5090f1;
                .custom-tree-node {
                    .el-dropdown-link {
                        color: #5090f1;
                    }
                    .node-input {
                        color: #5090f1;
                    }
                }
            }
            &__content {
                height: 48px;
                &:hover {
                    background-color: #F2F3F7;
                    color: #5090f1;
                }
            }
            &.is-current>.el-tree-node__content {
                background-color: #F2F3F7;
                color: #5090f1;
                .custom-tree-node .el-dropdown-link {
                    color: #5090f1;
                }
                .custom-tree-node>.node-content>.node-input {
                    color: #5090f1;
                }
            }
            .custom-tree-node .node-label {
                max-width: 118px;
            }
        }
        .el-tree-node__content:hover .custom-tree-node .el-dropdown-link {
            color: #5090f1;
        }
        .el-tree-node__content:hover>.custom-tree-node>.node-content>.node-input {
            color: #5090f1;
        }
        &>.el-tree-node>.el-tree-node__content {
            background-color: #222e44;
            border: 1px solid #2f4b77;
            color: #ddd;
            height: 48px;
            line-height: 48px;
            margin-top: 10px;
        }
    }
    ::v-deep .el-tree-node__expand-icon {
        display: none !important;
    }
}
.el-scrollbar {
    ::v-deep {
        .el-scrollbar__wrap {
            overflow-x: hidden;
        }
    }
}
</style>
```

## File: src/views/yqmonitor/components/SideMenu/ModuleNode.vue
```vue
<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleModuleClick" />
    <div class="node-content">
      <svg-icon icon-class="all" class-name="custom-class" />
      <span class="node-label" :title="node.label" @click="handleModuleClick">{{ node.label }}</span>
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click="handlePlusClick">
        <i class="el-icon-plus" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="addCategory">添加分类</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
import auth from '@/plugins/auth'
export default {
  name: 'ModuleNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handlePlusClick(event) {
      if (this.isTreeCurrentlyDragging) {
        event.stopPropagation()
        return
      }
      if (this.node.expanded) {
        event.stopPropagation()
        return
      }
    },
    handleModuleClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      if (this.node.expanded) {
        console.log('已经展开了不允许关闭')
        e && e.stopPropagation()
      }
      this.$emit('module-click', this.data)
    },
    addCategory() {
      if (!auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有添加分类的权限')
        return
      }
      const newCategory = {
        id: `category_${Date.now()}`,
        name: '新分类',
        type: 'category',
        parentId: this.data.id,
        children: []
      }
      if (!this.data.children) {
        this.$set(this.data, 'children', [])
      }
      this.data.children.push(newCategory)
      this.$nextTick(() => {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
          ...newCategory,
          isEditing: true
        })
      })
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'addCategory') {
        this.addCategory()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;
    .node-content {
        display: flex;
        align-items: center;
        padding-left: 8px;
    }
    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }
}
.custom-class {
    font-size: 15px;
}
.el-dropdown-link {
    cursor: pointer;
    color: #fff;
    &:hover {
        color: #409EFF;
    }
}
.click-area {
    position: absolute;
    top: -3px;
    right: 26px;
    width: 70px;
    height: 50px;
    cursor: pointer;
}
</style>
```

## File: src/views/yqmonitor/components/SideMenu/TopicNode.vue
```vue
<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleNodeClick" />
    <div class="node-content" :class="{ 'opacity-text': data.status === 0 }" @click="handleNodeClick">
      <svg-icon :icon-class="data.id === '1-1' ? 'all' : 'topic'" class-name="custom-class" />
      <span class="node-label" :title="node.label">{{ node.label }}</span>
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click.stop>
        <i class="el-icon-more" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="edit">编辑</el-dropdown-item>
        <el-dropdown-item command="toggleUpdate">{{ data.status === 1 ? '暂停更新' : '开启更新' }}</el-dropdown-item>
        <el-dropdown-item command="delete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
import auth from '@/plugins/auth'
import { openPlan, suspendPlan, deletePlan } from '@/api/yqmonitor/topic'
export default {
  name: 'TopicNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleNodeClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      this.$store.dispatch('yqmonitorMenu/setCurrentNode', this.data)
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'edit' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有编辑权限')
        return
      }
      if (command === 'toggleUpdate' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有更新权限')
        return
      }
      if (command === 'delete' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有删除权限')
        return
      }
      if (command === 'edit') {
        this.$emit('command', { command: 'editTopic', data: this.data })
      } else if (command === 'toggleUpdate') {
        this.handleToggleUpdate()
      } else if (command === 'delete') {
        this.deleteTopic()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    },
    deleteTopic() {
      this.$confirm('确认删除该专题吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePlan({ menuId: this.data.id }).then(async response => {
          console.log('删除专题', response)
          if (response.code === 0) {
            await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
            const menuList = this.$store.state.yqmonitorMenu.menuList
            if (menuList && menuList.length > 0) {
              this.$store.dispatch('yqmonitorMenu/setCurrentNode', menuList[0])
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      }).catch(() => { })
    },
    async handleToggleUpdate() {
      try {
        const menuId = this.data.id
        if (this.data.status === 1) {
          const res = await suspendPlan({ menuId })
          if (res.code === 0) {
            this.data.status = 0
            this.$message.success('暂停更新成功')
          } else {
            this.$message.error('暂停更新失败')
          }
        } else {
          const res = await openPlan({ menuId })
          if (res.code === 0) {
            this.data.status = 1
            this.$message.success('开启更新成功')
          } else {
            this.$message.error('开启更新失败')
          }
        }
      } catch (error) {
        console.error('切换更新状态失败:', error)
        this.$message.error('切换更新状态失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;
    .node-content {
        display: flex;
        align-items: center;
    }
    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }
    .node-input {
        margin-left: 8px;
        width: 130px;
        background: transparent;
        border: none;
        border-bottom: 1px solid #409EFF;
        color: #fff;
        outline: none;
        padding: 2px 4px;
        font-size: 16px;
    }
}
.custom-class {
    font-size: 15px;
}
.el-dropdown-link {
    cursor: pointer;
    color: #fff;
    &:hover {
        color: #409EFF;
    }
}
.opacity-text {
    opacity: 0.3;
}
.click-area {
    position: absolute;
    left: -35px;
    top: -9px;
    width: 170px;
    height: 50px;
    cursor: pointer;
}
</style>
```

## File: src/views/yqmonitor/components/SideMenu/CategoryNode.vue
```vue
<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleCategoryClick" />
    <div class="node-content">
      <svg-icon icon-class="folder" class-name="custom-class" />
      <span v-if="!isEditing" class="node-label" :title="node.label" @click="handleCategoryClick">{{ node.label
      }}</span>
      <input
        v-else
        ref="input"
        v-model="editingName"
        class="node-input"
        @blur="handleBlur"
        @keyup.enter="handleEnter"
      >
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click="handleMoreClick">
        <i class="el-icon-more" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="addTopic">添加专题</el-dropdown-item>
        <el-dropdown-item command="edit">编辑</el-dropdown-item>
        <el-dropdown-item command="delete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
import auth from '@/plugins/auth'
import { addMenu, updateMenu, delMenu } from '@/api/system/menu'
export default {
  name: 'CategoryNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      editingName: '',
      isEnterPressed: false
    }
  },
  computed: {
    currentNode() {
      return this.$store.state.yqmonitorMenu.currentNode
    },
    currentBusinessType() {
      return this.$store.state.yqmonitorMenu.currentBusinessType
    }
  },
  watch: {
    currentNode(newVal) {
      if (newVal &&
                newVal.id === this.data.id &&
                newVal.type === 'category' &&
                !newVal.isAddingTopic &&
                newVal.isEditing) {
        this.startEdit()
      }
    }
  },
  methods: {
    handleMoreClick(event) {
      if (this.isTreeCurrentlyDragging) {
        event.stopPropagation()
        return
      }
      if (this.node.expanded) {
        event.stopPropagation()
        return
      }
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'edit' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有编辑权限')
        return
      }
      if (command === 'delete' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有删除权限')
        return
      }
      if (command === 'addTopic' && !auth.hasRole('tyfz-admin')) {
        this.$message.warning('您没有添加专题的权限')
        return
      }
      if (command === 'edit') {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
          ...this.data,
          isEditing: true
        })
        this.startEdit()
      } else if (command === 'delete') {
        this.deleteCategory()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    },
    deleteCategory() {
      if (this.data.children && this.data.children.length > 0) {
        this.$message.warning('当前分类下存在专题，无法删除')
        return
      }
      this.$confirm('确认删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delMenu(this.data.id).then(async(response) => {
          if (response.code === 200) {
            await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
            const menuList = this.$store.state.yqmonitorMenu.menuList
            if (menuList && menuList.length > 0) {
              this.$store.dispatch('yqmonitorMenu/setCurrentNode', menuList[0])
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      }).catch(() => { })
    },
    startEdit() {
      this.isEditing = true
      this.editingName = this.node.label
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    },
    handleBlur() {
      if (!this.isEnterPressed) {
        this.handleSave()
      }
      this.isEnterPressed = false
    },
    handleEnter() {
      this.isEnterPressed = true
      this.handleSave()
    },
    handleSave() {
      if (this.editingName.trim() !== '') {
        this.updateNodeName(this.editingName)
      }
      this.isEditing = false
    },
    updateNodeName(newName) {
      this.data.name = newName
      // 构造菜单数据
      const menuData = {
        menuName: newName,
        parentId: this.data.parentId,
        orderNum: 1,
        path: this.currentBusinessType,
        menuType: 'M',
        status: '0',
        visible: '0',
        isFrame: '1',
        isCache: '0'
      }
      if (typeof this.data.id === 'string' && this.data.id.startsWith('category_')) {
        addMenu(menuData).then(response => {
          console.log(response)
          if (response.code === 200) {
            this.$message.success('添加成功')
            this.$store.dispatch('yqmonitorMenu/fetchMenuList')
          } else {
            this.$message.error('添加失败')
          }
        }).catch(error => {
          console.error('添加失败:', error)
          this.$message.error('添加失败')
        })
      } else {
        menuData.menuId = this.data.id
        updateMenu(menuData).then(response => {
          console.log(response)
          if (response.code === 200) {
            this.$message.success('更新分类成功')
          } else {
            this.$message.error('更新分类失败')
          }
        }).catch(error => {
          console.error('更新分类失败:', error)
          this.$message.error('更新分类失败')
        })
      }
    },
    handleCategoryClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      if (this.node.expanded) {
        console.log('已经展开了不允许关闭')
        e && e.stopPropagation()
      }
      this.$emit('category-click', this.data)
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;
    .node-content {
        display: flex;
        align-items: center;
    }
    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }
    .node-input {
        margin-left: 8px;
        width: 130px;
        background: transparent;
        border: none;
        border-bottom: 1px solid #409EFF;
        color: #fff;
        outline: none;
        padding: 2px 4px;
        font-size: 16px;
    }
}
.custom-class {
    font-size: 15px;
}
.el-dropdown-link {
    cursor: pointer;
    color: #fff;
    &:hover {
        color: #409EFF;
    }
}
.click-area {
    position: absolute;
    top: -9px;
    right: 30px;
    width: 132px;
    height: 50px;
    cursor: pointer;
}
</style>
```

## File: src/views/yqmonitor/index.vue
```vue
<template>
  <div class="app-container" :key="$route.fullPath">
    <el-container>
      <side-menu />
      <main-content :module-type="moduleType"/>
    </el-container>
    <topic-drawer :visible.sync="localDrawerVisible" />
  </div>
</template>
<script>
import SideMenu from './components/SideMenu/index.vue'
import MainContent from './components/MainContent/index.vue'
import TopicDrawer from './components/topic/index.vue'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'YQMonitor',
  components: {
    SideMenu,
    MainContent,
    TopicDrawer
  },
  data() {
    return {
      localDrawerVisible: false
    }
  },
  computed: {
    ...mapState('yqmonitorTopic', ['topicDrawerVisible']),
    moduleType() {
      const path = this.$route.path;
      if (path.startsWith('/followedEvents')) {
        return 'followedEvents';
      }
      if (path.startsWith('/targetMonitor')) {
        return 'targetMonitor';
      }
      return 'yqmonitor';
    }
  },
  watch: {
    topicDrawerVisible: {
      immediate: true,
      handler(val) {
        this.localDrawerVisible = val
      }
    },
    localDrawerVisible(val) {
      this.SET_TOPIC_DRAWER_VISIBLE(val)
    },
    moduleType: {
      immediate: true,
      handler(newModuleType) {
        this.setBusinessTypeByModule(newModuleType)
      }
    },
  },
  methods: {
    ...mapMutations('yqmonitorTopic', ['SET_TOPIC_DRAWER_VISIBLE']),
    setBusinessTypeByModule(moduleType) {
      let businessType = 'yqmonitor-business'
      switch (moduleType) {
        case 'yqmonitor':
          businessType = 'yqmonitor-business'
          break
        case 'followedEvents':
          businessType = 'followed-events-business'
          break
        case 'targetMonitor':
          businessType = 'target-monitor-business'
          break
        default:
          businessType = 'yqmonitor-business'
      }
      this.$store.commit('yqmonitorMenu/SET_CURRENT_BUSINESS_TYPE', businessType)
      console.log('设置业务类型:', businessType, '对应模块:', moduleType)
    }
  },
  mounted() {
    document.body.style.overflowY = 'hidden'
  },
  beforeDestroy() {
    document.body.style.overflowY = ''
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  height: 100%;
  background-color: #f5f7fa;
  padding: 0;
  .el-container {
    height: 100%;
  }
}
</style>
```

## File: src/api/yqmonitor/institution.js
```javascript
import request from '@/utils/request'
export function addInstitution(data) {
  return request({
    url: '/business/institution/addInstitution',
    method: 'post',
    data: data
  })
}
export function deleteInstitution(codes) {
  return request({
    url: '/business/institution/delete/' + codes,
    method: 'get'
  })
}
export function editInstitution(data) {
  return request({
    url: '/business/institution/editInstitution',
    method: 'post',
    data: data
  })
}
export function getInstitutionInfo(code) {
  return request({
    url: '/business/institution/getInstitutionInfo/' + code,
    method: 'get'
  })
}
export function listInstitution(query) {
  return request({
    url: '/business/institution/list',
    method: 'get',
    params: query
  })
}
export function getInstitutionTree() {
  return request({
    url: '/business/institution/tree',
    method: 'get'
  })
}
export function getInstitutionUserTree() {
  return request({
    url: '/business/institution/treeWithUser',
    method: 'get'
  })
}
export function sendWarnMsg(data) {
  return request({
    url: '/business/warnSms/sendWarnMsg',
    method: 'post',
    data: data
  })
}
export function listInstitutionGroup(institutionCode) {
  return request({
    url: '/business/institutionGroup/list',
    method: 'get',
    params: { institutionCode }
  })
}
export function addInstitutionGroup(data) {
  return request({
    url: '/business/institutionGroup/addInstitutionGroup',
    method: 'post',
    data: data
  })
}
export function editInstitutionGroup(data) {
  return request({
    url: '/business/institutionGroup/editInstitutionGroup',
    method: 'post',
    data: data
  })
}
export function removeInstitutionGroup(id) {
  return request({
    url: '/business/institutionGroup/remove/' + id,
    method: 'get'
  })
}
export function getAllInstitutionGroups(institutionCodes) {
  return request({
    url: '/business/institutionGroup/getAllInstitutionGroups',
    method: 'post',
    data: { institutionCodes },
    headers: {
      repeatSubmit: false
    }
  })
}
export function getAIAnalysisFromBackend(data) {
  return request({
    url: '/coze/analyze',
    method: 'post',
    data: data
  })
}
```

## File: src/store/modules/yqmonitorMenu.js
```javascript
import { listMenu } from "@/api/system/menu";
const state = {
  menuList: [],
  currentNode: null,
  lastQueryParams: {},
  isOpeningFromTag: false,
  cachedTimeRangeType: null,
  searchText: "", // 新增：搜索文本
  currentBusinessType: "yqmonitor-business",
};
const mutations = {
  SET_MENU_LIST(state, list) {
    state.menuList = list;
  },
  SET_CURRENT_NODE(state, node) {
    state.currentNode = node;
  },
  SET_LAST_QUERY_PARAMS(state, params) {
    state.lastQueryParams = params;
  },
  SET_OPENING_FROM_TAG(state, value) {
    state.isOpeningFromTag = value;
  },
  SET_CACHED_TIME_RANGE_TYPE(state, type) {
    state.cachedTimeRangeType = type;
  },
  SET_SEARCH_TEXT(state, text) {
    state.searchText = text;
  },
  SET_CURRENT_BUSINESS_TYPE(state, type) {
    state.currentBusinessType = type;
  },
};
const actions = {
  async fetchMenuList({ commit }) {
    try {
      const res = await listMenu();
      console.log("来自服务端的原始菜单数据:", res);
      const businessType = state.currentBusinessType;
      const yqmonitorMenus = res.data.filter(
        (item) => item.path === businessType
      );
      const buildTree = (items) => {
        const itemMap = new Map();
        const tree = [];
        for (const item of items) {
          const menuItem = {
            id: item.menuId,
            name: item.menuName,
            type: item.menuName.includes("本地监测")
              ? "module"
              : item.menuType === "C"
              ? "topic"
              : "category",
            parentId: item.parentId,
            status: item.showType === "2" ? 1 : 0,
            planId: item.planId,
            children: [],
          };
          itemMap.set(menuItem.id, menuItem);
        }
        for (const item of itemMap.values()) {
          const parent = itemMap.get(item.parentId);
          if (parent) {
            parent.children.push(item);
          } else {
            tree.push(item);
          }
        }
        return tree;
      };
      const menuList = buildTree(yqmonitorMenus);
      commit("SET_MENU_LIST", menuList);
      console.log("菜单数据", menuList);
    } catch (e) {
      console.error("获取菜单数据失败", e);
    }
  },
  setCurrentNode({ commit }, node) {
    commit("SET_CURRENT_NODE", node);
  },
  setOpeningFromTag({ commit }, value) {
    commit("SET_OPENING_FROM_TAG", value);
  },
};
const getters = {
  menuList: (state) => state.menuList,
  currentNode: (state) => state.currentNode,
  lastQueryParams: (state) => state.lastQueryParams,
  isOpeningFromTag: (state) => state.isOpeningFromTag,
  cachedTimeRangeType: (state) => state.cachedTimeRangeType,
  searchText: (state) => state.searchText,
};
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
```

## File: src/views/yqmonitor/components/MainContent/ContentList.vue
```vue
<template>
  <div class="content-list">
    <div v-if="mappedList.length === 0" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
    <div v-for="(item, index) in mappedList" :key="item.frontId" class="list-item">
      <Article :item="item" :index="index" :checked-visible="checkedVisible"
        :checked="checkedList.includes(item.uniqueId)" :sentiment-options="sentimentOptions"
        :ai-sentiment-options="aiSentimentOptions"
        @check-change="val => handleCheckChange(item.uniqueId, val)" @delete="handleDelete"
        @change-sentiment="val => handleChangeSentiment(item.uniqueId, val)"
        @change-ai-sentiment="val => handleChangeAiSentiment(item.uniqueId, val)" @mark-read="handleMarkRead"
        @expand-similar="payload => handleArticleExpandSimilar(payload)" />
    </div>
    <div v-if="total > 1" class="list-pagination">
      <el-pagination background layout="sizes, prev, pager, next, jumper" :current-page="pageNum" :page-size="pageSize"
        :page-sizes="[30, 50, 100]" :total="total" @current-change="onPageChange" @size-change="onSizeChange" />
      <span class="total-text">共{{ total }}条</span>
    </div>
  </div>
</template>
<script>
import Article from './Article.vue'
import { mapItem } from './articleUtil'
export default {
  name: 'ContentList',
  components: { Article },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 30
    },
    total: {
      type: Number,
      default: 0
    },
    checkedList: {
      type: Array,
      default: () => []
    },
    sentimentOptions: {
      type: Array,
      default: () => []
    },
    aiSentimentOptions: {
      type: Array,
      default: () => []
    },
    checkedVisible: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    startIndex() {
      return (this.pageNum - 1) * this.pageSize
    },
    mappedList() {
      return this.list.map(item => mapItem(item))
    },
  },
  methods: {
    onPageChange(page) {
      this.$emit('page-change', page)
    },
    onSizeChange(size) {
      this.$emit('size-change', size)
    },
    handleCheckChange(id, checked) {
      this.$emit('check-change', { id, checked })
    },
    handleDelete(item) {
      this.$emit('delete', item)
    },
    handleChangeSentiment(id, value) {
      this.$emit('change-sentiment', { id, value })
    },
    handleChangeAiSentiment(id, value) {
      this.$emit('change-ai-sentiment', { id, value })
    },
    handleMarkRead(uniqueId) {
      this.$emit('mark-read', uniqueId)
    },
    handleArticleExpandSimilar(payload) {
      this.$emit('expand-similar', payload)
    }
  }
}
</script>
<style lang="scss" scoped>
.content-list {
  position: relative;
  min-height: calc(100vh - 200px);
  padding-bottom: 120px;
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
  .list-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    .item-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .item-index {
        margin-right: 10px;
      }
      .el-tag+.el-tag {
        margin-left: 5px;
      }
      .item-title {
        margin-left: 10px;
        flex: 1;
        &.is-read {
          color: #909399;
        }
      }
      .item-stats {
        margin-left: 15px;
        span {
          margin-left: 15px;
          color: #909399;
          font-size: 12px;
          i {
            margin-right: 3px;
          }
        }
      }
    }
    .item-content {
      margin-bottom: 10px;
      .content-text {
        margin-bottom: 10px;
      }
      .content-images {
        display: flex;
        gap: 10px;
        margin-top: 10px;
        .el-image {
          width: 200px;
          height: 150px;
          border-radius: 4px;
          overflow: hidden;
          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 20px;
          }
        }
      }
    }
    .item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #909399;
      font-size: 12px;
      .info-group {
        display: flex;
        align-items: center;
        gap: 15px;
        .el-rate {
          display: inline-flex;
          height: 20px;
        }
      }
    }
  }
  .list-pagination {
    position: fixed;
    bottom: 60px;
    left: 220px;
    width: calc(100vw - 200px - 40px);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 9;
    .total-text {
      margin-left: 16px;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style>
```

## File: src/views/yqmonitor/components/MainContent/Article.vue
```vue
<template>
  <div class="article-container">
    <div class="item-header">
      <el-checkbox
        v-if="checkedVisible"
        class="article-checkbox"
        :value="checked"
        style="margin-right: 8px"
        @change="(val) => $emit('check-change', val)"
      />
      <span class="item-index">{{ index + 1 }}</span>
      <span v-if="item.moduleReadStatus === 1" class="read-status">已读</span>
      <el-tag size="small" :type="aiPublicOpinionTypeColor" effect="dark">{{
        aiPublicOpinionType
      }}</el-tag>
      <el-tag size="small" :type="articleTypeColor" effect="plain">{{
        articleType
      }}</el-tag>
      <el-tooltip v-if="item.titleObj.rawTitle" placement="top" :open-delay="contentTooltipDelay">
        <template #content>
          <div v-html="highlightHitWords(item.titleObj.rawTitle)" />
        </template>
        <span
          class="item-title"
          :class="{ 'is-read': item.moduleReadStatus === 1 }"
          @click="handleTitleClick(item)"
          v-html="highlightHitWords(item.titleObj.title, false)"
        />
      </el-tooltip>
      <span
        v-else
        class="item-title"
        :class="{ 'is-read': item.moduleReadStatus === 1 }"
        @click="handleTitleClick(item)"
        v-html="highlightHitWords(item.titleObj.title, false)"
      />
      <div class="item-stats">
        <el-tooltip
          v-if="item.similarCount && item.similarCount > 0"
          content="相似舆情"
          placement="top"
          :open-delay="iconTooltipDelay"
        >
          <span class="similar-count-badge" @click.stop="handleExpandSimilar">
            <svg-icon icon-class="similar" class-name="similar-icon" />
            {{ item.similarCount }}
          </span>
        </el-tooltip>
        <el-tooltip content="浏览量" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-view" /> {{ item.views }}</span>
        </el-tooltip>
        <el-tooltip content="评论数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-chat-line-square" /> {{ item.comments }}</span>
        </el-tooltip>
        <el-tooltip content="点赞数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-star-off" /> {{ item.likes }}</span>
        </el-tooltip>
        <el-tooltip content="转发数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-share" /> {{ item.reposts }}</span>
        </el-tooltip>
        <el-tooltip content="收藏数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-collection" /> {{ item.collects }}</span>
        </el-tooltip>
      </div>
    </div>
    <div class="item-content">
      <div v-if="item.isOriginal === 1" class="content-text">
        <template v-if="item.contentObj.content">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawContent)" />
            <span slot="reference" v-html="highlightHitWords(item.contentObj.content, false)" />
          </el-popover>
        </template>
        <div v-if="item.contentObj.ocr" class="ocr-block">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawOcr)" />
            <span slot="reference" class="ocr-content" v-html="highlightHitWords(item.contentObj.ocr, false)" />
          </el-popover>
        </div>
        <div v-if="item.contentObj.asr" class="asr-block">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawAsr)" />
            <span slot="reference" class="asr-content" v-html="highlightHitWords(item.contentObj.asr, false)" />
          </el-popover>
        </div>
      </div>
      <div v-else class="content-text">
        <template>
          <div class="origin-inline">
            <template v-if="item.contentObj.content">
              <el-popover placement="top" trigger="click" :width="400" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawContent)" />
                <span
                  slot="reference"
                  class="origin-main-content"
                  v-html="highlightHitWords(item.contentObj.content, false)"
                />
              </el-popover>
            </template>
            <div v-if="item.contentObj.ocr" class="ocr-block">
              <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawOcr)" />
                <span
                  slot="reference"
                  class="ocr-content"
                  v-html="highlightHitWords(item.contentObj.ocr, false)"
                />
              </el-popover>
            </div>
            <div v-if="item.contentObj.asr" class="asr-block">
              <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawAsr)" />
                <span
                  slot="reference"
                  class="asr-content"
                  v-html="highlightHitWords(item.contentObj.asr, false)"
                />
              </el-popover>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="item-footer">
      <div class="info-group">
        <span class="platform" @click="handleJumpSource"><el-tag
          size="mini"
          type="info"
          :class="{ 'hitword-highlight-specail': mediaHitWords.length > 0, 'is-key-item': mediaHitWords.length > 0 }"
        >{{ item.source }}</el-tag></span>
        <span class="author-info" :class="{'is-key-item': accountHitWords.length > 0 }">
          <el-image
            v-if="item.avatar"
            :src="item.avatar"
            style="
              width: 20px;
              height: 20px;
              border-radius: 50%;
              margin-right: 4px;
            "
            fit="cover"
          />
          <a
            v-if="item.authorUrl"
            :href="item.authorUrl"
            target="_blank"
            :class="{ 'hitword-highlight-specail': accountHitWords.length > 0 }"
            class="author-link"
          >{{ item.author
          }}</a>
          <span v-else :class="{ 'hitword-highlight-specail': accountHitWords.length > 0 }">{{ item.author }}</span>
        </span>
        <span class="fans">粉丝：{{ item.fansCount }}</span>
        <span v-if="item.location" class="location">地域：{{ item.location }}</span>
        <span class="sentiment-tag">万有引力倾向性：<el-tag size="mini" :type="sentimentTypeColor">{{
          sentiment
        }}</el-tag></span>
      </div>
      <div class="item-icons">
        <el-button v-hasPermi="['yqmonitor:createWarning','followedEvents:createWarning','targetMonitor:createWarning']" class="warning-btn" size="mini" type="text" @click="handleWarning">
          预警
        </el-button>
        <el-tooltip content="跳转源站" placement="top">
          <i class="el-icon-link" @click="handleJumpSource" />
        </el-tooltip>
        <el-tooltip content="复制url" placement="top">
          <i class="el-icon-document-copy" @click="handleCopyUrl" />
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:editSentiment','followedEvents:editSentiment','targetMonitor:editSentiment']" content="修改倾向性" placement="top">
          <el-dropdown trigger="click" @command="(val) => handleEditSentiment(val)">
            <span class="el-dropdown-link">
              <i class="el-icon-edit" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in sentimentOptions.filter(
                  (item) => item.value !== 'all'
                )"
                :key="item.value"
                :command="item.value"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:editAiSentiment','followedEvents:editAiSentiment','targetMonitor:editAiSentiment']" content="修改AI舆情倾向性" placement="top">
          <el-dropdown trigger="click" @command="(val) => handleEditAiSentiment(val)">
            <span class="el-dropdown-link">
              <i class="el-icon-document-add" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in aiSentimentOptions.filter(
                  (item) => item.value !== 'all'
                )"
                :key="item.value"
                :command="item.value"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip content="复制" placement="top">
          <i class="el-icon-files" @click="handleCopy" />
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:delete','followedEvents:delete','targetMonitor:delete']" content="删除" placement="top">
          <i class="el-icon-delete" @click="handleDelete" />
        </el-tooltip>
      </div>
    </div>
    <div class="item-footer_last">
      <div class="hit-word_wrap">
        <div v-if="aiFeatureHitWords.length" class="ai-feature-details">
          <div v-for="feature in aiFeatureHitWords" :key="feature.key" class="feature-item">
            <span class="feature-reason">{{ feature.label }}:</span>
            <span class="feature-hitword">{{ feature.value.join(", ") }}</span>
          </div>
        </div>
        <span v-if="wyylHitWords.length" class="hitword">
          <span class="hitword-label">命中词：</span>
          <el-tooltip v-if="wyylHitWords.join(',').length > 8" :content="wyylHitWords.join(',')" placement="top">
            <span class="hitword-content">{{
              wyylHitWords.join(",").slice(0, 8) + "..."
            }}</span>
          </el-tooltip>
          <span v-else class="hitword-content">{{
            wyylHitWords.join(",")
          }}</span>
        </span>
      </div>
      <div class="time-wrap">
        <el-popover
          v-if="item.time !== item.firstCollectionTime"
          placement="top"
          trigger="click"
          :content="'首次采集时间：' + item.firstCollectionTime"
          effect="light"
        >
          <span
            slot="reference"
            class="time collect-time-highlight"
            style="cursor: pointer;"
          >
            采集时间：{{ item.time }}
          </span>
        </el-popover>
        <span
          v-else
          class="time"
        >
          采集时间：{{ item.time }}
        </span>
        <span class="publish-time">发布时间：{{ item.publishTime }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import SvgIcon from '@/components/SvgIcon'
import {
  getWyylHitWords,
  getAiFeatureHitWords,
  getInstitutionAliasHitWords,
  getMediaHitWords,
  getAccountHitWords,
  getExcludedFromHighlight
} from './articleUtil'
import { mapGetters } from 'vuex'
export default {
  name: 'Article',
  components: { SvgIcon },
  dicts: [
    'content_publish_type',
    'ai_public_opinion_type',
    'negative_public_opinion_tag',
    'filter_sentiment'
  ],
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    checked: {
      type: Boolean,
      default: false
    },
    checkedVisible: {
      type: Boolean,
      default: true
    },
    contentTooltipDelay: {
      type: Number,
      default: 1500
    },
    iconTooltipDelay: {
      type: Number,
      default: 300
    },
    sentimentOptions: {
      type: Array,
      default: () => []
    },
    aiSentimentOptions: {
      type: Array,
      default: () => []
    },
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['searchText']),
    articleType() {
      const type = this.dict.type.content_publish_type.find(
        (item) => String(item.value) === String(this.item.isOriginal)
      )
      return type ? type.label : ''
    },
    articleTypeColor() {
      const colors = {
        原创: 'success',
        转发: 'warning',
        评论: 'info'
      }
      return colors[this.articleType] || 'info'
    },
    aiPublicOpinionType() {
      if (!this.item.dmxTagInfo) return ''
      const type = this.dict.type.ai_public_opinion_type.find(
        (item) => String(item.value) === String(this.item.dmxTagInfo.label)
      )
      return type ? type.label : ''
    },
    aiPublicOpinionTypeColor() {
      return this.aiPublicOpinionType === '敏感' ? 'danger' : ''
    },
    dmxTagInfoReason() {
      if (!this.item.dmxTagInfo) return []
      return this.item.dmxTagInfo.features
        .filter((value) => value.hitWord && value.hitWord.length > 0)
        .map((value) => {
          const reason = this.dict.type.negative_public_opinion_tag.find(
            (item) => String(item.value) === String(value.reason)
          )
          return reason ? reason.label : ''
        })
    },
    sentiment() {
      const type = this.dict.type.filter_sentiment.find(
        (item) => String(item.value) === String(this.item.sentiment)
      )
      return type ? type.label : ''
    },
    sentimentTypeColor() {
      const colors = {
        中性: 'info',
        非敏感: 'success',
        敏感: 'danger'
      }
      return colors[this.sentiment] || 'info'
    },
    isContentEllipsis() {
      return (
        this.item.contentObj.content &&
        this.item.contentObj.content.endsWith('...')
      )
    },
    isOcrEllipsis() {
      return (
        this.item.contentObj.ocr && this.item.contentObj.ocr.endsWith('...')
      )
    },
    isAsrEllipsis() {
      return (
        this.item.contentObj.asr && this.item.contentObj.asr.endsWith('...')
      )
    },
    wyylHitWords() {
      return getWyylHitWords(this.item.hitWordsInfo)
    },
    aiFeatureHitWords() {
      return getAiFeatureHitWords(this.item.hitWordsInfo)
    },
    mediaHitWords() {
      return getMediaHitWords(this.item.hitWordsInfo)
    },
    accountHitWords() {
      return getAccountHitWords(this.item.hitWordsInfo)
    },
    excludedHitWords() {
      return getExcludedFromHighlight(this.item.hitWordsInfo)
    },
  },
  methods: {
    highlightHitWords(text, isReplaceBr = true) {
      let words = []
      words = words.concat(getWyylHitWords(this.item.hitWordsInfo))
      const aiFeatureList = getAiFeatureHitWords(this.item.hitWordsInfo)
      aiFeatureList.forEach((feature) => {
        if (Array.isArray(feature.value)) {
          words = words.concat(feature.value)
        }
      })
      words = words.concat(getInstitutionAliasHitWords(this.item.hitWordsInfo))
      if (this.searchText) {
        const searchWords = this.searchText.split(/\s+/).filter((word) => word)
        words.push(...searchWords)
      }
      const excludedWords = this.excludedHitWords
      words = words.filter((word) => !excludedWords.includes(word))
      words = Array.from(new Set(words)).filter(Boolean)
      words.sort((a, b) => b.length - a.length)
      if (!text || !words.length) return text
      let html = text
      words.forEach((word) => {
        if (!word) return
        const reg = new RegExp(
          word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        )
        html = html.replace(
          reg,
          `<span class="hitword-highlight">${word}</span>`
        )
      })
      if (isReplaceBr) {
        html = html.replace(/\n/g, '<br/>')
      }
      return html
    },
    handleDelete() {
      this.$confirm('确认删除该文章吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('delete', this.item)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleJumpSource() {
      window.open(this.item.url, '_blank')
    },
    handleCopyUrl() {
      if (this.item.url) {
        navigator.clipboard
          .writeText(this.item.url)
          .then(() => {
            this.$message.success('链接已复制到剪切板')
          })
          .catch(() => {
            this.$message.error('复制失败，请手动复制')
          })
      } else {
        this.$message.warning('没有可复制的链接')
      }
    },
    handleCopy() {
      const summary = this.item.contentObj?.rawContent
        ? this.item.contentObj.rawContent
        : this.item.contentObj?.rawAsr
          ? this.item.contentObj.rawAsr
          : this.item.contentObj?.rawOcr
            ? this.item.contentObj.rawOcr
            : ''
      // 提取作者ID
      const authorId = this.item.authorId || ''
      // 提取认证类型
      const verifyType = this.item.verifyInfo?.verifyType || '普通用户'
      // 提取关键词，优先使用hitWordsInfo中的wyylHitWords
      let keywords = ''
      // 处理命中词
      if (this.hitWordsInfo) {
        // 收集所有命中词到一个数组
        const allHitWords = []
        for (const key in this.hitWordsInfo) {
          if (
            Array.isArray(this.hitWordsInfo[key]) &&
            this.hitWordsInfo[key].length > 0
          ) {
            allHitWords.push(...this.hitWordsInfo[key])
          }
        }
        // 去重（如果需要）
        const uniqueHitWords = [...new Set(allHitWords)]
        // 用中文逗号连接
        if (uniqueHitWords.length > 0) {
          keywords = uniqueHitWords.join('，')
        }
      }
      // 涉事省份，从location提取
      const province = this.item.location || ''
      const lines = [
        this.item.titleObj?.title
          ? `标题:${this.item.titleObj.rawTitle || this.item.titleObj.title}`
          : '',
        this.item.url ? `链接:${this.item.url}` : '',
        `摘要:${summary}`,
        this.item.source ? `来源:${this.item.source}` : '',
        this.item.author ? `作者:${this.item.author}` : '',
        this.item.publishTime ? `时间:${this.item.publishTime}` : '',
        authorId ? `作者ID:${authorId}` : '',
        this.sentiment ? `倾向性:${this.sentiment}` : '',
        typeof this.item.comments !== 'undefined'
          ? `评论数:${this.item.comments}`
          : '',
        typeof this.item.reposts !== 'undefined'
          ? `转发数:${this.item.reposts}`
          : '',
        keywords ? `涉及关键词:${keywords}` : '',
        `认证类型:${verifyType}`,
        typeof this.item.fansCount !== 'undefined'
          ? `粉丝数:${this.item.fansCount}`
          : '',
        province ? `涉事省份:${province}` : ''
      ]
        .filter(Boolean)
        .join('\n')
      navigator.clipboard
        .writeText(lines)
        .then(() => {
          this.$message.success('结构化信息已复制到剪切板')
        })
        .catch(() => {
          this.$message.error('复制失败，请手动复制')
        })
    },
    handleEditSentiment(val) {
      this.$emit('change-sentiment', {
        id: this.item.id,
        sentiment: val
      })
    },
    handleEditAiSentiment(val) {
      this.$emit('change-ai-sentiment', {
        id: this.item.uniqueId,
        value: val
      })
    },
    getFeatureReason(reason) {
      const reasonDict = this.dict.type.negative_public_opinion_tag.find(
        (item) => String(item.value) === String(reason)
      )
      return reasonDict ? reasonDict.label : ''
    },
    openDetail(item) {
      // 触发事件，通知父组件去标记已读
      this.$emit('mark-read', item.uniqueId)
      localStorage.removeItem('yq_detail_item')
      window.open(`/yqmonitor/detail/${item.uniqueId}`, '_blank')
    },
    handleExpandSimilar() {
      if (this.item.similarGroupId) {
        this.$emit('expand-similar', {
          similarGroupId: this.item.similarGroupId,
          representativeArticle: this.item
        })
      } else {
        this.$message.warning('此文章没有关联的相似组信息。')
      }
    },
    handleTitleClick(item) {
      const selection = window.getSelection()
      if (selection.toString().length > 0) {
        return
      }
      this.openDetail(item)
    },
    handleWarning() {
      window.open(
        `/yqwarning/sender?warningId=${this.item.uniqueId}`,
        '_blank'
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.article-container {
  background: #fff;
  border-radius: 4px;
}
.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  .item-index {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-right: 12px;
    color: #909399;
    font-size: 14px;
  }
  .el-tag {
    margin-right: 8px;
  }
  .item-title {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin-right: 16px;
    overflow: hidden;
    text-decoration: none;
    &.is-read {
      color: #909399;
    }
    &:hover {
      color: #409eff;
    }
  }
  .item-stats {
    display: flex;
    gap: 16px;
    color: #909399;
    font-size: 16px;
    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .similar-count-badge {
      cursor: pointer;
    }
  }
}
.item-content {
  margin-bottom: 12px;
  cursor: pointer;
}
.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .info-group {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #909399;
    .fans {
      color: #909399;
    }
    .platform {
      color: #909399;
      cursor: pointer;
    }
    .location {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
    .sentiment-tag {
      color: #909399;
    }
    .author-info {
      display: flex;
      align-items: center;
      color: #909399;
      .author-link {
        color: #909399;
        text-decoration: none;
        &:hover {
          color: #409eff;
        }
      }
    }
  }
  .item-icons {
    display: flex;
    align-items: center;
    gap: 8px;
    .warning-btn {
      background: #cce2ff;
      color: #e4393c;
      font-weight: bold;
      border-radius: 4px;
      padding: 6px 12px;
      border: none;
      transition: background 0.2s;
      &:hover {
        background: #b3d4fc;
        color: #d32f2f;
      }
    }
  }
}
.item-footer_last {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 8px;
  .hit-word_wrap {
    display: flex;
    column-gap: 12px;
    align-items: center;
    line-height: 1.5;
    .ai-feature-details {
      display: flex;
      column-gap: 20px;
      .feature-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        .feature-reason {
          color: #909399;
          margin-right: 8px;
          flex-shrink: 0;
        }
        .feature-hitword {
          color: #f10a0a;
          word-break: break-all;
        }
      }
    }
    .hitword {
      color: #909399;
      font-size: 14px;
      .hitword-label {
        color: #909399;
      }
      .hitword-content {
        color: #f10a0a;
      }
    }
  }
  .time-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #909399;
    column-gap: 20px;
    white-space: nowrap;
  }
}
.main-content {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.ocr-block,
.asr-block {
  display: flex;
  align-items: flex-start;
  margin-top: 12px;
  border-radius: 4px;
}
.ocr-content,
.asr-content {
  font-size: 16px;
  color: #333;
}
.ocr-tag {
  margin-right: 8px;
}
.asr-tag {
  margin-right: 8px;
}
.origin-inline {
  background: #fcedec;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
}
.origin-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  background: #e0e0e0;
  object-fit: cover;
  flex-shrink: 0;
}
.origin-text {
  display: block;
  flex: 1;
  min-width: 0;
  word-break: break-all;
}
.origin-nickname {
  color: #409eff;
  font-size: 15px;
  max-width: 100px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.origin-main-content {
  color: #333;
  font-size: 16px;
  vertical-align: middle;
  word-break: break-all;
}
.user-svg-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  background: #e0e0e0;
  display: inline-block;
  vertical-align: middle;
}
.read-status {
  display: inline-block;
  background: #aeb2b7;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  padding: 2px 8px;
  margin-right: 8px;
}
.collect-time-highlight {
  border: 4px solid #b3cfff;
  border-radius: 20px;
  padding: 2px 12px;
  background: #f6faff;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 8px 0 #e3eefd;
}
.is-key-item {
  outline: 3px solid #ff9900;
  border-radius: 4px;
}
</style>
<style>
.hitword-highlight {
  color: #e4393c;
  background: #fffbe6;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
}
.hitword-highlight-specail {
  color: #e47070f5 !important;
  font-weight: 600;
}
.custom-tag {
  display: inline-block;
  font-size: 16px;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  white-space: nowrap;
  margin-right: 8px;
  padding: 0 2px;
}
.zw {
  font-size: 12px;
}
.custom-tag-content {
  background-color: #e8f4ff;
  border-color: #d1e9ff;
  color: #1890ff;
}
.custom-tag-warning {
  background-color: #fff8e6;
  border-color: #fff1cc;
  color: #ffba00;
}
.custom-tag-success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}
.avatar-img {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  vertical-align: -3px;
  margin-right: 2px;
}
</style>
```

## File: src/views/yqmonitor/components/MainContent/index.vue
```vue
<template>
  <div class="main-content">
    <div v-loading="loading || similarArticlesLoading" class="card-box">
      <div class="fixed-header">
        <TopNav
          ref="topNavRef"
          :filter-tags="localFilterTags"
          :max-visible-tags="maxVisibleTags"
          @tagClick="handleTagClick"
          @nav-change="handleNavChange"
          @filterOpen="handleFilterOpen"
          @refresh="getContentList"
          @refreshTags="handleResetAndRefresh"
          @resetFilter="handleFilterReset"
          @search="handleSearch"
          @clear-search="handleClearSearch"
          @tags-config-changed="handleTagsOrderChanged"
        />
      </div>
      <div ref="scrollableContentRef" class="scrollable-content">
        <ContentList
          :list="contentList"
          :page-num="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          :checked-list="checkedList"
          :sentiment-options="sentimentOptions"
          :ai-sentiment-options="aiSentimentOptions"
          :module-type="moduleType"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @check-change="handleCheckChange"
          @delete="handleSingleDelete"
          @change-sentiment="handleChangeSingleSentiment"
          @change-ai-sentiment="handleChangeSingleAiSentimentType"
          @mark-read="handleSingleMarkRead"
          @expand-similar="handleExpandSimilarArticles"
        />
        <ScrollButtons :scroll-container="$refs.scrollableContentRef" />
      </div>
      <FixedActionBar
        :checked-list="checkedList"
        :content-list="contentList"
        :sentiment-options="sentimentOptions"
        :ai-sentiment-options="aiSentimentOptions"
        :selected-sort-type="selectedSortType"
        :sort-options="sortOptions"
        :module-type="moduleType"
        @check-all="handleCheckAll"
        @mark-read="handleMarkRead"
        @change-sentiment="handleChangeSentiment"
        @change-ai-sentiment-type="handleChangeAiSentimentType"
        @sort-type-change="handleSortTypeChange"
        @delete="handleDelete"
        @refresh="getContentList"
      />
      <FilterDrawer
        ref="filterDrawerRef"
        :visible.sync="filterVisible"
        :editing-tag-id="editingTagId"
        :module-type="moduleType"
        @submit="onFilterSubmit"
        @submitAndSave="onFilterSubmitAndSave"
        @updateFilterWord="handleUpdateFilterWord"
      />
      <el-dialog
        title="保存条件"
        :visible.sync="saveDialogVisible"
        width="400px"
        @close="saveDialogVisible = false"
      >
        <el-input v-model="saveName" placeholder="请输入条件名称" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfirm">确定</el-button>
        </span>
      </el-dialog>
      <SimilarArticlesDialog
        :visible.sync="similarDialogVisible"
        :articles="similarArticlesList"
        :loading="similarArticlesLoading"
        :representative-article-title="currentRepresentativeArticleTitle"
        :total="similarArticlesTotal"
        @page-change="handleSimilarArticlesPageChange"
      />
    </div>
  </div>
</template>
<script>
import {
  getPublicSentimentList,
  getFilterWordList,
  addFilterWord,
  updateFilterWord,
  batchUpdatePublicSentiment,
  batchUpdateSentiment,
  batchUpdateAiSentimentType,
  batchDeletePublicSentiment,
  getSimilarGroupListApi,
} from "@/api/yqmonitor";
import TopNav from "./TopNav.vue";
import ContentList from "./ContentList.vue";
import FilterDrawer from "./FilterDrawer.vue";
import FixedActionBar from "./FixedActionBar.vue";
import { getDateRangeByType } from "@/utils/yqmonitorTool";
import ScrollButtons from "@/views/yqmonitor/components/scrollButtons.vue";
import SimilarArticlesDialog from "./SimilarArticlesDialog.vue";
export default {
  name: "MainContent",
  components: {
    TopNav,
    ContentList,
    FilterDrawer,
    FixedActionBar,
    ScrollButtons,
    SimilarArticlesDialog,
  },
  dicts: ["filter_sentiment", "ai_public_opinion_type"],
  props: {
    moduleType: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      contentList: [],
      loading: false,
      total: 0,
      maxVisibleTags: 20,
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        mediaTypes: [],
        isAsc: "desc",
        orderByColumn: "",
        enableSimilarityDedup: "fold",
      },
      filterVisible: false,
      filterTags: [],
      localFilterTags: [],
      saveDialogVisible: false,
      saveName: "",
      saveForm: null,
      checkedList: [],
      selectedSortType: "time_desc",
      sortOptions: [
        {
          label: "按时间降序",
          value: "time_desc",
          orderByColumn: "pushTime",
          isAsc: "desc",
        },
        {
          label: "按时间升序",
          value: "time_asc",
          orderByColumn: "pushTime",
          isAsc: "asc",
        },
        {
          label: "评论数",
          value: "comment_desc",
          orderByColumn: "commentCount",
          isAsc: "desc",
        },
        {
          label: "转发数",
          value: "repost_desc",
          orderByColumn: "repostCount",
          isAsc: "desc",
        },
        {
          label: "点赞数",
          value: "like_desc",
          orderByColumn: "likeCount",
          isAsc: "desc",
        },
        {
          label: "阅读数",
          value: "view_desc",
          orderByColumn: "viewCount",
          isAsc: "desc",
        },
        {
          label: "粉丝数",
          value: "fans_desc",
          orderByColumn: "fansCount",
          isAsc: "desc",
        },
      ],
      sentimentOptions: [],
      aiSentimentOptions: [],
      editingTagId: "", // 当前编辑的tag id
      isTestMode: false,
      similarDialogVisible: false,
      similarArticlesList: [],
      similarArticlesLoading: false,
      currentRepresentativeArticleTitle: "相似文章列表",
      // 相似文章分页相关数据
      similarArticlesTotal: 0,
      currentSimilarGroupId: null,
      currentRepresentativeArticle: null,
    };
  },
  computed: {
    currentNode() {
      return this.$store.state.yqmonitorMenu.currentNode;
    },
    userId() {
      // 假设用户ID存在store里
      return this.$store.state.user?.id || 1;
    },
    readStatusKey() {
      const keyMap = {
        yqmonitor: "readStatus",
        followedEvents: "followReadStatus",
        targetMonitor: "targetDetectReadStatus",
      };
      return keyMap[this.moduleType] || "readStatus";
    },
  },
  watch: {
    currentNode: {
      handler(newVal, oldVal) {
        if (newVal && newVal.id !== oldVal?.id) {
          const currentRoute = this.$route.path;
          let shouldExecute = false;
          if (newVal.name === '本地监测') {
            shouldExecute = currentRoute === '/yqmonitor/index';
          } else if (newVal.name === '本地监测(F)') {
            shouldExecute = currentRoute === '/followedEvents/index';
          } else if (newVal.name === '本地监测(T)') {
            shouldExecute = currentRoute === '/targetMonitor/index';
          } else {
            shouldExecute = true;
          }
          if (shouldExecute) {
            this.handleResetAndRefresh();
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.sentimentOptions = this.dict.type.filter_sentiment || [];
    this.aiSentimentOptions = this.dict.type.ai_public_opinion_type || [];
  },
  methods: {
    collectPlanIds(node, result = []) {
      if (!node) return result;
      if (node.planId) {
        result.push(node.planId);
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => this.collectPlanIds(child, result));
      }
      return result.length > 0 ? result : ["-1"];
    },
    async getContentList() {
      try {
        this.loading = true;
        console.log("currentNode:", this.currentNode);
        if (!this.currentNode) {
          return;
        }
        const handleTimeRange = (type) => {
          if (type) {
            const [startTime, endTime] = getDateRangeByType(type);
            if (startTime && endTime) {
              this.queryParams = {
                ...this.queryParams,
                startTime,
                endTime,
              };
            }
          }
        };
        if (this.queryParams.cachedTimeRangeType) {
          handleTimeRange(this.queryParams.cachedTimeRangeType);
        } else if (this.$store.state.yqmonitorMenu.cachedTimeRangeType) {
          handleTimeRange(this.$store.state.yqmonitorMenu.cachedTimeRangeType);
        }
        const params = {
          schemeNumbers: this.collectPlanIds(this.currentNode),
          ...this.queryParams,
        };
        this.$store.commit("yqmonitorMenu/SET_LAST_QUERY_PARAMS", params);
        this.checkedList = [];
        const { rows, total } = await getPublicSentimentList(params);
        console.log("getPublicSentimentList:", rows);
        this.contentList = rows.map((row) => {
          return {
            ...row,
            moduleReadStatus: row[this.readStatusKey],
          };
        });
        this.total = total;
        this.resetScrollPosition();
      } catch (error) {
        console.error("获取内容列表失败:", error);
        this.$message.error("获取内容列表失败");
      } finally {
        this.loading = false;
      }
    },
    onFilterSubmit(form) {
      if ("cachedTimeRangeType" in this.queryParams) {
        delete this.queryParams.cachedTimeRangeType;
      }
      this.queryParams = {
        ...this.queryParams,
        ...form,
        pageNum: 1,
      };
      this.getContentList();
      this.$refs.topNavRef.resetSelectedTag();
    },
    handleNavChange({ mediaTypes }) {
      if (
        !mediaTypes ||
        (Array.isArray(mediaTypes) && mediaTypes.length === 0) ||
        (Array.isArray(mediaTypes) &&
          mediaTypes.length === 1 &&
          mediaTypes[0] === "all")
      ) {
        this.queryParams.mediaTypes = [];
      } else {
        this.queryParams.mediaTypes = mediaTypes;
      }
      this.queryParams.pageNum = 1;
      this.getContentList();
    },
    handleFilterOpen(filterObj) {
      this.filterVisible = true;
      this.editingTagId = filterObj?.editingTagId || "";
      // 如果有传入的筛选参数，则初始化表单
      if (filterObj) {
        this.$store.commit("yqmonitorMenu/SET_OPENING_FROM_TAG", true);
        this.$nextTick(() => {
          this.$refs.filterDrawerRef.initFormFromJson(filterObj.paramJson);
        });
      } else {
        this.$store.commit("yqmonitorMenu/SET_OPENING_FROM_TAG", false);
      }
    },
    handlePageChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getContentList();
    },
    handleSizeChange(pageSize) {
      this.queryParams.pageSize = pageSize;
      this.getContentList();
    },
    resetScrollPosition() {
      this.$nextTick(() => {
        if (this.$refs.scrollableContentRef) {
          this.$refs.scrollableContentRef.scrollTop = 0;
        }
      });
    },
    async fetchTags() {
      if (this.isTestMode) {
        const mockTags = this.generateMockTags(25);
        const userPreferences = this.loadUserTagPreferences();
        const processedTags = mockTags.map((mockTag, index) => {
          const preference = userPreferences.find((p) => p.id === mockTag.id);
          return {
            ...mockTag,
            isPinned: preference
              ? preference.isPinned
              : index < this.maxVisibleTags,
            order: preference ? preference.order : mockTag.originalOrder,
          };
        });
        processedTags.sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return a.order - b.order;
        });
        let currentOrder = 0;
        const pinnedTags = processedTags
          .filter((t) => t.isPinned)
          .sort((a, b) => a.order - b.order);
        const unpinnedTags = processedTags
          .filter((t) => !t.isPinned)
          .sort((a, b) => a.order - b.order);
        const finalSortedTags = [];
        pinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        unpinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        this.filterTags = finalSortedTags;
        this.localFilterTags = finalSortedTags;
        console.log("Test mode tags:", this.filterTags);
        return;
      }
      try {
        if (
          this.currentNode &&
          typeof this.currentNode.id === "string" &&
          this.currentNode.id.startsWith("category_")
        ) {
          return;
        }
        const res = await getFilterWordList({
          userId: this.userId,
          menuId: this.currentNode.id,
          pageNum: 1,
          pageSize: 100,
        });
        const apiTags = (res.rows || []).map((tag) => ({
          ...tag,
          id: `${tag.userId}${tag.menuId}${tag.name}`,
          originalOrder: tag.order || 0,
        }));
        const userPreferences = this.loadUserTagPreferences();
        const processedTags = apiTags.map((apiTag, index) => {
          const preference = userPreferences.find((p) => p.id === apiTag.id);
          return {
            ...apiTag,
            isPinned: preference
              ? preference.isPinned
              : index < this.maxVisibleTags,
            order: preference ? preference.order : apiTag.originalOrder,
          };
        });
        processedTags.sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return a.order - b.order;
        });
        let currentOrder = 0;
        const pinnedTags = processedTags
          .filter((t) => t.isPinned)
          .sort((a, b) => a.order - b.order);
        const unpinnedTags = processedTags
          .filter((t) => !t.isPinned)
          .sort((a, b) => a.order - b.order);
        const finalSortedTags = [];
        pinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        unpinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        this.filterTags = finalSortedTags;
        this.localFilterTags = finalSortedTags;
        console.log("filterTags:", this.filterTags);
      } catch (error) {
        console.error("获取标签列表失败:", error);
        this.$message.error("获取标签列表失败");
      }
    },
    generateMockTags(count) {
      const tags = [];
      const adjectives = [
        "重要",
        "紧急",
        "普通",
        "特殊",
        "关键",
        "核心",
        "基础",
        "高级",
        "临时",
        "长期",
      ];
      const nouns = [
        "监控",
        "分析",
        "预警",
        "报告",
        "统计",
        "评估",
        "追踪",
        "调查",
        "研究",
        "测试",
      ];
      for (let i = 0; i < count; i++) {
        const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
        const noun = nouns[Math.floor(Math.random() * nouns.length)];
        const name = `${adj}${noun}${i + 1}`;
        tags.push({
          id: `test_${i}`,
          name: name,
          userId: 1,
          menuId: 1,
          originalOrder: i,
          isPinned: i < 3,
          paramJson: JSON.stringify({
            mediaTypes: ["all"],
            sensitivitys: ["all"],
          }),
        });
      }
      return tags;
    },
    async onFilterSubmitAndSave(form) {
      this.saveForm = form;
      this.saveDialogVisible = true;
      this.saveName = "";
      // 更新查询参数以立即应用筛选
      const apiReadyForm = { ...form };
      if (
        apiReadyForm.mustContainGroups &&
        Array.isArray(apiReadyForm.mustContainGroups)
      ) {
        apiReadyForm.mustContainGroups = apiReadyForm.mustContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean);
      }
      if (
        apiReadyForm.mustNotContainGroups &&
        Array.isArray(apiReadyForm.mustNotContainGroups)
      ) {
        apiReadyForm.mustNotContainGroups = apiReadyForm.mustNotContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean);
      }
      this.queryParams = {
        ...this.queryParams,
        ...apiReadyForm,
        pageNum: 1,
      };
      this.getContentList();
    },
    // handleSaveConfirm只保留addFilterWord逻辑
    async handleSaveConfirm() {
      if (!this.saveName) {
        this.$message.warning("请输入条件名称");
        return;
      }
      // 新增标签
      await addFilterWord({
        userId: this.userId,
        menuId: this.currentNode.id,
        name: this.saveName,
        paramJson: JSON.stringify({
          ...this.saveForm,
          // 如果存在缓存的时间范围类型，则添加到paramJson中
          ...(this.$store.state.yqmonitorMenu.cachedTimeRangeType && {
            cachedTimeRangeType:
              this.$store.state.yqmonitorMenu.cachedTimeRangeType,
          }),
        }),
      });
      this.$message.success("保存成功");
      this.saveDialogVisible = false;
      await this.fetchTags();
      // 设置新标签为选中状态
      const newTagId = `${this.userId}${this.currentNode.id}${this.saveName}`;
      this.$refs.topNavRef.setSelectedTag(newTagId);
    },
    // 添加处理更新筛选条件的方法
    async handleUpdateFilterWord({ id, filterWord }) {
      try {
        const tag = this.filterTags.find((tag) => tag.id === id);
        const tagName = tag ? tag.name : "";
        if (!tagName) {
          this.$message.error("未找到对应的标签名");
          return;
        }
        await updateFilterWord({
          userId: this.userId,
          menuId: this.currentNode.id,
          name: tagName,
          paramJson: JSON.stringify({
            ...filterWord,
            ...(this.$store.state.yqmonitorMenu.cachedTimeRangeType && {
              cachedTimeRangeType:
                this.$store.state.yqmonitorMenu.cachedTimeRangeType,
            }),
          }),
        });
        // 更新查询参数
        const apiReadyFilterWord = { ...filterWord };
        if (
          apiReadyFilterWord.mustContainGroups &&
          Array.isArray(apiReadyFilterWord.mustContainGroups)
        ) {
          apiReadyFilterWord.mustContainGroups =
            apiReadyFilterWord.mustContainGroups
              .map((g) => g.value.trim())
              .filter(Boolean);
        }
        if (
          apiReadyFilterWord.mustNotContainGroups &&
          Array.isArray(apiReadyFilterWord.mustNotContainGroups)
        ) {
          apiReadyFilterWord.mustNotContainGroups =
            apiReadyFilterWord.mustNotContainGroups
              .map((g) => g.value.trim())
              .filter(Boolean);
        }
        this.queryParams = {
          ...this.queryParams,
          ...apiReadyFilterWord,
          pageNum: 1,
        };
        // 获取内容列表
        await this.getContentList();
        // 更新标签列表
        await this.fetchTags();
        // 清理LocalStorage中可能存在的该标签的排序信息
        const prefs = this.loadUserTagPreferences();
        const updatedPrefs = prefs.filter((p) => p.id !== id);
        this.saveUserTagPreferences(updatedPrefs);
        this.$message.success("更新成功");
        this.editingTagId = "";
      } catch (error) {
        console.error("更新筛选条件失败:", error);
        this.$message.error("更新筛选条件失败");
      }
    },
    handleTagClick(tag) {
      try {
        const params = JSON.parse(tag.paramJson);
        // 将保存的词组格式转换为API格式
        if (
          params.mustContainGroups &&
          Array.isArray(params.mustContainGroups)
        ) {
          params.mustContainGroups = params.mustContainGroups
            .map((g) => g.value.trim())
            .filter(Boolean);
        } else if (params.mustContain) {
          // 兼容旧版
          params.mustContainGroups = [params.mustContain.trim()].filter(
            Boolean
          );
          delete params.mustContain;
        }
        if (
          params.mustNotContainGroups &&
          Array.isArray(params.mustNotContainGroups)
        ) {
          params.mustNotContainGroups = params.mustNotContainGroups
            .map((g) => g.value.trim())
            .filter(Boolean);
        } else if (params.mustNotContain) {
          // 兼容旧版
          params.mustNotContainGroups = [params.mustNotContain.trim()].filter(
            Boolean
          );
          delete params.mustNotContain;
        }
        this.queryParams = {
          // 1. 保留那些不受筛选条件影响的、由其他组件控制的核心参数
          pageSize: this.queryParams.pageSize,
          mediaTypes: this.queryParams.mediaTypes, // 由顶部导航控制
          isAsc: this.queryParams.isAsc, // 由排序组件控制
          orderByColumn: this.queryParams.orderByColumn, // 由排序组件控制
          // 2. 应用从标签解析出来的所有筛选参数
          ...params,
          // 3. 确保翻页和搜索等状态被重置或设置
          pageNum: 1,
          searchText: this.queryParams.searchText, // 保留搜索词
          searchFields: this.queryParams.searchFields,
        };
        this.getContentList();
      } catch (e) {
        this.$message.error("条件解析失败");
      }
    },
    handleCheckChange({ id, checked }) {
      if (checked) {
        if (!this.checkedList.includes(id)) {
          this.checkedList.push(id);
        }
      } else {
        this.checkedList = this.checkedList.filter((_id) => _id !== id);
      }
    },
    handleCheckAll(val) {
      if (val) {
        this.checkedList = this.contentList.map((item) => item.uniqueId);
      } else {
        this.checkedList = [];
      }
    },
    handleSortTypeChange(value) {
      const option = this.sortOptions.find((opt) => opt.value === value);
      if (option) {
        this.queryParams.orderByColumn = option.orderByColumn;
        this.queryParams.isAsc = option.isAsc;
        this.queryParams.pageNum = 1;
        this.getContentList();
      }
    },
    handleFilterReset(needGetContentList = false) {
      // 重置选中的标签
      //  const readStatusKeys = ['readStatus', 'followReadStatus', 'targetDetectReadStatus'];
      this.queryParams = {
        pageNum: 1,
        pageSize: 30,
        mediaTypes: [],
        isAsc: "desc",
        orderByColumn: "",
        enableSimilarityDedup: "fold",
      };
      if (this.$refs.topNavRef) {
        this.$refs.topNavRef.resetSelectedTag();
        this.$refs.topNavRef.resetActiveNav();
        this.$refs.topNavRef.handleExitSearchBar("no-emit");
      }
      if (needGetContentList) {
        this.getContentList();
      }
    },
    async handleResetAndRefresh() {
      this.handleFilterReset();
      await this.getContentList();
      await this.fetchTags();
    },
    async handleMarkRead() {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要标记为已读的内容");
        return;
      }
      try {
        const payload = {
          uniqueIds: this.checkedList,
          [this.readStatusKey]: 1,
        };
        await batchUpdatePublicSentiment(payload);
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return { ...item, moduleReadStatus: 1 };
          }
          return item;
        });
        this.$message.success("标记已读成功");
      } catch (e) {
        this.$message.error("标记已读失败");
      }
    },
    async handleChangeSentiment(value) {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要修改的内容");
        return;
      }
      try {
        await batchUpdateSentiment({
          uniqueIds: this.checkedList,
          sensitivity: value,
        });
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return {
              ...item,
              feature: {
                ...item.feature,
                sensitive: value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改倾向性成功");
      } catch (e) {
        this.$message.error("修改倾向性失败");
      }
    },
    async handleChangeAiSentimentType(value) {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要修改的内容");
        return;
      }
      try {
        await batchUpdateAiSentimentType({
          uniqueIds: this.checkedList,
          aiSentimentType: value,
        });
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return {
              ...item,
              dmxTagInfo: {
                ...item.dmxTagInfo,
                label: value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改AI舆情倾向性成功");
      } catch (e) {
        this.$message.error("修改AI舆情倾向性失败");
      }
    },
    async handleDelete() {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要删除的内容");
        return;
      }
      try {
        await this.$confirm("确定要删除吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        await batchDeletePublicSentiment({ uniqueIds: this.checkedList });
        this.contentList = this.contentList.filter(
          (item) => !this.checkedList.includes(item.uniqueId)
        );
        this.checkedList = [];
        this.$message.success("删除成功");
      } catch (e) {
        if (e !== "cancel") {
          this.$message.error("删除失败");
        }
      }
    },
    async handleSingleDelete(item) {
      try {
        await batchDeletePublicSentiment({ uniqueIds: [item.uniqueId] });
        this.contentList = this.contentList.filter(
          (i) => i.uniqueId !== item.uniqueId
        );
        if (this.checkedList.includes(item.uniqueId)) {
          this.checkedList = this.checkedList.filter(
            (id) => id !== item.uniqueId
          );
        }
        this.$message.success("删除成功");
      } catch (e) {
        this.$message.error("删除失败");
      }
    },
    async handleChangeSingleSentiment({ id, value }) {
      try {
        await batchUpdateSentiment({
          uniqueIds: [id],
          sensitivity: value.sentiment,
        });
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === id) {
            return {
              ...item,
              feature: {
                ...item.feature,
                sensitive: value.sentiment,
              },
            };
          }
          return item;
        });
        this.$message.success("修改倾向性成功");
      } catch (e) {
        this.$message.error("修改倾向性失败");
      }
    },
    async handleChangeSingleAiSentimentType({ id, value }) {
      try {
        await batchUpdateAiSentimentType({
          uniqueIds: [id],
          aiSentimentType: value.value,
        });
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === id) {
            return {
              ...item,
              dmxTagInfo: {
                ...item.dmxTagInfo,
                label: value.value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改AI舆情倾向性成功");
      } catch (e) {
        this.$message.error("修改AI舆情倾向性失败");
      }
    },
    async handleSingleMarkRead(uniqueId) {
      try {
        const payload = {
          uniqueIds: [uniqueId],
          [this.readStatusKey]: 1,
        };
        await batchUpdatePublicSentiment(payload);
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === uniqueId) {
            return { ...item, moduleReadStatus: 1 };
          }
          return item;
        });
      } catch (e) {
        this.$message.error("标记已读失败");
      }
    },
    handleSearch({ fields, text }) {
      this.queryParams = {
        ...this.queryParams,
        searchFields: fields,
        searchText: text,
        pageNum: 1,
      };
      this.$store.commit("yqmonitorMenu/SET_SEARCH_TEXT", text);
      this.getContentList();
    },
    handleClearSearch() {
      if ("searchFields" in this.queryParams) {
        delete this.queryParams.searchFields;
      }
      if ("searchText" in this.queryParams) {
        delete this.queryParams.searchText;
      }
      this.$store.commit("yqmonitorMenu/SET_SEARCH_TEXT", "");
      this.queryParams.pageNum = 1;
      this.getContentList();
    },
    // 新增：加载用户标签偏好
    loadUserTagPreferences() {
      if (!this.currentNode || !this.currentNode.id) return [];
      const key = `userTagPreferences_${this.userId}_${this.currentNode.id}`;
      const prefs = localStorage.getItem(key);
      try {
        return prefs ? JSON.parse(prefs) : [];
      } catch (e) {
        console.error("Error parsing tag preferences from localStorage", e);
        return [];
      }
    },
    saveUserTagPreferences(tagsToSave) {
      if (!this.currentNode || !this.currentNode.id) return;
      const key = `userTagPreferences_${this.userId}_${this.currentNode.id}`;
      const preferencesToStore = tagsToSave.map((tag) => ({
        id: tag.id,
        isPinned: tag.isPinned,
        order: tag.order,
      }));
      localStorage.setItem(key, JSON.stringify(preferencesToStore));
      console.log("Saved tag preferences:", preferencesToStore);
    },
    handleTagsOrderChanged(newOrderedTags) {
      this.localFilterTags = [...newOrderedTags];
      this.saveUserTagPreferences(newOrderedTags);
      this.$message.success("标签配置已保存");
    },
    async handleExpandSimilarArticles({
      similarGroupId,
      representativeArticle,
    }) {
      if (!similarGroupId) return;
      this.currentSimilarGroupId = similarGroupId;
      this.currentRepresentativeArticle = representativeArticle;
      this.similarDialogVisible = true;
      this.currentRepresentativeArticleTitle =
        representativeArticle?.titleObj?.title || "相似文章列表";
      await this.loadSimilarArticles({
        pageNum: 1,
        pageSize: 10,
      });
    },
    async loadSimilarArticles({ pageNum = 1, pageSize = 10 }) {
      if (!this.currentSimilarGroupId || !this.currentRepresentativeArticle) {
        return;
      }
      this.similarArticlesLoading = true;
      this.similarArticlesList = [];
      try {
        const response = await getSimilarGroupListApi({
          similarGroupId: this.currentSimilarGroupId,
          uniqueId: this.currentRepresentativeArticle.uniqueId,
          pageNum,
          pageSize,
        });
        console.log("response:", response);
        this.similarArticlesList = response.rows || [];
        this.similarArticlesTotal = response.total || 0;
      } catch (error) {
        console.error("获取相似文章失败:", error);
        this.$message.error("获取相似文章列表失败");
        this.similarArticlesList = [];
        this.similarArticlesTotal = 0;
      } finally {
        this.similarArticlesLoading = false;
      }
    },
    async handleSimilarArticlesPageChange({ pageNum, pageSize }) {
      await this.loadSimilarArticles({ pageNum, pageSize });
    },
  },
};
</script>
<style lang="scss" scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .card-box {
    margin: 20px 20px 0;
    background: white;
    border-radius: 2px 2px 0 0;
    height: calc(100vh - 70px);
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .fixed-header {
    border-bottom: 1px solid #ebeef5;
    padding: 0 20px;
    flex-shrink: 0;
  }
  .scrollable-content {
    overflow-y: auto;
    padding: 20px;
    flex: 1;
    padding-bottom: 90px;
    position: relative;
  }
  .fixed-action-bar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16px 20px;
    background: #fff;
    display: flex;
    align-items: center;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    gap: 10px;
  }
}
</style>
```
