<template>
  <div class="scroll-buttons">
    <el-button circle size="mini" icon="el-icon-top" title="回到顶部" @click="scrollToTop" />
    <el-button circle size="mini" icon="el-icon-bottom" title="回到底部" style="margin-left:0" @click="scrollToBottom" />
  </div>
</template>

<script>
export default {
  name: 'ScrollButtons',
  props: {
    scrollContainer: {
      type: null, // 允许任何类型
      required: true
    }
  },
  data() {
    return {
      showButtons: false
    }
  },
  methods: {
    scrollToTop() {
      this.scrollContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    scrollToBottom() {
      this.scrollContainer.scrollTo({
        top: this.scrollContainer.scrollHeight,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-buttons {
  position: fixed;
  bottom: 130px;
  right: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 999;

  .el-button {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    opacity: 0.6;
    transition: all 0.3s ease;

    &:hover {
      opacity: 1;
      transform: scale(1.2);
    }
  }
}
</style>
