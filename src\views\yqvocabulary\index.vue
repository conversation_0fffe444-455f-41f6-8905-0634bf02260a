<template>
  <div class="app-container">
    <el-card>
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane v-for="item in categoryList" :key="item.categoryCode" :label="item.categoryName"
          :name="item.categoryCode">
          <span slot="label"><i :class="getTabIcon(item.categoryCode)"></i> {{ item.categoryName }}</span>
        </el-tab-pane>
      </el-tabs>

      <!-- 动态组件渲染 -->
      <keep-alive>
        <component :is="currentComponent" v-if="activeCategory" :key="activeCategory.categoryCode"
          :category-info="activeCategory" />
      </keep-alive>
    </el-card>
  </div>
</template>

<script>
import { getDictionaryCategoryOptions } from '@/api/yqvocabulary';
import WordPanel from './components/WordPanel.vue';
import ExclusionPanel from './components/ExclusionPanel.vue';

export default {
  name: 'YqVocabulary',
  components: { WordPanel, ExclusionPanel },
  data() {
    return {
      activeTab: '', // 当前激活的tab的categoryCode
      categoryList: [], // 从API获取的词库分类列表
      categoryMap: {}, // key为categoryCode，value为分类对象的映射
    };
  },
  computed: {
    // 根据当前激活的tab，决定渲染哪个组件
    currentComponent() {
      if (!this.activeTab) return null;
      if (this.activeTab === 'exclude_word') {
        return 'ExclusionPanel';
      }
      // 明星和名企词库共用一个组件
      return 'WordPanel';
    },
    // 当前激活的分类信息对象
    activeCategory() {
      return this.categoryMap[this.activeTab] || null;
    }
  },
  created() {
    this.fetchCategories();
  },
  methods: {
    // 获取所有词库分类
    fetchCategories() {
      getDictionaryCategoryOptions().then(response => {
        if (response.code === 200 && response.data.length > 0) {
          this.categoryList = response.data;
          // 构建映射并设置默认激活的tab
          this.categoryList.forEach(item => {
            this.$set(this.categoryMap, item.categoryCode, item);
          });
          this.activeTab = this.categoryList[0].categoryCode;
        }
      });
    },
    // tab点击事件（el-tabs的v-model已处理大部分逻辑，此方法可留空或用于特殊处理）
    handleTabClick(tab) {
      // this.activeTab已由v-model自动更新
    },
    // 根据code返回不同icon
    getTabIcon(code) {
      const icons = {
        'famous_company': 'el-icon-office-building',
        'celebrity': 'el-icon-star-on',
        'exclude_word': 'el-icon-remove-outline'
      };
      return icons[code] || 'el-icon-document';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #fff;
}
</style>