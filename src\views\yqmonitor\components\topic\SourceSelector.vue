<template>
  <div>
    <!-- 主显示区域：标签 + 添加按钮 -->
    <div class="source-selector-container">
      <div class="source-tags-wrapper">
        <el-tag
          v-for="item in value"
          :key="item.uniqueId"
          class="source-tag"
          closable
          @close="removeSelectedItem(item)"
        >
          {{ item.name }}
          <i
            v-if="item.type === 'group'"
            class="el-icon-view tag-action-icon"
            title="查看明细"
            @click.stop="showSourceGroupDetails(item.sourceData)"
          />
        </el-tag>
        <div v-if="!value || value.length === 0" class="placeholder-text">
          {{ placeholder }}
        </div>
      </div>
      <el-button icon="el-icon-plus" @click="openSourceSelector">{{
        buttonText
      }}</el-button>
    </div>

    <!-- 信源选择器弹窗 -->
    <el-drawer
      :title="selector.title"
      :visible.sync="selector.visible"
      direction="rtl"
      size="60%"
      append-to-body
    >
      <div class="selector-drawer-content">
        <el-tabs v-model="selector.activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="选择信源组" name="groups">
            <el-table
              ref="groupTable"
              :data="sourceGroups"
              style="width: 100%"
              height="calc(100vh - 200px)"
              @selection-change="handleSelectorGroupSelection"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="label" label="信源组名称" />
              <el-table-column prop="count" label="信源数" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="搜索单个信源" name="sources">
            <div class="search-form-container">
              <el-form :model="selector.searchForm" inline>
                <el-form-item label="关键词">
                  <el-input
                    v-model="selector.searchForm.keyword"
                    placeholder="搜索昵称"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="用户ID">
                  <el-input
                    v-model="selector.searchForm.user_id"
                    placeholder="搜索用户ID"
                    clearable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="searchSingleSource"
                    >搜索</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
            <el-table
              ref="sourceTable"
              v-loading="selector.sourceLoading"
              :data="selector.sourceList"
              style="width: 100%"
              height="calc(100vh - 250px)"
              @selection-change="handleSelectorSourceSelection"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="nickname" label="账号昵称" />
              <el-table-column prop="user_id" label="用户ID" />
              <el-table-column prop="platform_name" label="平台" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="drawer-footer">
        <el-button @click="selector.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmSourceSelection"
          >确定</el-button
        >
      </div>
    </el-drawer>

    <!-- 信源组明细弹窗 -->
    <el-dialog
      title="信源组明细"
      :visible.sync="details.visible"
      width="50%"
      append-to-body
    >
      <el-table
        v-loading="details.loading"
        :data="details.list"
        border
        height="400px"
      >
        <el-table-column prop="nickname" label="账号昵称" />
        <el-table-column prop="user_id" label="用户ID" />
        <el-table-column prop="platform_name" label="平台" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button type="text" class="danger-text" @click="handleDeleteDetail(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        :total="details.total"
        :page="details.currentPage"
        :limit="details.pageSize"
        @pagination="handleDetailsPaginationChange"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="details.visible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchUser, directDataList } from "@/api/yqsignal";
import { batchDeleteDetail } from "@/api/yqsignal";
import Pagination from "@/components/Pagination/index.vue";

export default {
  name: "SourceSelector",
  props: {
    // 使用 v-model 绑定已选中的项
    value: {
      type: Array,
      default: () => [],
    },
    // 从父组件传入所有可选的信源组
    sourceGroups: {
      type: Array,
      default: () => [],
    },
    // 自定义按钮文字和标题
    buttonText: {
      type: String,
      default: "添加信源",
    },
    title: {
      type: String,
      default: "添加信源",
    },
    placeholder: {
      type: String,
      default: "请点击右侧按钮添加",
    },
  },
  components: {
    Pagination,
  },
  data() {
    return {
      // 信源选择器弹窗状态
      selector: {
        visible: false,
        title: "",
        activeTab: "groups",
        searchForm: { keyword: "", user_id: "" },
        sourceList: [],
        sourceLoading: false,
        selectedGroups: [],
        selectedSources: [],
      },
      // 信源组明细弹窗状态
      details: {
        visible: false,
        loading: false,
        currentPage: 1,
        pageSize: 10, // 默认每页10条
        total: 0, // 总条数
        list: [],
      },
      // 当前查看的信源组数据
      currentGroupData: null,
    };
  },
  methods: {
    // --- 信源选择器核心方法 ---
    openSourceSelector() {
      this.selector.title = this.title;
      this.selector.searchForm = { keyword: "", user_id: "" };
      this.selector.sourceList = [];
      this.selector.selectedGroups = [];
      this.selector.selectedSources = [];
      this.selector.visible = true;
      this.selector.activeTab = "groups";
      
      // 清除表格选中状态
      this.$nextTick(() => {
        if (this.$refs.groupTable) {
          this.$refs.groupTable.clearSelection();
        }
        if (this.$refs.sourceTable) {
          this.$refs.sourceTable.clearSelection();
        }
      });
    },
    async searchSingleSource() {
      this.selector.sourceLoading = true;
      try {
        const res = await searchUser({
          ...this.selector.searchForm,
          page: 1,
          page_size: 100,
        });
        this.selector.sourceList = res.data.list || [];
      } catch (e) {
        console.error("搜索单个信源失败", e);
      } finally {
        this.selector.sourceLoading = false;
      }
    },
    handleSelectorGroupSelection(selection) {
      this.selector.selectedGroups = selection;
    },
    handleSelectorSourceSelection(selection) {
      this.selector.selectedSources = selection;
    },
    handleTabClick(tab) {
      // 切换标签页时清除选中状态
      this.selector.selectedGroups = [];
      this.selector.selectedSources = [];
      this.$nextTick(() => {
        if (this.$refs.groupTable) {
          this.$refs.groupTable.clearSelection();
        }
        if (this.$refs.sourceTable) {
          this.$refs.sourceTable.clearSelection();
        }
      });
    },
    confirmSourceSelection() {
      const existingIds = new Set(this.value.map((item) => item.uniqueId));

      const groupsToAdd = this.selector.selectedGroups
        .map((group) => ({
          uniqueId: `group_${group.value}`,
          name: group.label,
          type: "group",
          sourceData: { direct_id: group.value, direct_name: group.label },
        }))
        .filter((item) => !existingIds.has(item.uniqueId));

      const sourcesToAdd = this.selector.selectedSources
        .map((source) => ({
          uniqueId: `source_${source.platform}_${source.user_id}`,
          name: `${source.nickname} (${source.platform_name})`,
          type: "source",
          sourceData: source,
        }))
        .filter((item) => !existingIds.has(item.uniqueId));

      // 通过 input 事件更新 v-model
      this.$emit("input", [...this.value, ...groupsToAdd, ...sourcesToAdd]);
      this.selector.visible = false;
    },
    removeSelectedItem(itemToRemove) {
      const newList = this.value.filter(
        (item) => item.uniqueId !== itemToRemove.uniqueId
      );
      this.$emit("input", newList);
    },

    // --- 信源组明细 ---
    async showSourceGroupDetails(groupData = this.currentGroupData) {
      this.details.visible = true;
      this.details.loading = true;
      try {
        const res = await directDataList({
          direct_id: groupData.direct_id,
          page: this.details.currentPage,
          page_size: this.details.pageSize,
        });
        this.details.list = res.data.list || [];
        this.details.total = res.data.total || 0; // 保存总条数
        this.currentGroupData = groupData;
      } catch (e) {
        console.error("获取信源组明细失败", e);
      } finally {
        this.details.loading = false;
      }
    },
    handleDetailsPaginationChange(params) {
      this.details.currentPage = params.page;
      this.details.pageSize = params.limit;
      this.showSourceGroupDetails();
    },
    // 删除信源明细
    async handleDeleteDetail(row) {
      try {
        await this.$confirm('确认删除该信源吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const params = {
          direct_id: this.currentGroupData.direct_id,
          direct_detail_id: [row.id]
        };

        await batchDeleteDetail(params);
        this.$message.success('删除成功!');
        // 重新获取当前页数据
        this.showSourceGroupDetails();
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除');
        } else {
          console.error('删除信源失败:', error);
          this.$message.error('删除信源失败');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.source-selector-container {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.source-tags-wrapper {
  flex-grow: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 36px;
  /* 与 el-input 高度一致 */
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-right: 10px;
  box-sizing: border-box;
}

.placeholder-text {
  line-height: 24px;
  /* (36px - 2*5px padding - 2*1px border) */
  color: #c0c4cc;
  padding-left: 10px;
}

.source-tag {
  display: flex;
  align-items: center;
}

.tag-action-icon {
  margin-left: 5px;
  cursor: pointer;
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

.selector-drawer-content {
  padding: 0 20px;
  height: calc(100vh - 120px);
  overflow-y: hidden;
}

.search-form-container {
  padding: 10px 0;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  box-sizing: border-box;
}

.dialog-footer {
  text-align: right;
}

.danger-text {
  color: #F56C6C;
}
</style>
