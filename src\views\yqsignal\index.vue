<template>
  <div v-loading="loading" class="app-container">
    <el-button type="primary" style="margin-bottom: 20px;" @click="openDialog(false)">新建信源组</el-button>
    <el-input v-model="search" placeholder="信源组名" style="width: 120px; margin-bottom: 20px; float: right;" clearable />
    <el-table :data="filteredGroups" border style="width: 100%; clear: both;">
      <el-table-column prop="direct_id" label="信源组ID" />
      <el-table-column prop="direct_name" label="信源组名称" />
      <el-table-column prop="direct_detail_count" label="信源数" />
      <el-table-column prop="updated_at" label="更新时间" />
      <el-table-column prop="created_at" label="创建时间" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-link type="primary" style="margin-right: 10px;" @click="openDialog(true, scope.row)">编辑</el-link>
          <el-link type="primary" style="margin-right: 10px;" @click="handleDetail(scope.row)">明细</el-link>
          <el-link type="danger" style="margin-right: 10px;" @click="handleDelete(scope.row)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px; text-align: right;">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="10"
        :current-page="1"
      />
    </div>
    <SignalGroupForm
      ref="signalGroupForm"
      :visible.sync="formDrawerVisible"
      :is-edit="formIsEdit"
      :edit-data="formEditData"
      @success="handleFormSubmit"
    />
  </div>
</template>

<script>
import SignalGroupForm from './SignalGroupForm.vue'
import { directList, deleteDirect, createDirect, updateDirect } from '@/api/yqsignal'

export default {
  name: 'SignalGroupList',
  components: { SignalGroupForm },
  data() {
    return {
      search: '',
      groups: [],
      formDrawerVisible: false,
      formIsEdit: false,
      formEditData: null,
      page: 1,
      pageSize: 10,
      total: 0,
      loading: false
    }
  },
  computed: {
    filteredGroups() {
      if (!this.search) return this.groups
      return this.groups.filter(g => g.direct_name.includes(this.search))
    }
  },
  created() {
    this.fetchGroups()
  },
  methods: {
    async fetchGroups() {
      this.loading = true
      try {
        const res = await directList({
          page: this.page,
          page_size: this.pageSize
        })
        if (res.code === 0) {
          this.groups = res.data.list
          this.total = res.data.total
        }
      } catch (error) {
        console.error('获取信源组列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    openDialog(isEdit, row) {
      this.formIsEdit = isEdit
      this.formDrawerVisible = true
      this.formEditData = isEdit ? {
        id: row.direct_id,
        name: row.direct_name
      } : null
    },
    async handleFormSubmit(formData) {
      try {
        if (this.formIsEdit && this.formEditData) {
          // 编辑
          const res = await updateDirect({
            direct_id: this.formEditData.id,
            direct_name: formData.name
          })
          if (res.code === 0) {
            this.$message.success('编辑成功')
            this.formDrawerVisible = false
            this.fetchGroups()
          }
        } else {
          // 新增
          const res = await createDirect({
            direct_name: formData.name
          })
          if (res.code === 0) {
            this.$message.success('创建成功')
            this.formDrawerVisible = false
            this.fetchGroups()
          }
        }
      } catch (error) {
        console.error('操作失败:', error)
      } finally {
        this.$refs.signalGroupForm.loading = false
      }
    },
    handleDetail(row) {
      this.$router.push({
        path: `/signal/group/detail/${row.direct_id}`,
        query: { name: row.direct_name }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该信源组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.loading = true
        try {
          const res = await deleteDirect({
            direct_id: row.direct_id
          })
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.fetchGroups()
          }
        } catch (error) {
          console.error('删除信源组失败:', error)
          this.loading = false
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
