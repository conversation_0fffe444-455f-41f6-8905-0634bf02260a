import Mock from 'mockjs'

Mock.mock(/\/getRouters/, 'get', () => {
  const data = [
    {
      name: 'System',
      path: '/system',
      hidden: false,
      redirect: 'noRedirect',
      component: 'Layout',
      alwaysShow: true,
      meta: {
        title: '系统管理',
        icon: 'system',
        noCache: false,
        link: null
      },
      children: [
        {
          name: 'User',
          path: 'user',
          hidden: false,
          component: 'system/user/index',
          meta: {
            title: '用户管理',
            noCache: false,
            link: null
          }
        }
      ]
    },
    {
      name: 'SignalSource',
      path: '/signal',
      hidden: false,
      redirect: 'noRedirect',
      component: 'Layout',
      alwaysShow: true,
      meta: {
        title: '信源管理',
        icon: 'online',
        noCache: false,
        link: null
      },
      children: [
        {
          name: 'SignalGroupList',
          path: 'group',
          hidden: false,
          component: 'yqsignal/index',
          meta: {
            title: '信源组列表',
            noCache: false,
            link: null
          }
        },
        {
          name: 'SignalGroupDetail',
          path: 'group/detail/:id(\\d+)',
          hidden: true,
          component: 'yqsignal/detail',
          meta: {
            title: '信源组明细',
            noCache: false,
            link: null
          }
        }
      ]
    }
  ]

  return {
    msg: '操作成功',
    code: 200,
    data: data
  }
})
