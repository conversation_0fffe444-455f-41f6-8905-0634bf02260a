# 系统默认首页设置的实现

## 现状
访问http://xxxx/时，默认会跳转 http://xxxx/yqmonitor/index

## 存在的问题
菜单是由权限控制的，`/yqmonitor/index` 这个路由不是每个角色都会有，这就导致了部分用户访问根路径的时候重定向到了404页面。

## /getRouters接口返回的数据格式举例与业务目标
### 接口返回的数据格式举例
```
{
    "msg": "操作成功",
    "code": 200,
    "data": [
        {
            "name": "Yqmonitor-business",
            "path": "/yqmonitor-business",
            "hidden": false,
            "redirect": "noRedirect",
            "component": "Layout",
            "alwaysShow": true,
            "meta": {
                "title": "本地监测",
                "icon": "#",
                "noCache": false,
                "link": null
            },
            "children": [
                {
                    "name": "Yqmonitor-business",
                    "path": "yqmonitor-business",
                    "hidden": false,
                    "redirect": "noRedirect",
                    "component": "ParentView",
                    "alwaysShow": true,
                    "meta": {
                        "title": "苏法",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    },
                    "children": [
                        {
                            "name": "Yqmonitor-business",
                            "path": "yqmonitor-business",
                            "hidden": false,
                            "component": "Layout",
                            "meta": {
                                "title": "无锡中院-地域",
                                "icon": "#",
                                "noCache": false,
                                "link": null
                            }
                        }
                    ]
                },
                {
                    "name": "Yqmonitor-business",
                    "path": "yqmonitor-business",
                    "hidden": false,
                    "component": "ParentView",
                    "meta": {
                        "title": "沪法词距对比",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    }
                }
            ]
        },
        {
            "name": "Yqmonitor",
            "path": "/yqmonitor",
            "hidden": false,
            "redirect": "noRedirect",
            "component": "Layout",
            "alwaysShow": true,
            "meta": {
                "title": "舆情监控",
                "icon": "monitor",
                "noCache": false,
                "link": null
            },
            "children": [
                {
                    "name": "Index",
                    "path": "index",
                    "hidden": false,
                    "component": "yqmonitor/index",
                    "meta": {
                        "title": "舆情监控",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    }
                },
                {
                    "name": "Detail/:uniqueId",
                    "path": "detail/:uniqueId",
                    "hidden": true,
                    "component": "yqmonitor/detail",
                    "meta": {
                        "title": "舆情详情",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    }
                }
            ]
        },
        {
            "name": "FollowedEvents",
            "path": "/followedEvents",
            "hidden": false,
            "redirect": "noRedirect",
            "component": "Layout",
            "alwaysShow": true,
            "meta": {
                "title": "关注事件",
                "icon": "eye",
                "noCache": false,
                "link": null
            },
            "children": [
                {
                    "name": "Index",
                    "path": "index",
                    "hidden": false,
                    "component": "yqmonitor/index",
                    "meta": {
                        "title": "关注事件",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    }
                }
            ]
        },
        {
            "name": "TargetMonitor",
            "path": "/targetMonitor",
            "hidden": false,
            "redirect": "noRedirect",
            "component": "Layout",
            "alwaysShow": true,
            "meta": {
                "title": "目标监测",
                "icon": "eye-open",
                "noCache": false,
                "link": null
            },
            "children": [
                {
                    "name": "Index",
                    "path": "index",
                    "hidden": false,
                    "component": "yqmonitor/index",
                    "meta": {
                        "title": "目标监测",
                        "icon": "#",
                        "noCache": false,
                        "link": null
                    }
                }
            ]
        }
    ]
}
```

### 业务目标
当前默认行为前面已经介绍过，访问根路径时重定向到`/yqmonitor/index`。但是这个行为并不适用于所有用户。
#### 期望的效果是：
默认路由地址(后面简称为：首页)从上面的response.data中根据`指定的规则`进行动态获取。
#### 指定的规则
1. 排除path为/yqmonitor-business这个路由对象得到一个新的数组 noYqmonitorBusinessRouters
2. noYqmonitorBusinessRouters的长度可以断言不会为0，从里面获取第一个路由对象的path作为首页
但是要注意：这个path是需要和children[0]的path拼接起来的，拼接的时候注意 这个children[0].hidden是不是为false，如果是就可以拼接，如果不是那么还需要查看children中的下一个对象的hidden属性，直到找到一个hidden为false的对象，进行拼接

## 修改计划
1. 在 src/store/modules/permission.js 中，添加 findFirstValidRoute 函数，用于根据指定规则动态计算用户的首页路径，并将结果保存在 Vuex 的 state 中。
2. 在 src/router/index.js 中，移除硬编码的 /yqmonitor/index 重定向。
3. 在 src/permission.js 的路由守卫中，增加对根路径 / 的判断。当用户访问根路径时，系统会自动重定向到先前计算出的动态首页地址。


## 草稿
```
 else if (to.path === '/') {
      const firstRoute = store.getters.firstRoute
      if (firstRoute) {
        next({ path: firstRoute, replace: true })
      } else {
        next()
      }
    }
```