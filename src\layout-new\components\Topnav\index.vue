<template>
  <div class="topnav">
    <logo-section />
    <menu-section />
    <user-section />
  </div>
</template>

<script>
import LogoSection from './components/LogoSection.vue'
import MenuSection from './components/MenuSection.vue'
import UserSection from './components/UserSection.vue'

export default {
  name: 'Topnav',
  components: {
    LogoSection,
    MenuSection,
    UserSection
  }
}
</script>

<style scoped lang="scss">
.topnav {
  display: flex;
  height: 50px;
  background-color: #fff;
  box-shadow: 0 -1px 9px 0 rgba(204, 204, 204, 0.3);
  color: #222;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 1260px;
  z-index: 301;
}
</style>
