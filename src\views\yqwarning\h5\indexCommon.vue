<template>
  <div class="public-opinion-detail">
    <div class="container">
      <!-- 信息详情 -->
      <SimpleInfoDetail :info-detail="infoDetail" :raw-content="rawContent" :sentiment-data="sentimentData" />
    </div>
  </div>
</template>

<script>
// 引入组件
import SimpleInfoDetail from './components/SimpleInfoDetail.vue'
import { getSmsMsgInfoByMsgId, getPublicSentimentById } from '@/api/h5'

export default {
  name: 'PablicOpinionDetailCommon',
  components: {
    SimpleInfoDetail
  },
  data() {
    return {
      infoDetail: {
        title: '',
        link: '',
        riskStamp: '',
        agency: '',
        time: '',
        source: '',
        analysis: '',
        summary: ''
      },
      rawContent: '',
      sentimentData: null
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 更新：方法重命名并实现接口调用和数据解析
    fetchDetailById(msgId) {
      getSmsMsgInfoByMsgId(msgId).then(response => {
        if (response.code === 0) {
          const data = response.data || {}
          const content = data.content || ''
          this.rawContent = content 

          // 更新解析逻辑，以覆盖所有需要的字段，包括链接
          const titleMatch = content.match(/标题：([^\n]+)/)
          const riskMatch = content.match(/风险等级：([^\n]+)/)
          const agencyMatch = content.match(/机构：([^\n]+)/)
          const timeMatch = content.match(/时间：([^\n]+)/)
          const sourceMatch = content.match(/来源：([^\n]+)/)
          const summaryMatch = content.match(/摘要：([\s\S]*?)(?=研判分析：|链接：|来源：|时间：|$)/)
          const analysisMatch = content.match(/研判分析：([^\n]+)/)
          const linkMatch = content.match(/详情页：([^\n]+)/)

          this.infoDetail = {
            title: titleMatch ? titleMatch[1].trim() : '无标题',
            riskStamp: riskMatch ? riskMatch[1].trim() : '',
            agency: agencyMatch ? agencyMatch[1].trim() : '',
            time: timeMatch ? timeMatch[1].trim() : '',
            source: sourceMatch ? sourceMatch[1].trim() : '',
            summary: summaryMatch ? summaryMatch[1].trim() : '',
            analysis: analysisMatch ? analysisMatch[1].trim() : '',
            link: linkMatch ? linkMatch[1].trim() : ''
          }
          // 如果链接存在，尝试提取舆情ID
          if (this.infoDetail.link) {
            const match = this.infoDetail.link.match(/yqmonitor\/detail\/([^/]+)/)
            if (match && match[1]) {
              const publicSentimentId = match[1]
              this.fetchPublicSentimentById(publicSentimentId)
            }
          }
        } else {
          this.$message.error(response.msg || '获取预警详情失败')
        }
      }).catch(error => {
        console.error('获取预警详情失败:', error)
        this.$message.error('获取预警详情失败')
      })
    },
    fetchPublicSentimentById(id) {
      getPublicSentimentById(id).then(response => {
        if (response.code === 200) {
          this.sentimentData = response.data || null
        } else {
          // 不提示错误，因为此接口为补充数据，失败不应影响主流程
          console.error('获取媒体信息失败:', response.msg)
        }
      }).catch(error => {
        console.error('获取媒体信息异常:', error)
      })
    },
    initData() {
      const msgId = this.$route.params.id
      this.fetchDetailById(msgId)
    }
  }
}
</script>

<style scoped lang="scss">
.public-opinion-detail {
  background-color: #f5f7fa;
  min-height: 100vh;
}
.container {
  margin: 0;
  background: white;
  border-radius: 0;
  padding: 20px;
  width: 100%;
}
</style>
