<template>
  <div class="similar-article-item">
    <div class="similar-item-header" @click="handleJumpSource">
      <span
        class="similar-item-title"
        :title="item.titleObj.rawTitle || item.titleObj.title"
        v-html="highlightSimilarHitWords(item.titleObj.title, false)"
      />
    </div>
    <div class="similar-item-content">
      <!-- 内容：优先显示 rawContent，如果没有则截断并用 popover 展示 -->
      <template v-if="item.contentObj.content">
        <el-popover
          v-if="item.contentObj.rawContent && item.contentObj.rawContent.length > 100"
          placement="top"
          trigger="click"
          :width="600"
          popper-class="content-popover"
        >
          <div v-html="highlightSimilarHitWords(item.contentObj.rawContent)" />
          <span slot="reference" v-html="highlightSimilarHitWords(item.contentObj.content, false)" />
        </el-popover>
        <div v-else v-html="highlightSimilarHitWords(item.contentObj.rawContent || item.contentObj.content, true)" />
      </template>
      <!-- OCR 识别内容 -->
      <div v-if="item.contentObj.ocr" class="ocr-block">
        <strong style="margin-right: 5px;">OCR:</strong>
        <el-popover placement="top" trigger="click" :width="600" popper-class="content-popover">
          <div v-html="highlightSimilarHitWords(item.contentObj.rawOcr)" />
          <span slot="reference" v-html="highlightSimilarHitWords(item.contentObj.ocr, false)" />
        </el-popover>
      </div>
      <!-- ASR 识别内容 -->
      <div v-if="item.contentObj.asr" class="asr-block">
        <strong style="margin-right: 5px;">ASR:</strong>
        <el-popover placement="top" trigger="click" :width="600" popper-class="content-popover">
          <div v-html="highlightSimilarHitWords(item.contentObj.rawAsr)" />
          <span slot="reference" v-html="highlightSimilarHitWords(item.contentObj.asr, false)" />
        </el-popover>
      </div>
    </div>
    <div class="similar-item-footer">
      <span class="platform-author">{{ item.source }} - {{ item.author }}</span>
      <span class="publish-time">发布: {{ item.publishTime }}</span>
    </div>
  </div>
</template>

<script>
import { getWyylHitWords, getAiFeatureHitWords, getInstitutionAliasHitWords } from './articleUtil' // Assuming these are still relevant for highlighting
import { mapGetters } from 'vuex'

export default {
  name: 'SimilarArticleItem',
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['searchText']) // 用于全局搜索词高亮
  },
  methods: {
    // 简化的高亮方法，或复用 Article.vue 中的高亮方法（如适用）
    highlightSimilarHitWords(text, isReplaceBr = true) {
      if (!text) return ''
      let wordsToHighlight = []

      // 从 item 的 hitWordsInfo 中收集高亮词（如有且相关）
      if (this.item.hitWordsInfo) {
        wordsToHighlight = wordsToHighlight.concat(getWyylHitWords(this.item.hitWordsInfo))
        const aiFeatures = getAiFeatureHitWords(this.item.hitWordsInfo)
        aiFeatures.forEach(feature => {
          if (Array.isArray(feature.value)) wordsToHighlight = wordsToHighlight.concat(feature.value)
        })
        wordsToHighlight = wordsToHighlight.concat(getInstitutionAliasHitWords(this.item.hitWordsInfo))
      }

      // 如有需要，添加全局搜索词
      if (this.searchText) {
        const searchWords = this.searchText.split(/\s+/).filter(word => word)
        wordsToHighlight.push(...searchWords)
      }

      wordsToHighlight = Array.from(new Set(wordsToHighlight)).filter(Boolean)
      wordsToHighlight.sort((a, b) => b.length - a.length)

      if (!wordsToHighlight.length) return text

      let html = text
      wordsToHighlight.forEach(word => {
        if (!word) return
        const reg = new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        html = html.replace(reg, `<span class="hitword-highlight">${word}</span>`)
      })
      if (isReplaceBr) {
        html = html.replace(/\n/g, '<br/>')
      }
      return html
    },
    handleJumpSource() {
      const selection = window.getSelection()
      if (selection.toString().length > 0) {
        return
      }
      if (this.item.url) {
        window.open(this.item.url, '_blank')
      } else {
        this.$message.warning('没有可跳转的原文链接')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.similar-article-item {
  padding: 10px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.similar-item-header {
  margin-bottom: 8px;
  .similar-item-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    cursor: default;
    cursor: pointer;
    &:hover {
      color: #409EFF; // 可选：如需悬浮高亮效果
    }
  }
}

.similar-item-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 8px;
  word-break: break-all;

  .ocr-block,
  .asr-block {
    margin-top: 5px;
    font-size: 13px;
    color: #888;
  }

  ::v-deep .hitword-highlight {
    // 确保高亮样式生效
    color: #e4393c;
    background: #fffbe6;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: bold;
  }
}

.similar-item-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;

  .platform-author {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.content-popover {
  /* 针对 el-popover 内容样式 */
  max-height: 400px;
  overflow-y: auto;

  ::v-deep .hitword-highlight {
    color: #e4393c;
    background: #fffbe6;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: bold;
  }
}
</style>
