# 预警信息发送 表单 输入框 预填充数据前进行 html实体编码解码处理
## 需求拆解
1. 有哪些 会被初始化 预填充 &&  预填充的内容可能被html实体编码过  的 输入框？
2. 需要一个工具函数，能够将html实体编码过的字符转换成未被编码过的字符串

## AI回答
1. 分析目标代码，有哪些会被初始化预填充 && 预填充的内容可能被html实体编码过的输入框？  
通过分析两个文件中的所有表单字段，发现可能需要解析HTML实体编码的有两类输入框：

   - 初始化从后端数据加载的字段：  
     `title`, `summary`, `source`, `link`  
     在 `loadWarningData` 函数中通过API加载的内容，可能被HTML转义保存

   - 通过左侧面板解析粘贴内容填充的字段：  
     `suspectedInstitution`, `title`, `summary`, `source`, `time`, `link`, `analysis`  
     在 `handlePasteAndParse` 函数中处理的解析逻辑，可能使用HTML转义过的文本

   **具体字段解析逻辑路径：**
1. `loadWarningData()`
   - API加载后解析的预警详情字段：  
     ```js
     this.form.title = item.titleObj.rawTitle || item.titleObj.title
     // ...其它字段
     ```
   - `rawTitle/title/content` 字段可能包含HTML转义符号 (`&lt;`, `&gt;`, `&amp;`, etc.)

2. `handlePasteAndParse()`
   - 用户粘贴的原始文本解析填充表单：  
     ```js
     const extractedData = {}
     lines.forEach((line) => {
         // 字段解析逻辑
     })
     // 字段填充逻辑
     this.form.source = extractedData.source
     this.form.title = extractedData.title
     ```

---
2. 提供HTML实体解码工具函数：

```javascript
// HTML实体解码工具函数
function decodeHtmlEntities(encodedString) {
    // 使用div内部text来自动解码所有HTML实体字符
    const div = document.createElement('div')
    div.innerHTML = encodedString
    return div.textContent
}
```

```javascript
// 示例用法
const decodedTitle = decodeHtmlEntities(this.form.title)
this.form.title = decodedTitle
```

💡 **解码原理：**
- 通过浏览器的HTML解析机制，使用`div.innerHTML=`赋值后自动将实体字符转换为原始文本
- 通过读取textContent可以得到**未转义**的文本值

---

**整体解决思路：**

1. **数据加载时解码：**
   在从后端加载数据后对需要解码的字段处理解码
   ```javascript
   await this.loadWarningData(query.warningId)
   this.form.title = decodeHtmlEntities(this.form.title)
   this.form.summary = decodeHtmlEntities(this.form.summary)
   this.form.source = decodeHtmlEntities(this.form.source)
   this.form.link = decodeHtmlEntities(this.form.link)
   ```

2. **粘贴解析时解码：**
   在解析粘贴的文本数据后对每个字段处理解码
   ```javascript
   Object.keys(extractedData).forEach((key) => {
       if (this.form.hasOwnProperty(key)) {
           // 解码 HTML 实体字符
           this.form[key] = decodeHtmlEntities(extractedData[key])
       }
   })
   ```

3. **表单初始化时解码：**  
   在创建/重设表单时对可能含有HTML实体的字段预先处理，防止回显时出现转义符号

通过以上方法，可以确保界面展示时所有可能被HTML实体编码的文本都已被正确解码成原始文本，解决预警信息显示安全性和可读性问题。