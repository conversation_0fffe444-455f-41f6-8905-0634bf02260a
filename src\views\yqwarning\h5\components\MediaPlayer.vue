<template>
  <div>
    <div
      v-show="visible"
      class="media-drawer-overlay"
      @click="handleClose"
    />
    <div class="media-drawer" :class="{ 'media-drawer-open': visible }">
      <div class="media-drawer-header">
        <div class="drawer-title">{{ playerTitle }}</div>
        <button class="close-btn" @click="handleClose">
          <i class="el-icon-close" />
        </button>
      </div>

      <div class="media-drawer-content">
        <!-- 视频播放器 -->
        <video
          v-if="mediaType === 'video' && videoSrc"
          ref="videoPlayer"
          :src="videoSrc"
          controls
          autoplay
          class="media-element"
        />

        <!-- 图文轮播 -->
        <el-carousel
          v-if="mediaType === 'image' && imageSources.length"
          arrow="always"
          class="media-element"
          :interval="5000"
          indicator-position="outside"
        >
          <el-carousel-item v-for="(img, index) in imageSources" :key="index">
            <img :src="img" alt="Image content" class="carousel-image">
          </el-carousel-item>
        </el-carousel>

        <!-- 背景音乐播放器 (隐藏) -->
        <audio v-if="mediaType === 'image' && audioSrc" ref="audioPlayer" :src="audioSrc" loop />
      </div>

      <!-- OCR 文本展示 -->
      <div v-if="ocrText" class="ocr-container">
        <p class="ocr-text"><strong>ocr:</strong> {{ ocrText }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MediaPlayer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mediaType: { // 'video' 或 'image'
      type: String,
      required: true
    },
    videoSrc: {
      type: String,
      default: ''
    },
    imageSources: {
      type: Array,
      default: () => []
    },
    audioSrc: {
      type: String,
      default: ''
    },
    ocrText: {
      type: String,
      default: ''
    }
  },
  computed: {
    playerTitle() {
      return this.mediaType === 'video' ? '视频播放' : '图文浏览'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        document.body.style.overflow = 'hidden'
        this.$nextTick(() => {
          if (this.mediaType === 'image' && this.audioSrc && this.$refs.audioPlayer) {
            this.$refs.audioPlayer.play().catch(e => console.error('音频自动播放失败', e))
          }
          if (this.mediaType === 'video' && this.$refs.videoPlayer) {
            this.$refs.videoPlayer.play().catch(e => console.error('视频自动播放失败', e))
          }
        })
      } else {
        document.body.style.overflow = 'auto'
        this.stopAllPlayers()
      }
    }
  },
  beforeDestroy() {
    document.body.style.overflow = 'auto'
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    stopAllPlayers() {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
        this.$refs.videoPlayer.currentTime = 0
      }
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.pause()
        this.$refs.audioPlayer.currentTime = 0
      }
    }
  }
}
</script>

  <style scoped lang="scss">
  .media-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 2000;
  }

  .media-drawer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #1c1c1c;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.25);
    z-index: 2001;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 100vh;
    display: flex;
    flex-direction: column;

    &.media-drawer-open {
      transform: translateY(0);
    }
  }

  .media-drawer-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #333;
    background: #2a2a2a;

    .drawer-title {
      font-size: 16px;
      font-weight: 500;
      color: #f0f0f0;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      color: #ccc;
      cursor: pointer;
    }
  }

  .media-drawer-content {
    flex-grow: 1;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    min-height: 250px;

    .media-element {
      width: 100%;
      height: 100%;
      max-width: 100%;
      max-height: 65vh; /* 限制媒体内容最大高度 */
      object-fit: contain;
    }

    .carousel-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    // Element UI Carousel style overrides
    ::v-deep .el-carousel__container {
      height: 90%;
    }
    ::v-deep .el-carousel__arrow {
      background-color: rgba(31, 45, 61, 0.5);
      &:hover {
        background-color: rgba(31, 45, 61, 0.8);
      }
    }
    ::v-deep .el-carousel__indicators--outside {
      margin-top: 10px;
    }
  }

  .ocr-container {
    flex-shrink: 0;
    width: 100%;
    padding: 10px 16px;
    background-color: #1a1a1a;
    color: #e0e0e0;
    font-size: 14px;
    line-height: 1.6;
    max-height: 30vh;
    overflow-y: auto;
    box-sizing: border-box;
    border-top: 1px solid #333;

    .ocr-text {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
  </style>
