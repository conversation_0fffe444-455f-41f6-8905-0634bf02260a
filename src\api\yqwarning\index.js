import request from '@/utils/request'

/**
 * 获取预警发送记录列表
 * @param {Object} query - 查询参数
 * @param {string} [query.startTime] - 发送开始时间 (yyyy-MM-dd HH:mm:ss)
 * @param {string} [query.endTime] - 发送结束时间 (yyyy-MM-dd HH:mm:ss)
 * @param {string} [query.institutionQuery] - 机构名称模糊查询
 * @param {Array<string>} [query.institutionCodes] - 机构编码列表
 * @param {Array<string>} [query.riskLevels] - 风险等级列表 (例如: ["1", "2", "3"])
 * @param {Array<string>} [query.receiveTypes] - 接收途径类型列表
 * @param {string} [query.receiverQuery] - 接收人姓名模糊查询
 * @param {Array<number>} [query.receiverIds] - 接收人ID列表
 * @param {string} [query.senderQuery] - 发送人姓名模糊查询
 * @param {Array<number>} [query.senderIds] - 发送人ID列表
 * @param {Array<string>} [query.contentTypes] - 内容类型列表
 * @param {string} [query.contentQuery] - 内容模糊查询
 * @param {string} [query.warnType] - 发送预警方式：1全部；2短信；3企业微信
 * @param {number} query.pageNum - 页码
 * @param {number} query.pageSize - 每页数量
 * @returns {Promise}
 */
export function getWarnMsgList(query) {
  return request({
    url: '/business/smsMsg/getWarnMsgList',
    method: 'get',
    params: query
  })
}

/**
 * 获取预警信息详情(H5)
 * @param {Object} params - 请求参数
 * @param {string} params.postUniqueId - 预警信息ID
 * @returns {Promise} 返回content字段内容
 */
export function getWarnMsgDetail(params) {
  return request({
    url: '/business/smsMsg/getSmsMsgInfoByPostUniqueId',
    method: 'post',
    data: params
  })
}

/**
 * 查询不同账号，similarId相同且为相似主数据的数据，再按热度排序。
 * @param {Object} data - 请求体
 * @param {string} data.uniqueId
 * @param {string} data.similarId
 * @returns {Promise}
 */
export function getTopUsersBySimilarId(data) {
  return request({
    url: '/yqms/monitor/getTopUsersBySimilarId',
    method: 'post',
    data: data
  })
}

/**
 * 导入预警信息
 * @param {FormData} data - The FormData object containing the file.
 * @returns {Promise}
 */
export function importSmsMsg(data) {
  return request({
    url: '/business/smsMsg/import',
    method: 'post',
    data: data
  })
}

/**
 * 获取预警信息接收人详情列表
 * @param {Object} query - 查询参数
 * @param {string} query.warnMsgId - 预警信息ID
 * @param {number} query.pageNum - 页码
 * @param {number} query.pageSize - 每页数量
 * @returns {Promise}
 */
export function getSmsUserInfos(query) {
  return request({
    url: '/business/smsUser/getSmsUserInfos',
    method: 'get',
    params: query
  })
}

/**
 * 删除预警信息
 * @param {Object} data - 请求体
 * @param {Array<string>} data.msgIds - 要删除的预警id集合
 * @returns {Promise}
 */
export function deleteWarnMsg(data) {
  return request({
    url: '/business/smsMsg/remove',
    method: 'post',
    data
  })
}
